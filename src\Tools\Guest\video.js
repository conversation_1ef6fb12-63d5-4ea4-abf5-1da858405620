import React from 'react';
import '../../styles/video.css';
import Scene from "./Scene";
import Firebase from "../../config/Firebase";
import SceneControls from "./SceneControls.js";
import Projectloader from './Projectloader';
import { modLook } from '../helpers/modlook';
import { Resolvepath } from '../../components/Resolvepath';
import Info from "./Info"



class Video extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      floorplandata: false,
      pdfdata: "false",
      mapdata: "false",
      currentimageName: "",
      apiload: true,
      images: "",
      user_id: this.props.uid,
      data: '',
      init: true,
      ProjectSettings:false,
      loader: true,
      lock: false,
      redirect: false,
      bookurl: false,
      info: false,
      recordingstatus: false,
      currentnode: false,
      entityinfo:false,
      Projectinfo:false
    };

    this.bottom = React.createRef();
    this.loader = this.loader.bind(this);
    this.init = this.init.bind(this);
    this.SetImageDuration = this.SetImageDuration.bind(this);

    this.start = 0;
    this.analytics = [];
    var Prev_Rotation=""
    modLook({SendRotation:(Position)=>{
//       if(Prev_Rotation!=Position){
// Prev_Rotation=Position
      this.props.SendDatabyChannel({type:"GUEST_LOOK_AT_TO_HOST",position:Position})
      // }

    }});

  }



  getImageName = (id) => {
    for (var x in this.state.images) {
      if (id === x) {
        return (this.state.images[x].name)
      }
    }
  }



  componentDidMount() {

    this.init();

  }

  SetImageDuration(Snapshot) {
    return new Promise((resolve, reject) => {
this.setState({currentnode:false})

    })

  }

  init() {

    Firebase.database().ref("users/" + this.props.uid + "/Projects/" + this.props.pid + "/bookurl").once("value", bookurl => {
      if (bookurl.exists()) {
        this.setState({
          bookurl: bookurl.val()
        })
      }
    })





    var promise = new Promise((resolve, reject) => {





      this.setState({
        host: false
      })
      


      resolve("Promise resolved successfully");


    });


    promise.then(result => {
      this.props.SubscribeToCustom((msg) => {
        var data = JSON.parse(msg);


        if (data.actiontype === "floorplan") {

          this.setState({
            floorplandata: data.data
          })
        }
        if (data.actiontype === "pdf") {


          this.setState({
            pdfdata: data.data
          })
        }
        if (data.actiontype === "map") {

          this.setState({
            mapdata: data.data
          })

        }
        if (data.actiontype === "bookurl") {

          window.open(data.url, "_blank")

        }


        var player = document.getElementById('cam1');
        if (data.actiontype === "lock") {

          this.setState({
            lock: true
          })
          player.setAttribute('look-controls-enabled', false)
          player.removeAttribute('modlook-controls')

          player.setAttribute("animation","property: rotation; to: "+data.rotation.x +" "+data.rotation.y+" "+data.rotation.z+"; loop: false; dur: 500")
          this.setState({
            anirotation: data.rotation.x + " " + data.rotation.y + " " + data.rotation.z
          })
        }
        if (data.actiontype === "unlock") {
          this.setState({
            lock: false,
            info: false
          })
          player.setAttribute('modlook-controls', "")
          player.setAttribute('look-controls-enabled', true);
        }

        if (data.actiontype === "pdf_next") {
          let doc = document.getElementById('iframe').contentWindow;
          doc.document.getElementsByClassName('df-ui-next')[0].click();
        }

        if (data.actiontype === "pdf_prev") {
          let doc = document.getElementById('iframe').contentWindow;
          doc.document.getElementsByClassName('df-ui-prev')[0].click();
        }
        if (data.actiontype === "infocard") {
          if (data.data) {
            player.setAttribute('look-controls-enabled', false)
            player.removeAttribute('modlook-controls')

            player.setAttribute("rotation", data.rotation.x + " " + data.rotation.y + " " + data.rotation.z)
            this.setState({
              anirotation: data.rotation.x + " " + data.rotation.y + " " + data.rotation.z,
              entityinfo:{position:data.position,scale:data.scale,icon:data.icon}
            });

          }
          else {
            this.setState({
              entityinfo:false
            })
            player.setAttribute('modlook-controls', "")
            player.setAttribute('look-controls-enabled', true);
          }
          this.setState({
            info: data.data,
          })


        }
        if (data.actiontype == "annotate") {
          if (data.data) {
            document.getElementById("annotate-holder").innerHTML = `<a-entity look-at='#cam1' position="` + data.data.x + ` ` + data.data.y + ` ` + data.data.z + `" ><a-box  material="opacity:1;transparent:true;" color="transparent" rotation="180 180 180"  src="#Highlighter" scale="5 5 5" animation="property: scale; from:5 5 5; to:50 50 50;  dur: 2000; round:true; easing: linear; loop: true"  ></a-box></a-box></a-entity>`;
          }
          else {
            document.getElementById("annotate-holder").innerHTML = "";
          }


        }



      });




    });

  }
  shouldComponentUpdate(nextProps, nextState) {
    if(nextProps.pid!==this.props.pid){
      let end = new Date().getTime();
      let diffrence = (end - this.start) / 1000;
      this.SetImageDuration({
        Duration: diffrence,
        ImageId: this.state.currentnode.imageid,
        ImageName: this.state.currentnode.currentimageName,
        Project: this.props.pid,
        Uid: this.props.uid,
        roomId: this.props.roomId
      }).then(res=>{

      })
      return true
    }
    return true;
  }


  loader() {

    this.setState({
      loader: false,
      apiload: false
    })
  }










  render() {
    return (<>
      {this.state.info ? <Info info={this.state.info} /> : ""}

      {this.state.currentimageName.length &&  this.props.platform ? <div className="roomname">{(this.state.currentimageName).replaceAll(/_|-/g," ")}</div> : ""}

      {!this.state.init ? <Scene
        room={this.props.roomId}
        entityinfo={this.state.entityinfo}
        data={this.state.images}
        info={this.state.Projectinfo}
        loader={this.loader}
        ProjectSettings={this.state.ProjectSettings}
        currentnode={this.state.currentnode}
        loading={this.state.loader}
        platform={this.props.platform}
      /> : <></>}

      {this.state.apiload ? <></> : <>


        <div id="bottom" className="container" ref={this.bottom} >
          <SceneControls
            destruct={this.destruct}
            pid={this.props.pid}
            bookurl={this.state.bookurl}
            roomId={this.props.roomId}
            user_id={this.state.user_id}
            data={this.state.data}
            changeProject={this.changeProject}
            floorplandata={this.state.floorplandata}
            pdfdata={this.state.pdfdata}
            mapdata={this.state.mapdata}

          />
        </div>
      </>}


      {this.state.redirect &&
        <div className="modal" id="admit_modal" tabIndex={-1} role="dialog" style={{ display: 'block' }}>
          <div className="modal-dialog" role="document">
            <div className="modal-content">
              <div className="modal-header">
                <h5 style={{ color: '#222b45' }} className="modal-title">Permission Denied</h5>
              </div>
              <div className="modal-body">
                <p className="share_content">Permission to join the room was denied</p>
              </div>
              <div style={{ display: 'block', textAlign: 'end' }} className="modal-footer">
                <button type="button" onClick={() => { window.location = "https://propvr.in" }} className="btn cancel" >Cancel</button>
                <button style={{ marginLeft: '20px' }} type="button" className="btn proceed" onClick={() => { window.location.reload() }}>Retry</button>
              </div>
            </div>
          </div>
        </div>
      }











      {this.state.loader ?
        <Projectloader title={"You are joining the project!"} dis={this.state.loader} pid={this.props.pid} data={this.state.data} ></Projectloader>
        :
        <div className="roomname-lock">




          {this.state.lock ? <div className="guest-lock"><div> <svg height={24} width={24} viewBox="0 0 24 24">
            <g data-name="Layer 2">
              <g fill="#fff" data-name="lock">
                <rect width="24" height="24" opacity="0" />
                <path d="M17 8h-1V6.11a4 4 0 1 0-8 0V8H7a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3v-8a3 3 0 0 0-3-3zm-7-1.89A2.06 2.06 0 0 1 12 4a2.06 2.06 0 0 1 2 2.11V8h-4zM18 19a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1v-8a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1z" />
                <path d="M12 12a3 3 0 1 0 3 3 3 3 0 0 0-3-3zm0 4a1 1 0 1 1 1-1 1 1 0 0 1-1 1z" />
              </g>
            </g>
          </svg><div>Guide mode ON</div></div></div> : <></>}


        </div>

      }
      {this.state.recordingstatus ?
        <div className="record-display-div">
          <div className="circle"></div>
          <div className="REC">REC</div>
        </div> : ""}

    </>
    );

  }
}
export default Video;