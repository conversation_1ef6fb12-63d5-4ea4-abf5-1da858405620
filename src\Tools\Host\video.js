import React from 'react';
import { Redirect } from "react-router-dom";
import {connect} from "react-redux"
import '../../styles/video.css';
import Scene from "./Scene";
import Firebase from "../../config/Firebase";
import SceneControls from "./SceneControls.js";
import Switchprojectloader from './Switchprojectloader';
import ButtonClick from "../../assets/click.mp3";
import { modLook } from '../helpers/modlook';
import { Modalexception, Stickyexception } from "../../components/Exceptions.jsx";
import Info from "./Info"
import { Resolvepath } from '../../components/Resolvepath';
import InventoryInfo from './InventoryInfo';

class Video extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      current_image: false,
      currentimageName: "",
      apiload: true,
      images: {},
      user_id: false,
      data: false,
      init: true,
      loader: true,
      Switchstatus: false,
      lock: false,
      exception: false,
      warning: false,
      screenflag:false,
      ProjectSettings:false,
      hotspotIcon: {
        url: "https://firebasestorage.googleapis.com/v0/b/realvr-eb62c.appspot.com/o/assets%2Fhotspot.svg?alt=media&token=ccc646ce-5859-4c09-9440-230dfec48c4a",
        scale: "0.1 0.1",
        admin: false
      },
      info:false,
      inventoryinfo:false,
      annotate:false,
    };
    this.audioEl = null;

    this.slider_mover = React.createRef();
    this.audioctrl = React.createRef();
    this.localvideo = React.createRef();
    this.senddata = this.senddata.bind(this);
    this.loader = this.loader.bind(this);
    this.lock = this.lock.bind(this);
    this.init = this.init.bind(this);
    this.EnableAnnotate=this.EnableAnnotate.bind(this);
    this.closeinfo=this.closeinfo.bind(this);
    this.SetInfo=this.SetInfo.bind(this);
    // this.newuserid=false;
    modLook({Annotate:(position)=>{
if(this.state.annotate){
  const data = {
    actiontype: "annotate",

    data: position
  };
  this.props.SendCustomMessage(data,this.props.roomId)
  document.getElementById("annotate-holder").innerHTML=`<a-entity look-at='#cam1' position="`+position.x+` `+position.y+` `+position.z+`"><a-box  material="opacity:1;transparent:true;" color="transparent" rotation="180 180 180"  src="#Highlighter" scale="5 5 5" animation="property: scale; from:5 5 5; to:50 50 50;  dur: 2000; round:true; easing: linear; loop: true"  ></a-box></a-entity>`;


}


    },SendRotation:(Position)=>{
      //       if(Prev_Rotation!=Position){
      // Prev_Rotation=Position
            this.props.SendDatabyChannel({type:"GUEST_LOOK_AT_TO_HOST",position:Position})
            // }

          }
  });
  window.addEventListener("beforeunload", (ev) => {
    Firebase.database().ref("activeusers/" +this.props.Plan.Admin+ "/" + this.props.Auth.Auth.uid).once("value", check => {
            if (check.exists()) {
              Firebase.database().ref("activeusers/" + this.props.Plan.Admin + "/" + this.props.Auth.Auth.uid).set({
                active: true
              })
            }
          });
   });
  }

  switch = () => { this.setState({ Switchstatus: true }) }

  getImageName = (id) => {
    for (var x in this.state.images) {
      if (id === x) {
        return (this.state.images[x].name)
      }
    }
  }

  SetInfo(InfoType,InfoData,key,e,extra){
    this.setState({info:{...InfoData,InfoType,key:key,isMobile:e.isMobile}})
    var cameraEl =document.getElementById('cam1');
    const data = {
      actiontype: "infocard",
      lockmode: true,
      data:{...InfoData,key:key,InfoType},
      rotation:cameraEl.getAttribute("rotation"),
      ...extra
    };
    this.props.SendCustomMessage(data,this.props.roomId)
  }

  shouldComponentUpdate(nextProps,nextState){
      if(this.props.pid!=nextProps.pid){
        this.setState({
          init:true,
          apiload:true
        })
        setTimeout(() => {
          this.setState({init:false,
            apiload:false})
        }, 1000);
        this.init(nextProps.pid);
        return true
      }
      else{
        return true
      }
    }
  componentDidMount() {
    console.log("@@@@@@@@@@@@@@@@@@",this.props)

    this.init(this.props.pid);


  }

  async init(pid) {

    Firebase.database().ref("activeusers/" + this.props.Plan.Admin + "/" + this.props.Auth.Auth.uid).once("value", check => {
      if (check.exists()) {
        Firebase.database().ref("activeusers/" + this.props.Plan.Admin + "/" + this.props.Auth.Auth.uid).set({
          active: false
        })
      }
    });
    this.audioEl = document.getElementsByClassName("audio-element")[0]
    this.setState({
      pid: pid
    })



        Firebase.database().ref("users/" + this.props.Auth.Auth.uid + "/Projects/" + pid + "/roomactive/" + this.props.roomId).set({
          status: 'live'
        });
        Firebase.database().ref("users/" + this.props.Auth.Auth.uid + "/Projects/" + pid).once("value", async (node) => {
          this.state.data = node.val();
          if (node.val().projectsettings) {
            if (node.val().projectsettings.defaulthotspoticon) {
              if (node.val().projectsettings.defaulthotspotscale) {
                this.setState({
                  hotspotIcon: {
                    url: node.val().projectsettings.defaulthotspoticon,
                    scale: node.val().projectsettings.defaulthotspotscale + ' ' + node.val().projectsettings.defaulthotspotscale
                  }
                });
              } else {
                this.setState({
                  hotspotIcon: {
                    url: node.val().projectsettings.defaulthotspoticon,
                    scale: "0.1 0.1"
                  }
                });
              }
            }
          }
          for (var x in node.val().images) {

            
            this.setState({
              current_image: x,
              currentimageName: node.val().images[x].name,
              images: node.val().images,
              data: node.val(),
              user_id: this.props.Auth.Auth.uid,
              ProjectSettings:node.val().projectsettings,
              init: false,

            });
            if(window.vuplex){
              window.vuplex.postMessage({ action: 'scene.src.change', data: {src:encodeURIComponent(Resolvepath(node.val().images[x].url))} });
            }
            var thumbslider = setInterval(() => {
              if (document.getElementById(x + "_thumb")) {

                var a = document.querySelectorAll('.item_active');
                [].forEach.call(a, (el) => {
                  el.classList.remove("item_active");
                });
                document.getElementById(x + "_thumb").classList.add('item_active');
                clearInterval(thumbslider);
              }
            }, 1000);
            break;
          }

        }).then((value) => {




        })


  }






changeImage = (str) => {
 

    this.setState({
      current_image: str,
      currentimageName: this.state.images[str].name
    })
    if(window.vuplex){
      window.vuplex.postMessage({ action: 'scene.src.change', data: {src:encodeURIComponent(Resolvepath(this.state.images[str].url))} });
    }
    if (document.getElementById(str + "_thumb")) {
      var a = document.querySelectorAll('.item_active');
      [].forEach.call(a, function (el) {
        el.classList.remove("item_active");
      });
      document.getElementById(str + "_thumb").classList.add('item_active');
    }
  }

  change = (str) => {
    for (var key in this.state.images) {
      if (this.state.images[key].url === str) {

        this.changeImage(key)

      }
    }
  }
  changeProject = ( pid) => {
    this.setState({loader:true})
  }
  GetImageId = (str) => {
    for (var key in this.state.images) {
      if (this.state.images[key].url === str) {

        return {key:key,data:this.state.images[key]};

      }
    }
  }






senddata(data) {
    this.props.SendCustomMessage(data,this.props.roomId)
  }
loader() {
    this.setState({
      loader: false,
      apiload: false,
    })
  }
  ViewCta=(Link,authcheck)=> {
    var _D_Link = decodeURI(Link);
    if (_D_Link.includes("https://propvr.tech/embed")) {
      var _nodes = _D_Link.split("?cid=")
      var _Data = _nodes[1].split("&proid=");
      var _uid = _Data[0];
      var _project = _Data[1];
      if(authcheck){
        this.closeinfo();
        this.changeProject(Firebase.auth().currentUser.uid,_project)
      }else{
        if (_uid == Firebase.auth().currentUser.uid) {
        this.closeinfo();
        this.changeProject(Firebase.auth().currentUser.uid,_project)
      }
      else {
        window.open(Link)
      }
      }

    } else {
      window.open(Link)
    }
  }






  lock() {
    var cameraEl = document.getElementById('cam1');
    if (!this.state.lock) {

      const data = {
        actiontype: "lock",
        lockmode: true,
        rotation: cameraEl.getAttribute("rotation")
      };

      this.props.SendCustomMessage(data,this.props.roomId)
    }
    else {

      const data = {
        actiontype: "unlock",
        lockmode: false,
        rotation: cameraEl.getAttribute("rotation")
      };

      this.props.SendCustomMessage(data,this.props.roomId)
    }
    this.setState({
      lock: !this.state.lock
    })
  }






closeinfo(){
  if(this.state.info){
    const data = {
      actiontype: "infocard",
      lockmode: true,
      data: false
    };
    this.props.SendCustomMessage(data,this.props.roomId)
  }

this.setState({info:false})
}

EnableAnnotate(){
  if(this.state.annotate){
    const data = {
      actiontype: "annotate",

      data: false
    };
    document.getElementById("annotate-holder").innerHTML="";
    this.props.SendCustomMessage(data,this.props.roomId)
    this.setState({annotate:!this.state.annotate})
  }else{
    this.setState({annotate:!this.state.annotate})
  }
}
  render() {


      return (<>
        {this.state.exception ? <Modalexception title={this.state.exception.title}
          message={this.state.exception.message} /> : <></>}
        {this.state.info?<Info
    changeImage={this.changeImage}
          GetImageId={this.GetImageId}
          ViewCta={this.ViewCta}
          changeProject={this.changeProject} SendCustomMessage={this.props.SendCustomMessage} roomId={this.props.roomId} closeinfo={this.closeinfo} lock={this.state.lock} info={this.state.info}/>:""}
        {this.state.warning ?<Stickyexception onclick={()=>this.setState({
                warning:false
              })} message={this.state.warning.message}/>:<></>}
        <audio className="audio-element">
          <source src={ButtonClick}></source>
        </audio>


    {this.state.currentimageName.length && this.props.platform?  <div style={{display:'none'}} className="roomname">{(this.state.currentimageName).replaceAll(/_|-/g," ")}</div>:""}
        {!this.state.init && this.state.images ? <Scene
          room={this.props.roomId}
          SetInventoryInfo={this.SetInventoryInfo}
          project={this.props.pid}
          SetInfo={this.SetInfo}
          ProjectSettings={this.state.ProjectSettings}
          data={this.state.images}
          image={this.state.current_image}
          change={this.change}
          lock={this.state.lock}
          GetImageId={this.GetImageId}
          loader={this.loader}
          brand={this.state.data.Brandimage != undefined ? this.state.data.Brandimage : false}
          getImageName={this.getImageName}
          senddata={this.senddata}
          hotspotIcon={this.state.hotspotIcon}
          Inventory={this.state.data.inventory || {}}
          platform={this.props.platform}
        /> : <></>}
        {this.state.apiload  ? <></> : <>

          <div style={{position: 'fixed', top: '10px', right: '50%', transform: 'translateX(50%)'}}> {this.state.current_image?this.state.images[this.state.current_image].variantlinkId?<Variant CurrentImage={this.state.current_image}  changeImage={this.changeImage} Images={this.state.images} Variants={Object.keys(this.state.images).filter(Image=>{
  return this.state.images[Image].variantlinkId && this.state.images[Image].variantlinkId==this.state.images[this.state.current_image].variantlinkId
})}/>:"":""}</div>
  <SceneControls
  annotate={this.state.annotate}
  EnableAnnotate={this.EnableAnnotate}
    admin={this.state.admin}
    pid={this.state.pid}
    roompid={this.props.pid}
    loader={this.state.loader}
    roomId={this.props.roomId}
    user_id={this.state.user_id}
    lock={this.state.lock}
    setlock={this.lock}
    bookurl={this.state.data.Info != undefined ? this.state.data.Info.bookurl !== undefined ? this.state.data.Info.bookurl : false : false}
    data={this.state.data}
    changeImage={this.changeImage}
    changeProject={this.changeProject}
    senddata={this.senddata}
    switch={this.switch}
    slider_mover={this.slider_mover}
  />

{this.state.init ?
          <Switchprojectloader dis={this.state.loader} pid={this.state.pid} data={this.state.data} Switchstatus={this.state.Switchstatus}></Switchprojectloader>
          : <>

          </>}
</>}
 </>
      );
 }



}
const mapStateToProps = state => {
  return {
    Auth:state.Auth.auth,
    Plan:state.Auth.plan,
  }
}

// const mapDispatchToProps = {
//   ...HostActions,
// }

export default connect(mapStateToProps, null)(Video)

class Variant extends React.Component{

constructor(props){
  super(props);
}
  render(){
    return(
<>
{Object.values(this.props.Variants).map(Variant=>{
  return(
    <button style={{ background: this.props.CurrentImage==Variant?"#000":'#0000009c'}} className="Variation" onClick={()=>{this.props.changeImage(Variant)}}>{this.props.Images[Variant].varaintcustomname}</button>
  )
})}
</>
    )
  }
}