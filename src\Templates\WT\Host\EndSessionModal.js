import React from "react";
import { useNavigate } from "react-router-dom";

const EndSessionModal = ({  Close }) => {
  const navigate = useNavigate();

  const handleLeaveSession = () => {
    Close();
    navigate("/sessions");
    setTimeout(() => {
      window.location.reload();
    }, 100);
  };

  return (
    <div
      style={{ display: "block" }}
      className="bg-[#000000CC] fixed z-[10501] overflow-hidden w-full h-full inset-0 h-full w-full"
      id="close_modal"
      tabIndex="-1"
      role="dialog"
    >
      <div
        className="mx-auto py-4 max-w-[450px] sm:max-w-[500px] lg:max-w-[530px] flex justify-end items-center h-full w-full"
        role="document"
      >
        <div className="px-6 py-3 sm:py-4 shadow-[0_27px_24px_0_rgba(0,0,0,0.2),0_40px_77px_0_rgba(0,0,0,0.22)] h-fit flex flex-col w-full pointer-events-auto bg-[#FF44441A] backdrop-blur-lg bg-clip-padding m-auto rounded-md rounded-none border-[none] inset-0 relative">
          <div className="flex items-center pb-0 border-b-[none]">
            <svg
              className="w-6 h-6"
              viewBox="0 0 24 22"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10.2275 1.35666L0.274488 18.5945C0.0947018 18.9058 3.32746e-05 19.2589 8.77004e-09 19.6184C-3.3257e-05 19.9779 0.0945699 20.331 0.274298 20.6423C0.454027 20.9537 0.712546 21.2122 1.02387 21.3919C1.33519 21.5716 1.68834 21.6663 2.04782 21.6662H21.9522C22.3117 21.6663 22.6648 21.5716 22.9761 21.3919C23.2875 21.2122 23.546 20.9537 23.7257 20.6423C23.9054 20.331 24 19.9779 24 19.6184C24 19.2589 23.9053 18.9058 23.7255 18.5945L13.7736 1.35666C13.5939 1.04543 13.3354 0.786982 13.0242 0.607294C12.713 0.427606 12.3599 0.333008 12.0005 0.333008C11.6412 0.333008 11.2881 0.427606 10.9769 0.607294C10.6656 0.786982 10.4072 1.04543 10.2275 1.35666Z"
                fill="#FF4444"
              />
              <path
                d="M12.1279 6.67383H11.8683C11.2291 6.67383 10.7109 7.192 10.7109 7.83121V13.3633C10.7109 14.0025 11.2291 14.5207 11.8683 14.5207H12.1279C12.7671 14.5207 13.2853 14.0025 13.2853 13.3633V7.83121C13.2853 7.192 12.7671 6.67383 12.1279 6.67383Z"
                fill="white"
              />
              <path
                d="M11.9981 18.9084C12.709 18.9084 13.2853 18.3321 13.2853 17.6212C13.2853 16.9103 12.709 16.334 11.9981 16.334C11.2872 16.334 10.7109 16.9103 10.7109 17.6212C10.7109 18.3321 11.2872 18.9084 11.9981 18.9084Z"
                fill="white"
              />
            </svg>

            <h5
              style={{ color: "#222b45", fontFamily: "Open Sans" }}
              className="ml-1 mt-0 font-[bold] not-italic leading-[1.33] mb-0 tracking-[normal] text-sm sm:text-base lg:text-lg text-white font-bold"
            >
              You are about to leave the session
            </h5>
          </div>
          <div className="pt-2 pb-2 ">
            <p
              style={{ fontFamily: "Open Sans" }}
              className="text-xs lg:text-sm font-normal not-italic leading-[1.33] tracking-[normal] text-white mb-0 "
            >
              Are you sure you want to exit the project?
            </p>
          </div>
          <div className="block pt-0 pb-1 border-t-[none]">
            <div className="flex justify-end mt-2 gap-3">
              <button
                onClick={Close}
                type="button"
                className="w-fit h-12 border-2 border-solid border-[#FF4444] hover:border-[#fb5353] rounded bg-transparent m-0 px-3 sm:text-sm lg:text-base text-white font-semibold leading-6"
              >
                Cancel
              </button>
              <div className="flex">
              <button
                  onClick={handleLeaveSession}
                  type="button"
                  className=" w-fit h-12 rounded border bg-[#FF4444] hover:bg-[#fb5353] m-0 px-3 sm:text-sm lg:text-base border-0 text-white font-semibold leading-6"
                >
                  Leave Session
                </button>
                {/* <button
                  onClick={CloseSession}
                  type="button"
                  className="ml-3 w-fit h-12 border-2 border-solid border-[#FF4444] hover:border-[#fb5353] rounded bg-transparent m-0 px-3 sm:text-sm lg:text-base text-white font-semibold leading-6"
                >
                  End for all
                </button> */}
               
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EndSessionModal;