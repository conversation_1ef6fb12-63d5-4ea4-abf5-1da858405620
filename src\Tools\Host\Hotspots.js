import React from 'react';

export default class Hotspots extends React.Component {

    render()
    {
        return(
            <>
            {this.props.data[this.props.image].links!==undefined? this.links = Object.values(this.props.data[this.props.image].links).map((item) => 
            {
                
                var splitrot=item.position.split(" ");
                var x= parseFloat(splitrot[0]*120);
                var y= parseFloat(splitrot[1]*120);
                var z= parseFloat(splitrot[2]*120);
                
                let id = item['dest-image']+x+y+z;
                
                if(item.customicon)
                {      
                    var scalex = 10;
                    var scaley = 10;
                    
                    if(item.iconscale)
                    {
                        var spliticonscale=item.iconscale.split(" ");
                        scalex=parseFloat(spliticonscale[0]*120);
                        scaley=parseFloat(spliticonscale[1]*120);
                    }
                    // material={"transparent:true;opacity:0.8;"+item.customicon.includes(".gif")?"shader:gif;":"opacity:1;"}

                    return(
                        <a-image
                            id={id} 
                            key={id} 
                            src={item.customicon}
                            position={x+" "+y+" "+z} 
                            class="hotspot"
                            look-at='#cam1' 
                            material={"transparent:true;"+item.customicon.includes(".gif")?"shader:gif;":"opacity:1;"}
                            onClick={() => this.props.change(item["dest-image"])}
                            raycaster-listen
                            scale={scalex+" "+scaley}>
                        </a-image>
                    );
                }
                else{
                    var Hotspot_Scale = this.props.hotspotIcon.scale.split(" ");;
                    scalex=parseFloat(Hotspot_Scale[0]*120);
                    scaley=parseFloat(Hotspot_Scale[1]*120);
                
                    return(
                        <a-image
                            id={id} 
                            key={id} 
                            src="#hotspot"
                            position={x+" "+y+" "+z} 
                            scale={scalex+" "+scaley}
                            class="hotspot"
                            look-at='#cam1' 
                            onClick={() => this.props.change(item["dest-image"])}
                            raycaster-listen>
                        </a-image>
                    ) 
                }
              }):<></> }
            </>
        );
    }
}