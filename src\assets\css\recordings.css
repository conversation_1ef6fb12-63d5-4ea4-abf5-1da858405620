.record_input{
    color: #222b45;
    font-size: 14px;
    font-weight: bold;
    align-self: center;
}

@media only screen and (max-width:406px){
    .record_date_input_to{
        min-width: 34px;
    }
}
.recordings_search{
    width: 150px !important;
    margin-bottom: 10px;
    margin-right: 10px;
}

.recordings_search_div{
    margin-top: 8px;
    /* margin-bottom: 10px; */
    margin-right: 10px;
}

.recording_topic{
    color:#3366ff;
    margin-left: 40px;
    text-overflow: ellipsis;
  overflow: hidden; 
  width: 160px; 
  height: 1.2em; 
  white-space: nowrap;
}

td, th {
    padding-left: 30px;
    padding-right: 30px;
}

th{
    padding-top: 10px;
}

td{
    padding-top: 20px;
    font-weight: bold;
    font-size: 15px;
}

.recording_checkbox{
    position: relative !important;
    margin-left:0 !important;
}   


.meeting_edit_btn:hover{
color: #fff;
box-shadow: 0 14px 26px -12px rgb(153 153 153 / 42%), 0 4px 23px 0px rgb(0 0 0 / 12%), 0 8px 10px -5px rgb(153 153 153 / 20%);
background-color: var(--main-color-hover);

}


.recording_player{
    margin: auto;
    width: 100%;
    padding: 10px;
}
.recording_player_media{
    width: 60%;
    
}
.recording_player_media video{
    
    margin: auto;
    width: 100%;

}

 .of-container{
    flex: auto;
    text-align: center;
    vertical-align: baseline;
    margin: 0;
    margin: auto;
    font-weight: 500;
    font-size: 24px;
}
.recording_player_media .tools{
    display: flex;
    flex-direction: row;
}
.Recordings_list th{
    text-align: left;
    font-weight: bold;
}
.Recordings_list .span-text{
    text-align: center;
    font-weight: 100;
}