import React from "react";
import { connect } from "react-redux";


class Timer extends React.Component {

    constructor(props) {
        super(props)
        this.state = {
            start: new Date(this.props.start),
            timer: 0,
            sleepmodal: false,
            sleeptimer: 0,
            timeoutmodal: false
        }

    }
    componentDidMount() {
        const job = setInterval(() => {
            const time = new Date().getTime();
            const starttime = this.state.start.getTime();
            const diff = time - starttime;
            if (this.props.trackMaxtimeout && !this.state.timeoutmodal) {
                if (diff >= ((((this.maxsessionTime * 60) - 10) * 1000))) {
                    if (this.state.sleepmodal) {
                        this.StopSleep()
                    }
                    this.setState({
                        timeoutmodal: true,
                        sleeptimer: 10,
                    })
                    this.StartSleep()
                }
            }
            this.setState({ timer: diff })
        }, 1000);
    }
    convertTimetohours(diff) {
        let seconds = diff / 1000;
        let minutes = seconds / 60;
        let hours = minutes / 60;

        let tallyseconds = parseInt(seconds % 60);
        let tallyminutes = parseInt(minutes);
        if (tallyminutes <= 9)
            tallyminutes = "0" + tallyminutes
        if (tallyseconds <= 9)
            tallyseconds = "0" + tallyseconds
        if (hours > 0) {
            return (tallyminutes + ":" + tallyseconds)
        }
        return (tallyminutes + ":" + tallyseconds)
    }


    render() {
        return (
            <>
                {this.convertTimetohours(this.state.timer) !== undefined ?
                    <div
                        style={{
                            display: this.props.ShowControls ? "inline-flex" : "none",
                            position: this.props.position || 'relative',
                            border: this.props.style?.border || 'none'
                        }}
                        className="items-center justify-center bg-[#F3F4F6] text-black h-[25px] w-[56px] rounded-[34px]"
                    >
                        <span className="text-[12px] font-medium text-center leading-[150%]">
                            {this.state.timer < 0 ? "00:00" : this.convertTimetohours(this.state.timer)}</span>
                    </div>
                    :
                    <></>
                }




            </>
        )
    }
}
const mapStateToProps = state => {
    return {

        ShowControls: state.Call.ShowControls,
    }
}


export default connect(mapStateToProps, null)(Timer)