import React from "react";
import { connect } from "react-redux";
import 'jquery';

class ShareModalNew extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      url: '',
      tooltip: false,
      shortUrl: ''
    }
    this.handlecopy = this.handlecopy.bind(this);
    this.onClick = this.onClick.bind(this);
    this.handleShare = this.handleShare.bind(this);
  }

  onClick() {
    this.setState({
      tooltip: false
    });
    this.props.open_close('share', false);

    // Call onDismiss if provided
    if (this.props.onDismiss) {
      this.props.onDismiss();
    }
  }

  handlecopy() {
    navigator.clipboard.writeText(this.state.shortUrl || encodeURI(this.state.url));
    this.setState({
      tooltip: true
    });
  }

  handleShare() {
    if (navigator.share) {
      navigator.share({
        title: 'Join my meeting',
        text: 'Click the link to join my meeting',
        url: this.state.url,
      })
      .then(() => console.log('Successful share'))
      .catch((error) => console.log('Error sharing', error));
    } else {
      this.handlecopy();
    }
  }

  componentDidMount() {
    window.scrollTo(0, 0);

    const fullUrl = window.location.protocol + '//' + window.location.hostname + (window.location.port.length ? ":" + window.location.port : "") + "/salestool/joinroom/" + this.props.roomId;


    this.setState({
      url: fullUrl,
    });
  }

  render() {
    return (
      <div
        style={{ display: this.props.share === true ? 'flex' : 'none' }}
        className="bg-black/50 fixed z-[10501] inset-0 flex items-center justify-center p-4 md:p-0"
        id="share_modal"
        tabIndex="-1"
        role="dialog"
      >
        <div className="w-full max-w-md bg-white rounded-3xl shadow-xl overflow-hidden">
          <div className="p-6 md:p-8 flex flex-col">
            <h2 className="text-2xl font-medium text-center text-gray-700 mb-6">Meeting Link</h2>

            <div className="flex items-center bg-gray-100 rounded-xl p-4 mb-6">
              <svg className="w-6 h-6 text-gray-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
              </svg>

              <span className="mx-3 text-gray-700 text-base font-medium truncate flex-grow">
                {this.state.url}
              </span>

              <button
                onClick={this.handlecopy}
                className="flex-shrink-0 text-gray-500 hover:text-gray-700"
                title="Copy link to clipboard"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                </svg>
              </button>
            </div>

            <button
              onClick={this.handleShare}
              className="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-4 rounded-xl mb-4 transition duration-200"
            >
              Share the link
            </button>

            <button
              onClick={this.onClick}
              className="w-full text-blue-500 font-medium py-3 px-4 rounded-xl transition duration-200 hover:bg-blue-100"
            >
              Dismiss
            </button>

            {this.state.tooltip && (
              <div className="mt-2 text-sm text-green-600 font-medium text-center">
                Link copied to clipboard
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }
}

const mapStateToProps = state => {
  return {
    Config: state.Call.config,
  }
}

export default connect(mapStateToProps, null)(ShareModalNew);