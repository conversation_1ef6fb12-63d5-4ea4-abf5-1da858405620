import axios from "axios";
import { get<PERSON><PERSON><PERSON> } from "./domhelper";

export async function GetRequestWithHeaders({ url }) {
  return new Promise((resolve, reject) => {
    var config = {
      method: "get",
      url,
      headers: {
        'accesstoken': getCookie('accessToken'),
        'organization':getCookie('organization'),
      },
    };

    axios(config)
      .then(function (response) {
        if (response.data.status === 1) {
          resolve(response.data);
        } else {
          reject(response.data);
        }
      })
      .catch(function (error) {
        reject(error);
      });
  });
}

export async function PostRequestWithHeaders({url,body}) {
  return new Promise((resolve, reject) => {
    var config = {
      method: 'post',
      url,
      headers: {
        'accesstoken': getCookie('accessToken'),
        'organization': getCookie('organization'),
      },
      data: body
    };

    axios(config)
      .then(function (response) {
        if(response.data.status===1){
        resolve(response.data.data);}
        else{
          reject(response.data)
        }

      })
      .catch(function (error) {
        reject(error);
      });
  })
}

export async function PostRequest({url,body}) {
    return new Promise((resolve, reject) => {
      var config = {
        method: 'post',
        url,
        data: body
      };

      axios(config)
        .then(function (response) {
          if(response.data.status===1){
          resolve(response.data.data);}
          else{
            reject(response.data)
          }

        })
        .catch(function (error) {
          reject(error);
        });
    })
  }

//temp
export async function GetMasterSceneWithHeader({ url,body }) {
  return new Promise((resolve, reject) => {
    var config = {
      method: "get",
      url,
      headers: {
        'accesstoken': getCookie('accessToken'),
        'organization':getCookie('organization'),
      },
      body:body
    };

    axios(config)
      .then(function (response) {
        if (response.data.status === 1) {
          resolve(response.data);
        } else {
          reject(response.data);
        }
      })
      .catch(function (error) {
        reject(error);
      });
  });
}
