body,html{
  padding: 0;
  margin: 0;
}


/* Custom loading screen styles */
.loading.svelte-ro1nws.svelte-ro1nws {
  /* background-image: none !important; */
  background-image: url('https://storagecdn.propvr.tech/0SalestoolAssets/loader.svg'), linear-gradient(135deg, #0a2463 0%, #3e92cc 100%) !important;
  background-size: 120px auto, 100% 100% !important;
  background-repeat: no-repeat !important;
  background-position: center, center !important;
  /* position: relative !important; */

}

.logo-footer.svelte-ro1nws,
.logo {
  display: none !important;
}

/* Add the "Loading Experience" text using :after pseudo-element */
.loading.svelte-ro1nws.svelte-ro1nws:after {
  content: "Loading Experience" !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, 80px) !important; /* Position below the logo */
  color: white !important;
  font-size: 18px !important;
  font-weight: 500 !important;
  font-family: sans-serif !important;
  letter-spacing: 1px !important;
  white-space: nowrap !important;
}

/* Default */
/* 全局变量 */
:root {
    /* 主背景颜色 */
    --webclient-bg-color: rgba(0, 0, 0, 0.05);
    /* all */
    --webclient-check-color: #00B031;
    /* 左上角网络图标 */
    --webclient-rtt-good: #34a853;
    --webclient-rtt-bad: red;
    /* PC-菜单 */
    --webclient-menubar-bg: rgba(68, 68, 68, 0.80);
    --webclient-menubar-icon-color: #BBB;
    --webclient-menubar-icon-hover-color: #fff;
    --webclient-menubar-tooltip-bg: rgba(51, 51, 51, 0.90);
    --webclient-menubar-tooltip-font-color: #DDD;
    --webclient-menubar-btn-font-color: #5A5A5A;
    --webclient-menubar-btn-bg: #444;
    --webclient-menubar-btn-hover-bg: #ccc;
    /* Mob menubar */
    --webclient-mob-menubar-bg: #545454;
    --webclient-mob-menubar-game-bg: rgba(255, 255, 255, 0.10);
    --webclient-mob-menubar-icon-color: #DDD;
    --webclient-mob-menubar-icon-close-bg: rgba(51, 51, 51, 0.40);
    /* mob control_ball */
    --webclient-controlball-bg: rgba(84, 84, 84, 0.40);
    --webclient-controlball-icon-color: #EEEEEE;
    --webclient-controlball-panel-bg: #454545;
    --webclient-controlball-panel-font-color: #DDD;
    /* 摄像头麦克风下拉框 */
    --webclient-menubar-select-bg: #454545;
    --webclient-menubar-select-hover-bg: #EEE;
    --webclient-menubar-select-hover-font-color: #454545;
    --webclient-menubar-select-title-font-color: #DDD;
    --webclient-menubar-select-font-color: #FAFAFA;
    /* dialog */
    --webclient-dialog-title-bg: rgba(51, 51, 51, 0.90);
    --webclient-dialog-body-bg: rgba(51, 51, 51, 0.80);
    --webclient-dialog-font-color: #DDD;
    --webclient-dialog-divider: rgba(255, 255, 255, 0.20);
    --webclient-dialog-button-bg: #000;
    --webclient-dialog-button-font-color: #DDD;
    --webclient-dialog-button-hover-bg: #333;
    /* toast */
    --webclient-toast-bg-1: #333333;
    --webclient-toast-bg-05: rgba(51, 51, 51, 0.5);
    --webclient-toast-font-color: #fff;
    /* PC-设置面板 */
    --webclient-setting-titletab-bg: #555;
    --webclient-setting-titletab-font-color: #fff;
    --webclient-setting-btn-active-bg: #fff;
    --webclient-setting-btn-active-font-color: #444;
    --webclient-slide-bg: #474747;
    --webclient-slide-bar-bg: #00B031;
    --webclient-slide-btn-bg: #fff;
    /* 移动端面板 */
    --webclient-modal_bg: #5A5A5A;
    --webclient-modal-title-bg: #454545;
    --webclient-modal-font-color: #DDDDDD;
    --webclient-modal-row-check-bg: #454545;
    --webclient-modal-row-check-font-color: #DDD;
    --webclient-modal-tag-font-color: #fff;
    --webclient-modal-tag-active-bg: #fff;
    --webclient-modal-tag-active-font-color: #444;
}

.loading.svelte-ro1nws.svelte-ro1nws{
    background-image:url('https://storagecdn.propvr.tech/0SalestoolAssets/loader.svg'), linear-gradient(135deg, #0a2463 0%, #3e92cc 100%) !important;
    background-size: 120px auto, 100% 100% !important;
    background-repeat: no-repeat !important;
    background-position: center, center !important;}
.logo-footer.svelte-ro1nws{
    background-image:url('https://storagecdn.propvr.tech/0SalestoolAssets/loader.svg') !important;
    display:none;
}
.logo{
    display: none !important;
}
.loading.svelte-ro1nws.svelte-ro1nws:after {
    content: "Loading Experience" !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, 80px) !important; /* Position below the logo */
    color: white !important;
    font-size: 18px !important;
    font-weight: 500 !important;
    font-family: sans-serif !important;
    letter-spacing: 1px !important;
    white-space: nowrap !important;
  }


  .el-message-box {
    background-color: rgba(51, 51, 51, 0.9) !important;
    border: none !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  }
  
  /* Hide the original resource error message */
  .el-message-box__content, 
  .el-message-box__message {
    display: none !important;
  }
  
  /* Add our custom message */
  .el-message-box__content:after {
    content: "Our system is currently experiencing high demand. We're working to free up resources as soon as possible. Please try again in a few moments. Thank you for your patience!" !important;
    display: block !important;
    color: white !important;
    font-size: 16px !important;
    font-family: sans-serif !important;
    text-align: center !important;
    padding: 20px 15px !important;
    line-height: 1.5 !important;
  }
  
  /* Style the OK button */
  .el-message-box__btns button {
    background-color: #007bff !important;
    border-color: #007bff !important;
    color: white !important;
    border-radius: 4px !important;
    padding: 8px 20px !important;
    font-weight: 500 !important;
    transition: background-color 0.2s ease !important;
  }
  
  .el-message-box__btns button:hover {
    background-color: #0069d9 !important;
    border-color: #0062cc !important;
  }

  .resource-full-message {
    display: none !important;
  }
iframe#webpack-dev-server-client-overlay{display:none!important}