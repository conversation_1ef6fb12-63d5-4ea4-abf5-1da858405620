(function e(t,r){if(typeof exports==="object"&&typeof module==="object")module.exports=r();else if(typeof define==="function"&&define.amd)define("pdfjs-dist/build/pdf",[],r);else if(typeof exports==="object")exports["pdfjs-dist/build/pdf"]=r();else t["pdfjs-dist/build/pdf"]=t.pdfjsDistBuildPdf=r()})(typeof self!=="undefined"?self:this,function(){return function(e){var t={};function r(n){if(t[n]){return t[n].exports}var a=t[n]={i:n,l:false,exports:{}};e[n].call(a.exports,a,a.exports,r);a.l=true;return a.exports}r.m=e;r.c=t;r.d=function(e,t,n){if(!r.o(e,t)){Object.defineProperty(e,t,{configurable:false,enumerable:true,get:n})}};r.n=function(e){var t=e&&e.__esModule?function t(){return e["default"]}:function t(){return e};r.d(t,"a",t);return t};r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)};r.p="";return r(r.s=75)}([function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.unreachable=t.warn=t.utf8StringToString=t.stringToUTF8String=t.stringToPDFString=t.stringToBytes=t.string32=t.shadow=t.setVerbosityLevel=t.ReadableStream=t.removeNullCharacters=t.readUint32=t.readUint16=t.readInt8=t.log2=t.loadJpegStream=t.isEvalSupported=t.isLittleEndian=t.createValidAbsoluteUrl=t.isSameOrigin=t.isNodeJS=t.isSpace=t.isString=t.isNum=t.isEmptyObj=t.isBool=t.isArrayBuffer=t.info=t.getVerbosityLevel=t.getLookupTableFactory=t.deprecated=t.createObjectURL=t.createPromiseCapability=t.createBlob=t.bytesToString=t.assert=t.arraysToBytes=t.arrayByteLength=t.FormatError=t.XRefParseException=t.Util=t.UnknownErrorException=t.UnexpectedResponseException=t.TextRenderingMode=t.StreamType=t.StatTimer=t.PasswordResponses=t.PasswordException=t.PageViewport=t.NotImplementedException=t.NativeImageDecoding=t.MissingPDFException=t.MissingDataException=t.MessageHandler=t.InvalidPDFException=t.AbortException=t.CMapCompressionType=t.ImageKind=t.FontType=t.AnnotationType=t.AnnotationFlag=t.AnnotationFieldFlag=t.AnnotationBorderStyleType=t.UNSUPPORTED_FEATURES=t.VERBOSITY_LEVELS=t.OPS=t.IDENTITY_MATRIX=t.FONT_IDENTITY_MATRIX=undefined;var n=typeof Symbol==="function"&&typeof Symbol.iterator==="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol==="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};r(76);var a=r(116);var i=[.001,0,0,.001,0,0];var s={NONE:"none",DECODE:"decode",DISPLAY:"display"};var o={FILL:0,STROKE:1,FILL_STROKE:2,INVISIBLE:3,FILL_ADD_TO_PATH:4,STROKE_ADD_TO_PATH:5,FILL_STROKE_ADD_TO_PATH:6,ADD_TO_PATH:7,FILL_STROKE_MASK:3,ADD_TO_PATH_FLAG:4};var u={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3};var l={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26};var c={INVISIBLE:1,HIDDEN:2,PRINT:4,NOZOOM:8,NOROTATE:16,NOVIEW:32,READONLY:64,LOCKED:128,TOGGLENOVIEW:256,LOCKEDCONTENTS:512};var f={READONLY:1,REQUIRED:2,NOEXPORT:4,MULTILINE:4096,PASSWORD:8192,NOTOGGLETOOFF:16384,RADIO:32768,PUSHBUTTON:65536,COMBO:131072,EDIT:262144,SORT:524288,FILESELECT:1048576,MULTISELECT:2097152,DONOTSPELLCHECK:4194304,DONOTSCROLL:8388608,COMB:16777216,RICHTEXT:33554432,RADIOSINUNISON:33554432,COMMITONSELCHANGE:67108864};var d={SOLID:1,DASHED:2,BEVELED:3,INSET:4,UNDERLINE:5};var h={UNKNOWN:0,FLATE:1,LZW:2,DCT:3,JPX:4,JBIG:5,A85:6,AHX:7,CCF:8,RL:9};var v={UNKNOWN:0,TYPE1:1,TYPE1C:2,CIDFONTTYPE0:3,CIDFONTTYPE0C:4,TRUETYPE:5,CIDFONTTYPE2:6,TYPE3:7,OPENTYPE:8,TYPE0:9,MMTYPE1:10};var p={errors:0,warnings:1,infos:5};var m={NONE:0,BINARY:1,STREAM:2};var g={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotations:78,endAnnotations:79,beginAnnotation:80,endAnnotation:81,paintJpegXObject:82,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91};var b=p.warnings;function y(e){b=e}function _(){return b}function A(e){if(b>=p.infos){console.log("Info: "+e)}}function S(e){if(b>=p.warnings){console.log("Warning: "+e)}}function w(e){console.log("Deprecated API usage: "+e)}function P(e){throw new Error(e)}function k(e,t){if(!e){P(t)}}var C={unknown:"unknown",forms:"forms",javaScript:"javaScript",smask:"smask",shadingPattern:"shadingPattern",font:"font"};function R(e,t){try{var r=new URL(e);if(!r.origin||r.origin==="null"){return false}}catch(e){return false}var n=new URL(t,r);return r.origin===n.origin}function x(e){if(!e){return false}switch(e.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return true;default:return false}}function T(e,t){if(!e){return null}try{var r=t?new URL(e,t):new URL(e);if(x(r)){return r}}catch(e){}return null}function E(e,t,r){Object.defineProperty(e,t,{value:r,enumerable:true,configurable:true,writable:false});return r}function O(e){var t;return function(){if(e){t=Object.create(null);e(t);e=null}return t}}var L={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};var I=function e(){function t(e,t){this.name="PasswordException";this.message=e;this.code=t}t.prototype=new Error;t.constructor=t;return t}();var j=function e(){function t(e,t){this.name="UnknownErrorException";this.message=e;this.details=t}t.prototype=new Error;t.constructor=t;return t}();var F=function e(){function t(e){this.name="InvalidPDFException";this.message=e}t.prototype=new Error;t.constructor=t;return t}();var D=function e(){function t(e){this.name="MissingPDFException";this.message=e}t.prototype=new Error;t.constructor=t;return t}();var M=function e(){function t(e,t){this.name="UnexpectedResponseException";this.message=e;this.status=t}t.prototype=new Error;t.constructor=t;return t}();var N=function e(){function t(e){this.message=e}t.prototype=new Error;t.prototype.name="NotImplementedException";t.constructor=t;return t}();var q=function e(){function t(e,t){this.begin=e;this.end=t;this.message="Missing data ["+e+", "+t+")"}t.prototype=new Error;t.prototype.name="MissingDataException";t.constructor=t;return t}();var U=function e(){function t(e){this.message=e}t.prototype=new Error;t.prototype.name="XRefParseException";t.constructor=t;return t}();var W=function e(){function t(e){this.message=e}t.prototype=new Error;t.prototype.name="FormatError";t.constructor=t;return t}();var B=function e(){function t(e){this.name="AbortException";this.message=e}t.prototype=new Error;t.constructor=t;return t}();var z=/\x00/g;function G(e){if(typeof e!=="string"){S("The argument for removeNullCharacters must be a string.");return e}return e.replace(z,"")}function H(e){k(e!==null&&(typeof e==="undefined"?"undefined":n(e))==="object"&&e.length!==undefined,"Invalid argument for bytesToString");var t=e.length;var r=8192;if(t<r){return String.fromCharCode.apply(null,e)}var a=[];for(var i=0;i<t;i+=r){var s=Math.min(i+r,t);var o=e.subarray(i,s);a.push(String.fromCharCode.apply(null,o))}return a.join("")}function X(e){k(typeof e==="string","Invalid argument for stringToBytes");var t=e.length;var r=new Uint8Array(t);for(var n=0;n<t;++n){r[n]=e.charCodeAt(n)&255}return r}function V(e){if(e.length!==undefined){return e.length}k(e.byteLength!==undefined);return e.byteLength}function Y(e){if(e.length===1&&e[0]instanceof Uint8Array){return e[0]}var t=0;var r,n=e.length;var a,i;for(r=0;r<n;r++){a=e[r];i=V(a);t+=i}var s=0;var o=new Uint8Array(t);for(r=0;r<n;r++){a=e[r];if(!(a instanceof Uint8Array)){if(typeof a==="string"){a=X(a)}else{a=new Uint8Array(a)}}i=a.byteLength;o.set(a,s);s+=i}return o}function J(e){return String.fromCharCode(e>>24&255,e>>16&255,e>>8&255,e&255)}function Q(e){var t=1,r=0;while(e>t){t<<=1;r++}return r}function K(e,t){return e[t]<<24>>24}function Z(e,t){return e[t]<<8|e[t+1]}function $(e,t){return(e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3])>>>0}function ee(){var e=new Uint8Array(4);e[0]=1;var t=new Uint32Array(e.buffer,0,1);return t[0]===1}function te(){try{new Function("");return true}catch(e){return false}}var re=[1,0,0,1,0,0];var ne=function e(){function t(){}var r=["rgb(",0,",",0,",",0,")"];t.makeCssRgb=function e(t,n,a){r[1]=t;r[3]=n;r[5]=a;return r.join("")};t.transform=function e(t,r){return[t[0]*r[0]+t[2]*r[1],t[1]*r[0]+t[3]*r[1],t[0]*r[2]+t[2]*r[3],t[1]*r[2]+t[3]*r[3],t[0]*r[4]+t[2]*r[5]+t[4],t[1]*r[4]+t[3]*r[5]+t[5]]};t.applyTransform=function e(t,r){var n=t[0]*r[0]+t[1]*r[2]+r[4];var a=t[0]*r[1]+t[1]*r[3]+r[5];return[n,a]};t.applyInverseTransform=function e(t,r){var n=r[0]*r[3]-r[1]*r[2];var a=(t[0]*r[3]-t[1]*r[2]+r[2]*r[5]-r[4]*r[3])/n;var i=(-t[0]*r[1]+t[1]*r[0]+r[4]*r[1]-r[5]*r[0])/n;return[a,i]};t.getAxialAlignedBoundingBox=function e(r,n){var a=t.applyTransform(r,n);var i=t.applyTransform(r.slice(2,4),n);var s=t.applyTransform([r[0],r[3]],n);var o=t.applyTransform([r[2],r[1]],n);return[Math.min(a[0],i[0],s[0],o[0]),Math.min(a[1],i[1],s[1],o[1]),Math.max(a[0],i[0],s[0],o[0]),Math.max(a[1],i[1],s[1],o[1])]};t.inverseTransform=function e(t){var r=t[0]*t[3]-t[1]*t[2];return[t[3]/r,-t[1]/r,-t[2]/r,t[0]/r,(t[2]*t[5]-t[4]*t[3])/r,(t[4]*t[1]-t[5]*t[0])/r]};t.apply3dTransform=function e(t,r){return[t[0]*r[0]+t[1]*r[1]+t[2]*r[2],t[3]*r[0]+t[4]*r[1]+t[5]*r[2],t[6]*r[0]+t[7]*r[1]+t[8]*r[2]]};t.singularValueDecompose2dScale=function e(t){var r=[t[0],t[2],t[1],t[3]];var n=t[0]*r[0]+t[1]*r[2];var a=t[0]*r[1]+t[1]*r[3];var i=t[2]*r[0]+t[3]*r[2];var s=t[2]*r[1]+t[3]*r[3];var o=(n+s)/2;var u=Math.sqrt((n+s)*(n+s)-4*(n*s-i*a))/2;var l=o+u||1;var c=o-u||1;return[Math.sqrt(l),Math.sqrt(c)]};t.normalizeRect=function e(t){var r=t.slice(0);if(t[0]>t[2]){r[0]=t[2];r[2]=t[0]}if(t[1]>t[3]){r[1]=t[3];r[3]=t[1]}return r};t.intersect=function e(r,n){function a(e,t){return e-t}var i=[r[0],r[2],n[0],n[2]].sort(a),s=[r[1],r[3],n[1],n[3]].sort(a),o=[];r=t.normalizeRect(r);n=t.normalizeRect(n);if(i[0]===r[0]&&i[1]===n[0]||i[0]===n[0]&&i[1]===r[0]){o[0]=i[1];o[2]=i[2]}else{return false}if(s[0]===r[1]&&s[1]===n[1]||s[0]===n[1]&&s[1]===r[1]){o[1]=s[1];o[3]=s[2]}else{return false}return o};var n=["","C","CC","CCC","CD","D","DC","DCC","DCCC","CM","","X","XX","XXX","XL","L","LX","LXX","LXXX","XC","","I","II","III","IV","V","VI","VII","VIII","IX"];t.toRoman=function e(t,r){k(Number.isInteger(t)&&t>0,"The number should be a positive integer.");var a,i=[];while(t>=1e3){t-=1e3;i.push("M")}a=t/100|0;t%=100;i.push(n[a]);a=t/10|0;t%=10;i.push(n[10+a]);i.push(n[20+t]);var s=i.join("");return r?s.toLowerCase():s};t.appendToArray=function e(t,r){Array.prototype.push.apply(t,r)};t.prependToArray=function e(t,r){Array.prototype.unshift.apply(t,r)};t.extendObj=function e(t,r){for(var n in r){t[n]=r[n]}};t.getInheritableProperty=function e(t,r,n){while(t&&!t.has(r)){t=t.get("Parent")}if(!t){return null}return n?t.getArray(r):t.get(r)};t.inherit=function e(t,r,n){t.prototype=Object.create(r.prototype);t.prototype.constructor=t;for(var a in n){t.prototype[a]=n[a]}};t.loadScript=function e(t,r){var n=document.createElement("script");var a=false;n.setAttribute("src",t);if(r){n.onload=function(){if(!a){r()}a=true}}document.getElementsByTagName("head")[0].appendChild(n)};return t}();var ae=function e(){function t(e,t,r,n,a,i){this.viewBox=e;this.scale=t;this.rotation=r;this.offsetX=n;this.offsetY=a;var s=(e[2]+e[0])/2;var o=(e[3]+e[1])/2;var u,l,c,f;r=r%360;r=r<0?r+360:r;switch(r){case 180:u=-1;l=0;c=0;f=1;break;case 90:u=0;l=1;c=1;f=0;break;case 270:u=0;l=-1;c=-1;f=0;break;default:u=1;l=0;c=0;f=-1;break}if(i){c=-c;f=-f}var d,h;var v,p;if(u===0){d=Math.abs(o-e[1])*t+n;h=Math.abs(s-e[0])*t+a;v=Math.abs(e[3]-e[1])*t;p=Math.abs(e[2]-e[0])*t}else{d=Math.abs(s-e[0])*t+n;h=Math.abs(o-e[1])*t+a;v=Math.abs(e[2]-e[0])*t;p=Math.abs(e[3]-e[1])*t}this.transform=[u*t,l*t,c*t,f*t,d-u*t*s-c*t*o,h-l*t*s-f*t*o];this.width=v;this.height=p;this.fontScale=t}t.prototype={clone:function e(r){r=r||{};var n="scale"in r?r.scale:this.scale;var a="rotation"in r?r.rotation:this.rotation;return new t(this.viewBox.slice(),n,a,this.offsetX,this.offsetY,r.dontFlip)},convertToViewportPoint:function e(t,r){return ne.applyTransform([t,r],this.transform)},convertToViewportRectangle:function e(t){var r=ne.applyTransform([t[0],t[1]],this.transform);var n=ne.applyTransform([t[2],t[3]],this.transform);return[r[0],r[1],n[0],n[1]]},convertToPdfPoint:function e(t,r){return ne.applyInverseTransform([t,r],this.transform)}};return t}();var ie=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,728,711,710,729,733,731,730,732,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,8226,8224,8225,8230,8212,8211,402,8260,8249,8250,8722,8240,8222,8220,8221,8216,8217,8218,8482,64257,64258,321,338,352,376,381,305,322,339,353,382,0,8364];function se(e){var t,r=e.length,n=[];if(e[0]==="þ"&&e[1]==="ÿ"){for(t=2;t<r;t+=2){n.push(String.fromCharCode(e.charCodeAt(t)<<8|e.charCodeAt(t+1)))}}else{for(t=0;t<r;++t){var a=ie[e.charCodeAt(t)];n.push(a?String.fromCharCode(a):e.charAt(t))}}return n.join("")}function oe(e){return decodeURIComponent(escape(e))}function ue(e){return unescape(encodeURIComponent(e))}function le(e){for(var t in e){return false}return true}function ce(e){return typeof e==="boolean"}function fe(e){return typeof e==="number"}function de(e){return typeof e==="string"}function he(e){return(typeof e==="undefined"?"undefined":n(e))==="object"&&e!==null&&e.byteLength!==undefined}function ve(e){return e===32||e===9||e===13||e===10}function pe(){return(typeof process==="undefined"?"undefined":n(process))==="object"&&process+""==="[object process]"}function me(){var e={};e.promise=new Promise(function(t,r){e.resolve=t;e.reject=r});return e}var ge=function e(){function t(e,t,r){while(e.length<r){e+=t}return e}function r(){this.started=Object.create(null);this.times=[];this.enabled=true}r.prototype={time:function e(t){if(!this.enabled){return}if(t in this.started){S("Timer is already running for "+t)}this.started[t]=Date.now()},timeEnd:function e(t){if(!this.enabled){return}if(!(t in this.started)){S("Timer has not been started for "+t)}this.times.push({name:t,start:this.started[t],end:Date.now()});delete this.started[t]},toString:function e(){var r,n;var a=this.times;var i="";var s=0;for(r=0,n=a.length;r<n;++r){var o=a[r]["name"];if(o.length>s){s=o.length}}for(r=0,n=a.length;r<n;++r){var u=a[r];var l=u.end-u.start;i+=t(u["name"]," ",s)+" "+l+"ms\n"}return i}};return r}();var be=function e(t,r){if(typeof Blob!=="undefined"){return new Blob([t],{type:r})}throw new Error('The "Blob" constructor is not supported.')};var ye=function e(){var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";return function e(r,n){var a=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;if(!a&&URL.createObjectURL){var i=be(r,n);return URL.createObjectURL(i)}var s="data:"+n+";base64,";for(var o=0,u=r.length;o<u;o+=3){var l=r[o]&255;var c=r[o+1]&255;var f=r[o+2]&255;var d=l>>2,h=(l&3)<<4|c>>4;var v=o+1<u?(c&15)<<2|f>>6:64;var p=o+2<u?f&63:64;s+=t[d]+t[h]+t[v]+t[p]}return s}}();function _e(e,t){var r=arguments.length>2&&arguments[2]!==undefined?arguments[2]:null;if(!e){return Promise.resolve(undefined)}return new Promise(function(n,a){n(e.apply(r,t))})}function Ae(e){if((typeof e==="undefined"?"undefined":n(e))!=="object"){return e}switch(e.name){case"AbortException":return new B(e.message);case"MissingPDFException":return new D(e.message);case"UnexpectedResponseException":return new M(e.message,e.status);default:return new j(e.message,e.details)}}function Se(e){if(!(e instanceof Error)||e instanceof B||e instanceof D||e instanceof M||e instanceof j){return e}return new j(e.message,e.toString())}function we(e,t,r){if(t){e.resolve()}else{e.reject(r)}}function Pe(e){return Promise.resolve(e).catch(function(){})}function ke(e,t,r){var n=this;this.sourceName=e;this.targetName=t;this.comObj=r;this.callbackId=1;this.streamId=1;this.postMessageTransfers=true;this.streamSinks=Object.create(null);this.streamControllers=Object.create(null);var a=this.callbacksCapabilities=Object.create(null);var i=this.actionHandler=Object.create(null);this._onComObjOnMessage=function(e){var t=e.data;if(t.targetName!==n.sourceName){return}if(t.stream){n._processStreamMessage(t)}else if(t.isReply){var s=t.callbackId;if(t.callbackId in a){var o=a[s];delete a[s];if("error"in t){o.reject(Ae(t.error))}else{o.resolve(t.data)}}else{throw new Error("Cannot resolve callback "+s)}}else if(t.action in i){var u=i[t.action];if(t.callbackId){var l=n.sourceName;var c=t.sourceName;Promise.resolve().then(function(){return u[0].call(u[1],t.data)}).then(function(e){r.postMessage({sourceName:l,targetName:c,isReply:true,callbackId:t.callbackId,data:e})},function(e){r.postMessage({sourceName:l,targetName:c,isReply:true,callbackId:t.callbackId,error:Se(e)})})}else if(t.streamId){n._createStreamSink(t)}else{u[0].call(u[1],t.data)}}else{throw new Error("Unknown action from worker: "+t.action)}};r.addEventListener("message",this._onComObjOnMessage)}ke.prototype={on:function e(t,r,n){var a=this.actionHandler;if(a[t]){throw new Error('There is already an actionName called "'+t+'"')}a[t]=[r,n]},send:function e(t,r,n){var a={sourceName:this.sourceName,targetName:this.targetName,action:t,data:r};this.postMessage(a,n)},sendWithPromise:function e(t,r,n){var a=this.callbackId++;var i={sourceName:this.sourceName,targetName:this.targetName,action:t,data:r,callbackId:a};var s=me();this.callbacksCapabilities[a]=s;try{this.postMessage(i,n)}catch(e){s.reject(e)}return s.promise},sendWithStream:function e(t,r,n,i){var s=this;var o=this.streamId++;var u=this.sourceName;var l=this.targetName;return new a.ReadableStream({start:function e(n){var a=me();s.streamControllers[o]={controller:n,startCall:a,isClosed:false};s.postMessage({sourceName:u,targetName:l,action:t,streamId:o,data:r,desiredSize:n.desiredSize});return a.promise},pull:function e(t){var r=me();s.streamControllers[o].pullCall=r;s.postMessage({sourceName:u,targetName:l,stream:"pull",streamId:o,desiredSize:t.desiredSize});return r.promise},cancel:function e(t){var r=me();s.streamControllers[o].cancelCall=r;s.streamControllers[o].isClosed=true;s.postMessage({sourceName:u,targetName:l,stream:"cancel",reason:t,streamId:o});return r.promise}},n)},_createStreamSink:function e(t){var r=this;var n=this;var a=this.actionHandler[t.action];var i=t.streamId;var s=t.desiredSize;var o=this.sourceName;var u=t.sourceName;var l=me();var c=function e(t){var n=t.stream,a=t.chunk,s=t.transfers,l=t.success,c=t.reason;r.postMessage({sourceName:o,targetName:u,stream:n,streamId:i,chunk:a,success:l,reason:c},s)};var f={enqueue:function e(t){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:1;var n=arguments[2];if(this.isCancelled){return}var a=this.desiredSize;this.desiredSize-=r;if(a>0&&this.desiredSize<=0){this.sinkCapability=me();this.ready=this.sinkCapability.promise}c({stream:"enqueue",chunk:t,transfers:n})},close:function e(){if(this.isCancelled){return}this.isCancelled=true;c({stream:"close"});delete n.streamSinks[i]},error:function e(t){if(this.isCancelled){return}this.isCancelled=true;c({stream:"error",reason:t})},sinkCapability:l,onPull:null,onCancel:null,isCancelled:false,desiredSize:s,ready:null};f.sinkCapability.resolve();f.ready=f.sinkCapability.promise;this.streamSinks[i]=f;_e(a[0],[t.data,f],a[1]).then(function(){c({stream:"start_complete",success:true})},function(e){c({stream:"start_complete",success:false,reason:e})})},_processStreamMessage:function e(t){var r=this;var n=this.sourceName;var a=t.sourceName;var i=t.streamId;var s=function e(t){var s=t.stream,o=t.success,u=t.reason;r.comObj.postMessage({sourceName:n,targetName:a,stream:s,success:o,streamId:i,reason:u})};var o=function e(){Promise.all([r.streamControllers[t.streamId].startCall,r.streamControllers[t.streamId].pullCall,r.streamControllers[t.streamId].cancelCall].map(function(e){return e&&Pe(e.promise)})).then(function(){delete r.streamControllers[t.streamId]})};switch(t.stream){case"start_complete":we(this.streamControllers[t.streamId].startCall,t.success,Ae(t.reason));break;case"pull_complete":we(this.streamControllers[t.streamId].pullCall,t.success,Ae(t.reason));break;case"pull":if(!this.streamSinks[t.streamId]){s({stream:"pull_complete",success:true});break}if(this.streamSinks[t.streamId].desiredSize<=0&&t.desiredSize>0){this.streamSinks[t.streamId].sinkCapability.resolve()}this.streamSinks[t.streamId].desiredSize=t.desiredSize;_e(this.streamSinks[t.streamId].onPull).then(function(){s({stream:"pull_complete",success:true})},function(e){s({stream:"pull_complete",success:false,reason:e})});break;case"enqueue":k(this.streamControllers[t.streamId],"enqueue should have stream controller");if(!this.streamControllers[t.streamId].isClosed){this.streamControllers[t.streamId].controller.enqueue(t.chunk)}break;case"close":k(this.streamControllers[t.streamId],"close should have stream controller");if(this.streamControllers[t.streamId].isClosed){break}this.streamControllers[t.streamId].isClosed=true;this.streamControllers[t.streamId].controller.close();o();break;case"error":k(this.streamControllers[t.streamId],"error should have stream controller");this.streamControllers[t.streamId].controller.error(Ae(t.reason));o();break;case"cancel_complete":we(this.streamControllers[t.streamId].cancelCall,t.success,Ae(t.reason));o();break;case"cancel":if(!this.streamSinks[t.streamId]){break}_e(this.streamSinks[t.streamId].onCancel,[Ae(t.reason)]).then(function(){s({stream:"cancel_complete",success:true})},function(e){s({stream:"cancel_complete",success:false,reason:e})});this.streamSinks[t.streamId].sinkCapability.reject(Ae(t.reason));this.streamSinks[t.streamId].isCancelled=true;delete this.streamSinks[t.streamId];break;default:throw new Error("Unexpected stream case")}},postMessage:function e(t,r){if(r&&this.postMessageTransfers){this.comObj.postMessage(t,r)}else{this.comObj.postMessage(t)}},destroy:function e(){this.comObj.removeEventListener("message",this._onComObjOnMessage)}};function Ce(e,t,r){var n=new Image;n.onload=function t(){r.resolve(e,n)};n.onerror=function t(){r.resolve(e,null);S("Error during JPEG image loading")};n.src=t}t.FONT_IDENTITY_MATRIX=i;t.IDENTITY_MATRIX=re;t.OPS=g;t.VERBOSITY_LEVELS=p;t.UNSUPPORTED_FEATURES=C;t.AnnotationBorderStyleType=d;t.AnnotationFieldFlag=f;t.AnnotationFlag=c;t.AnnotationType=l;t.FontType=v;t.ImageKind=u;t.CMapCompressionType=m;t.AbortException=B;t.InvalidPDFException=F;t.MessageHandler=ke;t.MissingDataException=q;t.MissingPDFException=D;t.NativeImageDecoding=s;t.NotImplementedException=N;t.PageViewport=ae;t.PasswordException=I;t.PasswordResponses=L;t.StatTimer=ge;t.StreamType=h;t.TextRenderingMode=o;t.UnexpectedResponseException=M;t.UnknownErrorException=j;t.Util=ne;t.XRefParseException=U;t.FormatError=W;t.arrayByteLength=V;t.arraysToBytes=Y;t.assert=k;t.bytesToString=H;t.createBlob=be;t.createPromiseCapability=me;t.createObjectURL=ye;t.deprecated=w;t.getLookupTableFactory=O;t.getVerbosityLevel=_;t.info=A;t.isArrayBuffer=he;t.isBool=ce;t.isEmptyObj=le;t.isNum=fe;t.isString=de;t.isSpace=ve;t.isNodeJS=pe;t.isSameOrigin=R;t.createValidAbsoluteUrl=T;t.isLittleEndian=ee;t.isEvalSupported=te;t.loadJpegStream=Ce;t.log2=Q;t.readInt8=K;t.readUint16=Z;t.readUint32=$;t.removeNullCharacters=G;t.ReadableStream=a.ReadableStream;t.setVerbosityLevel=y;t.shadow=E;t.string32=J;t.stringToBytes=X;t.stringToPDFString=se;t.stringToUTF8String=oe;t.utf8StringToString=ue;t.warn=S;t.unreachable=P},function(e,t,r){"use strict";var n=e.exports=typeof window!="undefined"&&window.Math==Math?window:typeof self!="undefined"&&self.Math==Math?self:Function("return this")();if(typeof __g=="number")__g=n},function(e,t,r){"use strict";var n=typeof Symbol==="function"&&typeof Symbol.iterator==="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol==="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};e.exports=function(e){return(typeof e==="undefined"?"undefined":n(e))==="object"?e!==null:typeof e==="function"}},function(e,t,r){"use strict";var n=r(51)("wks");var a=r(16);var i=r(1).Symbol;var s=typeof i=="function";var o=e.exports=function(e){return n[e]||(n[e]=s&&i[e]||(s?i:a)("Symbol."+e))};o.store=n},function(e,t,r){"use strict";var n=e.exports={version:"2.5.7"};if(typeof __e=="number")__e=n},function(e,t,r){"use strict";var n=r(1);var a=r(4);var i=r(6);var s=r(14);var o=r(11);var u="prototype";var l=function e(t,r,l){var c=t&e.F;var f=t&e.G;var d=t&e.S;var h=t&e.P;var v=t&e.B;var p=f?n:d?n[r]||(n[r]={}):(n[r]||{})[u];var m=f?a:a[r]||(a[r]={});var g=m[u]||(m[u]={});var b,y,_,A;if(f)l=r;for(b in l){y=!c&&p&&p[b]!==undefined;_=(y?p:l)[b];A=v&&y?o(_,n):h&&typeof _=="function"?o(Function.call,_):_;if(p)s(p,b,_,t&e.U);if(m[b]!=_)i(m,b,A);if(h&&g[b]!=_)g[b]=_}};n.core=a;l.F=1;l.G=2;l.S=4;l.P=8;l.B=16;l.W=32;l.U=64;l.R=128;e.exports=l},function(e,t,r){"use strict";var n=r(13);var a=r(27);e.exports=r(8)?function(e,t,r){return n.f(e,t,a(1,r))}:function(e,t,r){e[t]=r;return e}},function(e,t,r){"use strict";var n=r(2);e.exports=function(e){if(!n(e))throw TypeError(e+" is not an object!");return e}},function(e,t,r){"use strict";e.exports=!r(9)(function(){return Object.defineProperty({},"a",{get:function e(){return 7}}).a!=7})},function(e,t,r){"use strict";e.exports=function(e){try{return!!e()}catch(e){return true}}},function(e,t,r){"use strict";var n={}.hasOwnProperty;e.exports=function(e,t){return n.call(e,t)}},function(e,t,r){"use strict";var n=r(22);e.exports=function(e,t,r){n(e);if(t===undefined)return e;switch(r){case 1:return function(r){return e.call(t,r)};case 2:return function(r,n){return e.call(t,r,n)};case 3:return function(r,n,a){return e.call(t,r,n,a)}}return function(){return e.apply(t,arguments)}}},function(e,t,r){"use strict";var n=r(17);var a=Math.min;e.exports=function(e){return e>0?a(n(e),9007199254740991):0}},function(e,t,r){"use strict";var n=r(7);var a=r(45);var i=r(33);var s=Object.defineProperty;t.f=r(8)?Object.defineProperty:function e(t,r,o){n(t);r=i(r,true);n(o);if(a)try{return s(t,r,o)}catch(e){}if("get"in o||"set"in o)throw TypeError("Accessors not supported!");if("value"in o)t[r]=o.value;return t}},function(e,t,r){"use strict";var n=r(1);var a=r(6);var i=r(10);var s=r(16)("src");var o="toString";var u=Function[o];var l=(""+u).split(o);r(4).inspectSource=function(e){return u.call(e)};(e.exports=function(e,t,r,o){var u=typeof r=="function";if(u)i(r,"name")||a(r,"name",t);if(e[t]===r)return;if(u)i(r,s)||a(r,s,e[t]?""+e[t]:l.join(String(t)));if(e===n){e[t]=r}else if(!o){delete e[t];a(e,t,r)}else if(e[t]){e[t]=r}else{a(e,t,r)}})(Function.prototype,o,function e(){return typeof this=="function"&&this[s]||u.call(this)})},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.DOMSVGFactory=t.DOMCMapReaderFactory=t.DOMCanvasFactory=t.DEFAULT_LINK_REL=t.getDefaultSetting=t.LinkTarget=t.getFilenameFromUrl=t.isValidUrl=t.isExternalLinkTargetSet=t.addLinkAttributes=t.RenderingCancelledException=t.CustomStyle=undefined;var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(t,r,n){if(r)e(t.prototype,r);if(n)e(t,n);return t}}();var a=r(0);var i=r(20);var s=o(i);function o(e){return e&&e.__esModule?e:{default:e}}function u(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var l="noopener noreferrer nofollow";var c="http://www.w3.org/2000/svg";var f=function(){function e(){u(this,e)}n(e,[{key:"create",value:function e(t,r){if(t<=0||r<=0){throw new Error("invalid canvas size")}var n=document.createElement("canvas");var a=n.getContext("2d");n.width=t;n.height=r;return{canvas:n,context:a}}},{key:"reset",value:function e(t,r,n){if(!t.canvas){throw new Error("canvas is not specified")}if(r<=0||n<=0){throw new Error("invalid canvas size")}t.canvas.width=r;t.canvas.height=n}},{key:"destroy",value:function e(t){if(!t.canvas){throw new Error("canvas is not specified")}t.canvas.width=0;t.canvas.height=0;t.canvas=null;t.context=null}}]);return e}();var d=function(){function e(t){var r=t.baseUrl,n=r===undefined?null:r,a=t.isCompressed,i=a===undefined?false:a;u(this,e);this.baseUrl=n;this.isCompressed=i}n(e,[{key:"fetch",value:function e(t){var r=this;var n=t.name;if(!this.baseUrl){return Promise.reject(new Error("CMap baseUrl must be specified, "+'see "PDFJS.cMapUrl" (and also "PDFJS.cMapPacked").'))}if(!n){return Promise.reject(new Error("CMap name must be specified."))}return new Promise(function(e,t){var i=r.baseUrl+n+(r.isCompressed?".bcmap":"");var s=new XMLHttpRequest;s.open("GET",i,true);if(r.isCompressed){s.responseType="arraybuffer"}s.onreadystatechange=function(){if(s.readyState!==XMLHttpRequest.DONE){return}if(s.status===200||s.status===0){var n=void 0;if(r.isCompressed&&s.response){n=new Uint8Array(s.response)}else if(!r.isCompressed&&s.responseText){n=(0,a.stringToBytes)(s.responseText)}if(n){e({cMapData:n,compressionType:r.isCompressed?a.CMapCompressionType.BINARY:a.CMapCompressionType.NONE});return}}t(new Error("Unable to load "+(r.isCompressed?"binary ":"")+"CMap at: "+i))};s.send(null)})}}]);return e}();var h=function(){function e(){u(this,e)}n(e,[{key:"create",value:function e(t,r){(0,a.assert)(t>0&&r>0,"Invalid SVG dimensions");var n=document.createElementNS(c,"svg:svg");n.setAttribute("version","1.1");n.setAttribute("width",t+"px");n.setAttribute("height",r+"px");n.setAttribute("preserveAspectRatio","none");n.setAttribute("viewBox","0 0 "+t+" "+r);return n}},{key:"createElement",value:function e(t){(0,a.assert)(typeof t==="string","Invalid SVG element type");return document.createElementNS(c,t)}}]);return e}();var v=function e(){var t=["ms","Moz","Webkit","O"];var r=Object.create(null);function n(){}n.getProp=function e(n,a){if(arguments.length===1&&typeof r[n]==="string"){return r[n]}a=a||document.documentElement;var i=a.style,s,o;if(typeof i[n]==="string"){return r[n]=n}o=n.charAt(0).toUpperCase()+n.slice(1);for(var u=0,l=t.length;u<l;u++){s=t[u]+o;if(typeof i[s]==="string"){return r[n]=s}}return r[n]="undefined"};n.setProp=function e(t,r,n){var a=this.getProp(t);if(a!=="undefined"){r.style[a]=n}};return n}();var p=function e(){function e(e,t){this.message=e;this.type=t}e.prototype=new Error;e.prototype.name="RenderingCancelledException";e.constructor=e;return e}();var m={NONE:0,SELF:1,BLANK:2,PARENT:3,TOP:4};var g=["","_self","_blank","_parent","_top"];function b(e,t){var r=t&&t.url;e.href=e.title=r?(0,a.removeNullCharacters)(r):"";if(r){var n=t.target;if(typeof n==="undefined"){n=_("externalLinkTarget")}e.target=g[n];var i=t.rel;if(typeof i==="undefined"){i=_("externalLinkRel")}e.rel=i}}function y(e){var t=e.indexOf("#");var r=e.indexOf("?");var n=Math.min(t>0?t:e.length,r>0?r:e.length);return e.substring(e.lastIndexOf("/",n)+1,n)}function _(e){var t=s.default.PDFJS;switch(e){case"pdfBug":return t?t.pdfBug:false;case"disableAutoFetch":return t?t.disableAutoFetch:false;case"disableStream":return t?t.disableStream:false;case"disableRange":return t?t.disableRange:false;case"disableFontFace":return t?t.disableFontFace:false;case"disableCreateObjectURL":return t?t.disableCreateObjectURL:false;case"disableWebGL":return t?t.disableWebGL:true;case"cMapUrl":return t?t.cMapUrl:null;case"cMapPacked":return t?t.cMapPacked:false;case"postMessageTransfers":return t?t.postMessageTransfers:true;case"workerPort":return t?t.workerPort:null;case"workerSrc":return t?t.workerSrc:null;case"disableWorker":return t?t.disableWorker:false;case"maxImageSize":return t?t.maxImageSize:-1;case"imageResourcesPath":return t?t.imageResourcesPath:"";case"isEvalSupported":return t?t.isEvalSupported:true;case"externalLinkTarget":if(!t){return m.NONE}switch(t.externalLinkTarget){case m.NONE:case m.SELF:case m.BLANK:case m.PARENT:case m.TOP:return t.externalLinkTarget}(0,a.warn)("PDFJS.externalLinkTarget is invalid: "+t.externalLinkTarget);t.externalLinkTarget=m.NONE;return m.NONE;case"externalLinkRel":return t?t.externalLinkRel:l;case"enableStats":return!!(t&&t.enableStats);case"pdfjsNext":return!!(t&&t.pdfjsNext);default:throw new Error("Unknown default setting: "+e)}}function A(){var e=_("externalLinkTarget");switch(e){case m.NONE:return false;case m.SELF:case m.BLANK:case m.PARENT:case m.TOP:return true}}function S(e,t){(0,a.deprecated)("isValidUrl(), please use createValidAbsoluteUrl() instead.");var r=t?"http://example.com":null;return(0,a.createValidAbsoluteUrl)(e,r)!==null}t.CustomStyle=v;t.RenderingCancelledException=p;t.addLinkAttributes=b;t.isExternalLinkTargetSet=A;t.isValidUrl=S;t.getFilenameFromUrl=y;t.LinkTarget=m;t.getDefaultSetting=_;t.DEFAULT_LINK_REL=l;t.DOMCanvasFactory=f;t.DOMCMapReaderFactory=d;t.DOMSVGFactory=h},function(e,t,r){"use strict";var n=0;var a=Math.random();e.exports=function(e){return"Symbol(".concat(e===undefined?"":e,")_",(++n+a).toString(36))}},function(e,t,r){"use strict";var n=Math.ceil;var a=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(e>0?a:n)(e)}},function(e,t,r){"use strict";var n=r(35);e.exports=function(e){return Object(n(e))}},function(e,t,r){"use strict";e.exports={}},function(e,t,r){"use strict";e.exports=typeof window!=="undefined"&&window.Math===Math?window:typeof global!=="undefined"&&global.Math===Math?global:typeof self!=="undefined"&&self.Math===Math?self:{}},function(e,t,r){"use strict";e.exports=false},function(e,t,r){"use strict";e.exports=function(e){if(typeof e!="function")throw TypeError(e+" is not a function!");return e}},function(e,t,r){"use strict";var n=r(14);e.exports=function(e,t,r){for(var a in t){n(e,a,t[a],r)}return e}},function(e,t,r){"use strict";e.exports=function(e,t,r,n){if(!(e instanceof t)||n!==undefined&&n in e){throw TypeError(r+": incorrect invocation!")}return e}},function(e,t,r){"use strict";var n={}.toString;e.exports=function(e){return n.call(e).slice(8,-1)}},function(e,t,r){"use strict";var n=r(13).f;var a=r(10);var i=r(3)("toStringTag");e.exports=function(e,t,r){if(e&&!a(e=r?e:e.prototype,i))n(e,i,{configurable:true,value:t})}},function(e,t,r){"use strict";e.exports=function(e,t){return{enumerable:!(e&1),configurable:!(e&2),writable:!(e&4),value:t}}},function(e,t,r){"use strict";var n=r(34);var a=r(35);e.exports=function(e){return n(a(e))}},function(e,t,r){"use strict";var n=r(17);var a=Math.max;var i=Math.min;e.exports=function(e,t){e=n(e);return e<0?a(e+t,0):i(e,t)}},function(e,t,r){"use strict";var n=r(25);var a=r(3)("toStringTag");var i=n(function(){return arguments}())=="Arguments";var s=function e(t,r){try{return t[r]}catch(e){}};e.exports=function(e){var t,r,o;return e===undefined?"Undefined":e===null?"Null":typeof(r=s(t=Object(e),a))=="string"?r:i?n(t):(o=n(t))=="Object"&&typeof t.callee=="function"?"Arguments":o}},function(e,t,r){"use strict";var n=r(11);var a=r(98);var i=r(53);var s=r(7);var o=r(12);var u=r(57);var l={};var c={};var f=e.exports=function(e,t,r,f,d){var h=d?function(){return e}:u(e);var v=n(r,f,t?2:1);var p=0;var m,g,b,y;if(typeof h!="function")throw TypeError(e+" is not iterable!");if(i(h))for(m=o(e.length);m>p;p++){y=t?v(s(g=e[p])[0],g[1]):v(e[p]);if(y===l||y===c)return y}else for(b=h.call(e);!(g=b.next()).done;){y=a(b,v,g.value,t);if(y===l||y===c)return y}};f.BREAK=l;f.RETURN=c},function(e,t,r){"use strict";var n=r(2);var a=r(1).document;var i=n(a)&&n(a.createElement);e.exports=function(e){return i?a.createElement(e):{}}},function(e,t,r){"use strict";var n=r(2);e.exports=function(e,t){if(!n(e))return e;var r,a;if(t&&typeof(r=e.toString)=="function"&&!n(a=r.call(e)))return a;if(typeof(r=e.valueOf)=="function"&&!n(a=r.call(e)))return a;if(!t&&typeof(r=e.toString)=="function"&&!n(a=r.call(e)))return a;throw TypeError("Can't convert object to primitive value")}},function(e,t,r){"use strict";var n=r(25);e.exports=Object("z").propertyIsEnumerable(0)?Object:function(e){return n(e)=="String"?e.split(""):Object(e)}},function(e,t,r){"use strict";e.exports=function(e){if(e==undefined)throw TypeError("Can't call method on  "+e);return e}},function(e,t,r){"use strict";var n=r(51)("keys");var a=r(16);e.exports=function(e){return n[e]||(n[e]=a(e))}},function(e,t,r){"use strict";e.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},function(e,t,r){"use strict";var n=r(49);var a=r(37);e.exports=Object.keys||function e(t){return n(t,a)}},function(e,t,r){"use strict";var n=r(11);var a=r(34);var i=r(18);var s=r(12);var o=r(82);e.exports=function(e,t){var r=e==1;var u=e==2;var l=e==3;var c=e==4;var f=e==6;var d=e==5||f;var h=t||o;return function(t,o,v){var p=i(t);var m=a(p);var g=n(o,v,3);var b=s(m.length);var y=0;var _=r?h(t,b):u?h(t,0):undefined;var A,S;for(;b>y;y++){if(d||y in m){A=m[y];S=g(A,y,p);if(e){if(r)_[y]=S;else if(S)switch(e){case 3:return true;case 5:return A;case 6:return y;case 2:_.push(A)}else if(c)return false}}}return f?-1:l||c?c:_}}},function(e,t,r){"use strict";var n=r(7);var a=r(22);var i=r(3)("species");e.exports=function(e,t){var r=n(e).constructor;var s;return r===undefined||(s=n(r)[i])==undefined?t:a(s)}},function(e,t,r){"use strict";var n=r(3)("iterator");var a=false;try{var i=[7][n]();i["return"]=function(){a=true};Array.from(i,function(){throw 2})}catch(e){}e.exports=function(e,t){if(!t&&!a)return false;var r=false;try{var i=[7];var s=i[n]();s.next=function(){return{done:r=true}};i[n]=function(){return s};e(i)}catch(e){}return r}},function(e,t,r){"use strict";var n=r(22);function a(e){var t,r;this.promise=new e(function(e,n){if(t!==undefined||r!==undefined)throw TypeError("Bad Promise constructor");t=e;r=n});this.resolve=n(t);this.reject=n(r)}e.exports.f=function(e){return new a(e)}},function(e,t,r){"use strict";var n=typeof Symbol==="function"&&typeof Symbol.iterator==="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol==="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};var a=r(16)("meta");var i=r(2);var s=r(10);var o=r(13).f;var u=0;var l=Object.isExtensible||function(){return true};var c=!r(9)(function(){return l(Object.preventExtensions({}))});var f=function e(t){o(t,a,{value:{i:"O"+ ++u,w:{}}})};var d=function e(t,r){if(!i(t))return(typeof t==="undefined"?"undefined":n(t))=="symbol"?t:(typeof t=="string"?"S":"P")+t;if(!s(t,a)){if(!l(t))return"F";if(!r)return"E";f(t)}return t[a].i};var h=function e(t,r){if(!s(t,a)){if(!l(t))return true;if(!r)return false;f(t)}return t[a].w};var v=function e(t){if(c&&p.NEED&&l(t)&&!s(t,a))f(t);return t};var p=e.exports={KEY:a,NEED:false,fastKey:d,getWeak:h,onFreeze:v}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.validateResponseStatus=t.validateRangeRequestCapabilities=t.createResponseStatusError=undefined;var n=r(0);function a(e){var t=e.getResponseHeader,r=e.isHttp,a=e.rangeChunkSize,i=e.disableRange;(0,n.assert)(a>0);var s={allowRangeRequests:false,suggestedLength:undefined};if(i||!r){return s}if(t("Accept-Ranges")!=="bytes"){return s}var o=t("Content-Encoding")||"identity";if(o!=="identity"){return s}var u=parseInt(t("Content-Length"),10);if(!Number.isInteger(u)){return s}s.suggestedLength=u;if(u<=2*a){return s}s.allowRangeRequests=true;return s}function i(e,t){if(e===404||e===0&&/^file:/.test(t)){return new n.MissingPDFException('Missing PDF "'+t+'".')}return new n.UnexpectedResponseException("Unexpected server response ("+e+') while retrieving PDF "'+t+'".',e)}function s(e){return e===200||e===206}t.createResponseStatusError=i;t.validateRangeRequestCapabilities=a;t.validateResponseStatus=s},function(e,t,r){"use strict";e.exports=!r(8)&&!r(9)(function(){return Object.defineProperty(r(32)("div"),"a",{get:function e(){return 7}}).a!=7})},function(e,t,r){"use strict";var n=r(1);var a=r(6);var i=r(16);var s=i("typed_array");var o=i("view");var u=!!(n.ArrayBuffer&&n.DataView);var l=u;var c=0;var f=9;var d;var h="Int8Array,Uint8Array,Uint8ClampedArray,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array".split(",");while(c<f){if(d=n[h[c++]]){a(d.prototype,s,true);a(d.prototype,o,true)}else l=false}e.exports={ABV:u,CONSTR:l,TYPED:s,VIEW:o}},function(e,t,r){"use strict";var n=r(17);var a=r(12);e.exports=function(e){if(e===undefined)return 0;var t=n(e);var r=a(t);if(t!==r)throw RangeError("Wrong length!");return r}},function(e,t,r){"use strict";var n=r(49);var a=r(37).concat("length","prototype");t.f=Object.getOwnPropertyNames||function e(t){return n(t,a)}},function(e,t,r){"use strict";var n=r(10);var a=r(28);var i=r(50)(false);var s=r(36)("IE_PROTO");e.exports=function(e,t){var r=a(e);var o=0;var u=[];var l;for(l in r){if(l!=s)n(r,l)&&u.push(l)}while(t.length>o){if(n(r,l=t[o++])){~i(u,l)||u.push(l)}}return u}},function(e,t,r){"use strict";var n=r(28);var a=r(12);var i=r(29);e.exports=function(e){return function(t,r,s){var o=n(t);var u=a(o.length);var l=i(s,u);var c;if(e&&r!=r)while(u>l){c=o[l++];if(c!=c)return true}else for(;u>l;l++){if(e||l in o){if(o[l]===r)return e||l||0}}return!e&&-1}}},function(e,t,r){"use strict";var n=r(4);var a=r(1);var i="__core-js_shared__";var s=a[i]||(a[i]={});(e.exports=function(e,t){return s[e]||(s[e]=t!==undefined?t:{})})("versions",[]).push({version:n.version,mode:r(21)?"pure":"global",copyright:"© 2018 Denis Pushkarev (zloirock.ru)"})},function(e,t,r){"use strict";var n=r(18);var a=r(29);var i=r(12);e.exports=function e(t){var r=n(this);var s=i(r.length);var o=arguments.length;var u=a(o>1?arguments[1]:undefined,s);var l=o>2?arguments[2]:undefined;var c=l===undefined?s:a(l,s);while(c>u){r[u++]=t}return r}},function(e,t,r){"use strict";var n=r(19);var a=r(3)("iterator");var i=Array.prototype;e.exports=function(e){return e!==undefined&&(n.Array===e||i[a]===e)}},function(e,t,r){"use strict";var n=r(7);var a=r(81);var i=r(37);var s=r(36)("IE_PROTO");var o=function e(){};var u="prototype";var l=function e(){var t=r(32)("iframe");var n=i.length;var a="<";var s=">";var o;t.style.display="none";r(55).appendChild(t);t.src="javascript:";o=t.contentWindow.document;o.open();o.write(a+"script"+s+"document.F=Object"+a+"/script"+s);o.close();l=o.F;while(n--){delete l[u][i[n]]}return l()};e.exports=Object.create||function e(t,r){var i;if(t!==null){o[u]=n(t);i=new o;o[u]=null;i[s]=t}else i=l();return r===undefined?i:a(i,r)}},function(e,t,r){"use strict";var n=r(1).document;e.exports=n&&n.documentElement},function(e,t,r){"use strict";var n=r(10);var a=r(18);var i=r(36)("IE_PROTO");var s=Object.prototype;e.exports=Object.getPrototypeOf||function(e){e=a(e);if(n(e,i))return e[i];if(typeof e.constructor=="function"&&e instanceof e.constructor){return e.constructor.prototype}return e instanceof Object?s:null}},function(e,t,r){"use strict";var n=r(30);var a=r(3)("iterator");var i=r(19);e.exports=r(4).getIteratorMethod=function(e){if(e!=undefined)return e[a]||e["@@iterator"]||i[n(e)]}},function(e,t,r){"use strict";var n=r(85);var a=r(86);var i=r(19);var s=r(28);e.exports=r(59)(Array,"Array",function(e,t){this._t=s(e);this._i=0;this._k=t},function(){var e=this._t;var t=this._k;var r=this._i++;if(!e||r>=e.length){this._t=undefined;return a(1)}if(t=="keys")return a(0,r);if(t=="values")return a(0,e[r]);return a(0,[r,e[r]])},"values");i.Arguments=i.Array;n("keys");n("values");n("entries")},function(e,t,r){"use strict";var n=r(21);var a=r(5);var i=r(14);var s=r(6);var o=r(19);var u=r(87);var l=r(26);var c=r(56);var f=r(3)("iterator");var d=!([].keys&&"next"in[].keys());var h="@@iterator";var v="keys";var p="values";var m=function e(){return this};e.exports=function(e,t,r,g,b,y,_){u(r,t,g);var A=function e(t){if(!d&&t in k)return k[t];switch(t){case v:return function e(){return new r(this,t)};case p:return function e(){return new r(this,t)}}return function e(){return new r(this,t)}};var S=t+" Iterator";var w=b==p;var P=false;var k=e.prototype;var C=k[f]||k[h]||b&&k[b];var R=C||A(b);var x=b?!w?R:A("entries"):undefined;var T=t=="Array"?k.entries||C:C;var E,O,L;if(T){L=c(T.call(new e));if(L!==Object.prototype&&L.next){l(L,S,true);if(!n&&typeof L[f]!="function")s(L,f,m)}}if(w&&C&&C.name!==p){P=true;R=function e(){return C.call(this)}}if((!n||_)&&(d||P||!k[f])){s(k,f,R)}o[t]=R;o[S]=m;if(b){E={values:w?R:A(p),keys:y?R:A(v),entries:x};if(_)for(O in E){if(!(O in k))i(k,O,E[O])}else a(a.P+a.F*(d||P),t,E)}return E}},function(e,t,r){"use strict";var n=r(1);var a=r(13);var i=r(8);var s=r(3)("species");e.exports=function(e){var t=n[e];if(i&&t&&!t[s])a.f(t,s,{configurable:true,get:function e(){return this}})}},function(e,t,r){"use strict";var n=r(62);var a=r(27);var i=r(28);var s=r(33);var o=r(10);var u=r(45);var l=Object.getOwnPropertyDescriptor;t.f=r(8)?l:function e(t,r){t=i(t);r=s(r,true);if(u)try{return l(t,r)}catch(e){}if(o(t,r))return a(!n.f.call(t,r),t[r])}},function(e,t,r){"use strict";t.f={}.propertyIsEnumerable},function(e,t,r){"use strict";var n=r(30);var a={};a[r(3)("toStringTag")]="z";if(a+""!="[object z]"){r(14)(Object.prototype,"toString",function e(){return"[object "+n(this)+"]"},true)}},function(e,t,r){"use strict";var n=r(58);var a=r(38);var i=r(14);var s=r(1);var o=r(6);var u=r(19);var l=r(3);var c=l("iterator");var f=l("toStringTag");var d=u.Array;var h={CSSRuleList:true,CSSStyleDeclaration:false,CSSValueList:false,ClientRectList:false,DOMRectList:false,DOMStringList:false,DOMTokenList:true,DataTransferItemList:false,FileList:false,HTMLAllCollection:false,HTMLCollection:false,HTMLFormElement:false,HTMLSelectElement:false,MediaList:true,MimeTypeArray:false,NamedNodeMap:false,NodeList:true,PaintRequestList:false,Plugin:false,PluginArray:false,SVGLengthList:false,SVGNumberList:false,SVGPathSegList:false,SVGPointList:false,SVGStringList:false,SVGTransformList:false,SourceBufferList:false,StyleSheetList:true,TextTrackCueList:false,TextTrackList:false,TouchList:false};for(var v=a(h),p=0;p<v.length;p++){var m=v[p];var g=h[m];var b=s[m];var y=b&&b.prototype;var _;if(y){if(!y[c])o(y,c,d);if(!y[f])o(y,f,m);u[m]=d;if(g)for(_ in n){if(!y[_])i(y,_,n[_],true)}}}},function(e,t,r){"use strict";var n=r(11);var a=r(99);var i=r(55);var s=r(32);var o=r(1);var u=o.process;var l=o.setImmediate;var c=o.clearImmediate;var f=o.MessageChannel;var d=o.Dispatch;var h=0;var v={};var p="onreadystatechange";var m,g,b;var y=function e(){var t=+this;if(v.hasOwnProperty(t)){var r=v[t];delete v[t];r()}};var _=function e(t){y.call(t.data)};if(!l||!c){l=function e(t){var r=[];var n=1;while(arguments.length>n){r.push(arguments[n++])}v[++h]=function(){a(typeof t=="function"?t:Function(t),r)};m(h);return h};c=function e(t){delete v[t]};if(r(25)(u)=="process"){m=function e(t){u.nextTick(n(y,t,1))}}else if(d&&d.now){m=function e(t){d.now(n(y,t,1))}}else if(f){g=new f;b=g.port2;g.port1.onmessage=_;m=n(b.postMessage,b,1)}else if(o.addEventListener&&typeof postMessage=="function"&&!o.importScripts){m=function e(t){o.postMessage(t+"","*")};o.addEventListener("message",_,false)}else if(p in s("script")){m=function e(t){i.appendChild(s("script"))[p]=function(){i.removeChild(this);y.call(t)}}}else{m=function e(t){setTimeout(n(y,t,1),0)}}}e.exports={set:l,clear:c}},function(e,t,r){"use strict";e.exports=function(e){try{return{e:false,v:e()}}catch(e){return{e:true,v:e}}}},function(e,t,r){"use strict";var n=r(7);var a=r(2);var i=r(42);e.exports=function(e,t){n(e);if(a(t)&&t.constructor===e)return t;var r=i.f(e);var s=r.resolve;s(t);return r.promise}},function(e,t,r){"use strict";var n=r(2);e.exports=function(e,t){if(!n(e)||e._t!==t)throw TypeError("Incompatible receiver, "+t+" required!");return e}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.build=t.version=t._UnsupportedManager=t.setPDFNetworkStreamClass=t.PDFPageProxy=t.PDFDocumentProxy=t.PDFWorker=t.PDFDataRangeTransport=t.LoopbackPort=t.getDocument=undefined;var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(t,r,n){if(r)e(t.prototype,r);if(n)e(t,n);return t}}();var a=typeof Symbol==="function"&&typeof Symbol.iterator==="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol==="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};var i=r(0);var s=r(15);var o=r(119);var u=r(120);var l=r(20);var c=h(l);var f=r(71);var d=r(123);function h(e){return e&&e.__esModule?e:{default:e}}function v(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var p=65536;var m=false;var g;var b=false;var y=typeof document!=="undefined"&&document.currentScript?document.currentScript.src:null;var _=null;var A=false;{if(typeof window==="undefined"){m=true;if(typeof require.ensure==="undefined"){require.ensure=require("node-ensure")}A=true}else if(typeof require!=="undefined"&&typeof require.ensure==="function"){A=true}if(typeof requirejs!=="undefined"&&requirejs.toUrl){g=requirejs.toUrl("pdfjs-dist/build/pdf.worker.js")}var S=typeof requirejs!=="undefined"&&requirejs.load;_=A?function(e){require.ensure([],function(){var t;t=require("./pdf.worker.js");e(t.WorkerMessageHandler)})}:S?function(e){requirejs(["pdfjs-dist/build/pdf.worker"],function(t){e(t.WorkerMessageHandler)})}:null}var w;function P(e){w=e}function k(e,t,r,n){var o=new R;if(arguments.length>1){(0,i.deprecated)("getDocument is called with pdfDataRangeTransport, "+"passwordCallback or progressCallback argument")}if(t){if(!(t instanceof x)){t=Object.create(t);t.length=e.length;t.initialData=e.initialData;if(!t.abort){t.abort=function(){}}}e=Object.create(e);e.range=t}o.onPassword=r||null;o.onProgress=n||null;var u;if(typeof e==="string"){u={url:e}}else if((0,i.isArrayBuffer)(e)){u={data:e}}else if(e instanceof x){u={range:e}}else{if((typeof e==="undefined"?"undefined":a(e))!=="object"){throw new Error("Invalid parameter in getDocument, "+"need either Uint8Array, string or a parameter object")}if(!e.url&&!e.data&&!e.range){throw new Error("Invalid parameter object: need either .data, .range or .url")}u=e}var l={};var c=null;var f=null;var h=s.DOMCMapReaderFactory;for(var v in u){if(v==="url"&&typeof window!=="undefined"){l[v]=new URL(u[v],window.location).href;continue}else if(v==="range"){c=u[v];continue}else if(v==="worker"){f=u[v];continue}else if(v==="data"&&!(u[v]instanceof Uint8Array)){var m=u[v];if(typeof m==="string"){l[v]=(0,i.stringToBytes)(m)}else if((typeof m==="undefined"?"undefined":a(m))==="object"&&m!==null&&!isNaN(m.length)){l[v]=new Uint8Array(m)}else if((0,i.isArrayBuffer)(m)){l[v]=new Uint8Array(m)}else{throw new Error("Invalid PDF binary data: either typed array, "+"string or array-like object is expected in the "+"data property.")}continue}else if(v==="CMapReaderFactory"){h=u[v];continue}l[v]=u[v]}l.rangeChunkSize=l.rangeChunkSize||p;l.ignoreErrors=l.stopAtErrors!==true;if(l.disableNativeImageDecoder!==undefined){(0,i.deprecated)("parameter disableNativeImageDecoder, "+"use nativeImageDecoderSupport instead")}l.nativeImageDecoderSupport=l.nativeImageDecoderSupport||(l.disableNativeImageDecoder===true?i.NativeImageDecoding.NONE:i.NativeImageDecoding.DECODE);if(l.nativeImageDecoderSupport!==i.NativeImageDecoding.DECODE&&l.nativeImageDecoderSupport!==i.NativeImageDecoding.NONE&&l.nativeImageDecoderSupport!==i.NativeImageDecoding.DISPLAY){(0,i.warn)("Invalid parameter nativeImageDecoderSupport: "+"need a state of enum {NativeImageDecoding}");l.nativeImageDecoderSupport=i.NativeImageDecoding.DECODE}if(!f){var g=(0,s.getDefaultSetting)("workerPort");f=g?L.fromPort(g):new L;o._worker=f}var b=o.docId;f.promise.then(function(){if(o.destroyed){throw new Error("Loading aborted")}return C(f,l,c,b).then(function(e){if(o.destroyed){throw new Error("Loading aborted")}var t=void 0;if(c){t=new d.PDFDataTransportStream(l,c)}else if(!l.data){t=new w({source:l,disableRange:(0,s.getDefaultSetting)("disableRange")})}var r=new i.MessageHandler(b,e,f.port);r.postMessageTransfers=f.postMessageTransfers;var n=new I(r,o,t,h);o._transport=n;r.send("Ready",null)})}).catch(o._capability.reject);return o}function C(e,t,r,n){if(e.destroyed){return Promise.reject(new Error("Worker was destroyed"))}var a="1.10.100";t.disableAutoFetch=(0,s.getDefaultSetting)("disableAutoFetch");t.disableStream=(0,s.getDefaultSetting)("disableStream");t.chunkedViewerLoading=!!r;if(r){t.length=r.length;t.initialData=r.initialData}return e.messageHandler.sendWithPromise("GetDocRequest",{docId:n,apiVersion:a,source:{data:t.data,url:t.url,password:t.password,disableAutoFetch:t.disableAutoFetch,rangeChunkSize:t.rangeChunkSize,length:t.length},maxImageSize:(0,s.getDefaultSetting)("maxImageSize"),disableFontFace:(0,s.getDefaultSetting)("disableFontFace"),disableCreateObjectURL:(0,s.getDefaultSetting)("disableCreateObjectURL"),postMessageTransfers:(0,s.getDefaultSetting)("postMessageTransfers")&&!b,docBaseUrl:t.docBaseUrl,nativeImageDecoderSupport:t.nativeImageDecoderSupport,ignoreErrors:t.ignoreErrors,isEvalSupported:(0,s.getDefaultSetting)("isEvalSupported")}).then(function(t){if(e.destroyed){throw new Error("Worker was destroyed")}return t})}var R=function e(){var t=0;function r(){this._capability=(0,i.createPromiseCapability)();this._transport=null;this._worker=null;this.docId="d"+t++;this.destroyed=false;this.onPassword=null;this.onProgress=null;this.onUnsupportedFeature=null}r.prototype={get promise(){return this._capability.promise},destroy:function e(){var t=this;this.destroyed=true;var r=!this._transport?Promise.resolve():this._transport.destroy();return r.then(function(){t._transport=null;if(t._worker){t._worker.destroy();t._worker=null}})},then:function e(t,r){return this.promise.then.apply(this.promise,arguments)}};return r}();var x=function e(){function t(e,t){this.length=e;this.initialData=t;this._rangeListeners=[];this._progressListeners=[];this._progressiveReadListeners=[];this._readyCapability=(0,i.createPromiseCapability)()}t.prototype={addRangeListener:function e(t){this._rangeListeners.push(t)},addProgressListener:function e(t){this._progressListeners.push(t)},addProgressiveReadListener:function e(t){this._progressiveReadListeners.push(t)},onDataRange:function e(t,r){var n=this._rangeListeners;for(var a=0,i=n.length;a<i;++a){n[a](t,r)}},onDataProgress:function e(t){var r=this;this._readyCapability.promise.then(function(){var e=r._progressListeners;for(var n=0,a=e.length;n<a;++n){e[n](t)}})},onDataProgressiveRead:function e(t){var r=this;this._readyCapability.promise.then(function(){var e=r._progressiveReadListeners;for(var n=0,a=e.length;n<a;++n){e[n](t)}})},transportReady:function e(){this._readyCapability.resolve()},requestDataRange:function e(t,r){throw new Error("Abstract method PDFDataRangeTransport.requestDataRange")},abort:function e(){}};return t}();var T=function e(){function t(e,t,r){this.pdfInfo=e;this.transport=t;this.loadingTask=r}t.prototype={get numPages(){return this.pdfInfo.numPages},get fingerprint(){return this.pdfInfo.fingerprint},getPage:function e(t){return this.transport.getPage(t)},getPageIndex:function e(t){return this.transport.getPageIndex(t)},getDestinations:function e(){return this.transport.getDestinations()},getDestination:function e(t){return this.transport.getDestination(t)},getPageLabels:function e(){return this.transport.getPageLabels()},getPageMode:function e(){return this.transport.getPageMode()},getAttachments:function e(){return this.transport.getAttachments()},getJavaScript:function e(){return this.transport.getJavaScript()},getOutline:function e(){return this.transport.getOutline()},getMetadata:function e(){return this.transport.getMetadata()},getData:function e(){return this.transport.getData()},getDownloadInfo:function e(){return this.transport.downloadInfoCapability.promise},getStats:function e(){return this.transport.getStats()},cleanup:function e(){this.transport.startCleanup()},destroy:function e(){return this.loadingTask.destroy()}};return t}();var E=function e(){function t(e,t,r){this.pageIndex=e;this.pageInfo=t;this.transport=r;this.stats=new i.StatTimer;this.stats.enabled=(0,s.getDefaultSetting)("enableStats");this.commonObjs=r.commonObjs;this.objs=new j;this.cleanupAfterRender=false;this.pendingCleanup=false;this.intentStates=Object.create(null);this.destroyed=false}t.prototype={get pageNumber(){return this.pageIndex+1},get rotate(){return this.pageInfo.rotate},get ref(){return this.pageInfo.ref},get userUnit(){return this.pageInfo.userUnit},get view(){return this.pageInfo.view},getViewport:function e(t,r){if(arguments.length<2){r=this.rotate}return new i.PageViewport(this.view,t,r,0,0)},getAnnotations:function e(t){var r=t&&t.intent||null;if(!this.annotationsPromise||this.annotationsIntent!==r){this.annotationsPromise=this.transport.getAnnotations(this.pageIndex,r);this.annotationsIntent=r}return this.annotationsPromise},render:function e(t){var r=this;var n=this.stats;n.time("Overall");this.pendingCleanup=false;var a=t.intent==="print"?"print":"display";var o=t.canvasFactory||new s.DOMCanvasFactory;if(!this.intentStates[a]){this.intentStates[a]=Object.create(null)}var u=this.intentStates[a];if(!u.displayReadyCapability){u.receivingOperatorList=true;u.displayReadyCapability=(0,i.createPromiseCapability)();u.operatorList={fnArray:[],argsArray:[],lastChunk:false};this.stats.time("Page Request");this.transport.messageHandler.send("RenderPageRequest",{pageIndex:this.pageNumber-1,intent:a,renderInteractiveForms:t.renderInteractiveForms===true})}var l=function e(t){var a=u.renderTasks.indexOf(c);if(a>=0){u.renderTasks.splice(a,1)}if(r.cleanupAfterRender){r.pendingCleanup=true}r._tryCleanup();if(t){c.capability.reject(t)}else{c.capability.resolve()}n.timeEnd("Rendering");n.timeEnd("Overall")};var c=new D(l,t,this.objs,this.commonObjs,u.operatorList,this.pageNumber,o);c.useRequestAnimationFrame=a!=="print";if(!u.renderTasks){u.renderTasks=[]}u.renderTasks.push(c);var f=c.task;if(t.continueCallback){(0,i.deprecated)("render is used with continueCallback parameter");f.onContinue=t.continueCallback}u.displayReadyCapability.promise.then(function(e){if(r.pendingCleanup){l();return}n.time("Rendering");c.initializeGraphics(e);c.operatorListChanged()}).catch(l);return f},getOperatorList:function e(){function t(){if(n.operatorList.lastChunk){n.opListReadCapability.resolve(n.operatorList);var e=n.renderTasks.indexOf(a);if(e>=0){n.renderTasks.splice(e,1)}}}var r="oplist";if(!this.intentStates[r]){this.intentStates[r]=Object.create(null)}var n=this.intentStates[r];var a;if(!n.opListReadCapability){a={};a.operatorListChanged=t;n.receivingOperatorList=true;n.opListReadCapability=(0,i.createPromiseCapability)();n.renderTasks=[];n.renderTasks.push(a);n.operatorList={fnArray:[],argsArray:[],lastChunk:false};this.transport.messageHandler.send("RenderPageRequest",{pageIndex:this.pageIndex,intent:r})}return n.opListReadCapability.promise},streamTextContent:function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};var r=100;return this.transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this.pageNumber-1,normalizeWhitespace:t.normalizeWhitespace===true,combineTextItems:t.disableCombineTextItems!==true},{highWaterMark:r,size:function e(t){return t.items.length}})},getTextContent:function e(t){t=t||{};var r=this.streamTextContent(t);return new Promise(function(e,t){function n(){a.read().then(function(t){var r=t.value,a=t.done;if(a){e(s);return}i.Util.extendObj(s.styles,r.styles);i.Util.appendToArray(s.items,r.items);n()},t)}var a=r.getReader();var s={items:[],styles:Object.create(null)};n()})},_destroy:function e(){this.destroyed=true;this.transport.pageCache[this.pageIndex]=null;var t=[];Object.keys(this.intentStates).forEach(function(e){if(e==="oplist"){return}var r=this.intentStates[e];r.renderTasks.forEach(function(e){var r=e.capability.promise.catch(function(){});t.push(r);e.cancel()})},this);this.objs.clear();this.annotationsPromise=null;this.pendingCleanup=false;return Promise.all(t)},destroy:function e(){(0,i.deprecated)("page destroy method, use cleanup() instead");this.cleanup()},cleanup:function e(){this.pendingCleanup=true;this._tryCleanup()},_tryCleanup:function e(){if(!this.pendingCleanup||Object.keys(this.intentStates).some(function(e){var t=this.intentStates[e];return t.renderTasks.length!==0||t.receivingOperatorList},this)){return}Object.keys(this.intentStates).forEach(function(e){delete this.intentStates[e]},this);this.objs.clear();this.annotationsPromise=null;this.pendingCleanup=false},_startRenderPage:function e(t,r){var n=this.intentStates[r];if(n.displayReadyCapability){n.displayReadyCapability.resolve(t)}},_renderPageChunk:function e(t,r){var n=this.intentStates[r];var a,i;for(a=0,i=t.length;a<i;a++){n.operatorList.fnArray.push(t.fnArray[a]);n.operatorList.argsArray.push(t.argsArray[a])}n.operatorList.lastChunk=t.lastChunk;for(a=0;a<n.renderTasks.length;a++){n.renderTasks[a].operatorListChanged()}if(t.lastChunk){n.receivingOperatorList=false;this._tryCleanup()}}};return t}();var O=function(){function e(t){v(this,e);this._listeners=[];this._defer=t;this._deferred=Promise.resolve(undefined)}n(e,[{key:"postMessage",value:function e(t,r){var n=this;function s(e){if((typeof e==="undefined"?"undefined":a(e))!=="object"||e===null){return e}if(o.has(e)){return o.get(e)}var t;var n;if((n=e.buffer)&&(0,i.isArrayBuffer)(n)){var u=r&&r.indexOf(n)>=0;if(e===n){t=e}else if(u){t=new e.constructor(n,e.byteOffset,e.byteLength)}else{t=new e.constructor(e)}o.set(e,t);return t}t=Array.isArray(e)?[]:{};o.set(e,t);for(var l in e){var c,f=e;while(!(c=Object.getOwnPropertyDescriptor(f,l))){f=Object.getPrototypeOf(f)}if(typeof c.value==="undefined"||typeof c.value==="function"){continue}t[l]=s(c.value)}return t}if(!this._defer){this._listeners.forEach(function(e){e.call(this,{data:t})},this);return}var o=new WeakMap;var u={data:s(t)};this._deferred.then(function(){n._listeners.forEach(function(e){e.call(this,u)},n)})}},{key:"addEventListener",value:function e(t,r){this._listeners.push(r)}},{key:"removeEventListener",value:function e(t,r){var n=this._listeners.indexOf(r);this._listeners.splice(n,1)}},{key:"terminate",value:function e(){this._listeners=[]}}]);return e}();var L=function e(){var t=0;function r(){if(typeof g!=="undefined"){return g}if((0,s.getDefaultSetting)("workerSrc")){return(0,s.getDefaultSetting)("workerSrc")}if(y){return y.replace(/(\.(?:min\.)?js)(\?.*)?$/i,".worker$1$2")}throw new Error("No PDFJS.workerSrc specified")}var n=void 0;function a(){var e;if(n){return n.promise}n=(0,i.createPromiseCapability)();var t=_||function(e){i.Util.loadScript(r(),function(){e(window.pdfjsDistBuildPdfWorker.WorkerMessageHandler)})};t(n.resolve);return n.promise}function o(e){var t="importScripts('"+e+"');";return URL.createObjectURL(new Blob([t]))}var u=new WeakMap;function l(e,t){if(t&&u.has(t)){throw new Error("Cannot use more than one PDFWorker per port")}this.name=e;this.destroyed=false;this.postMessageTransfers=true;this._readyCapability=(0,i.createPromiseCapability)();this._port=null;this._webWorker=null;this._messageHandler=null;if(t){u.set(t,this);this._initializeFromPort(t);return}this._initialize()}l.prototype={get promise(){return this._readyCapability.promise},get port(){return this._port},get messageHandler(){return this._messageHandler},_initializeFromPort:function e(t){this._port=t;this._messageHandler=new i.MessageHandler("main","worker",t);this._messageHandler.on("ready",function(){});this._readyCapability.resolve()},_initialize:function e(){var t=this;if(!m&&!(0,s.getDefaultSetting)("disableWorker")&&typeof Worker!=="undefined"){var n=r();try{if(!(0,i.isSameOrigin)(window.location.href,n)){n=o(new URL(n,window.location).href)}var a=new Worker(n);var u=new i.MessageHandler("main","worker",a);var l=function e(){a.removeEventListener("error",c);u.destroy();a.terminate();if(t.destroyed){t._readyCapability.reject(new Error("Worker was destroyed"))}else{t._setupFakeWorker()}};var c=function e(){if(!t._webWorker){l()}};a.addEventListener("error",c);u.on("test",function(e){a.removeEventListener("error",c);if(t.destroyed){l();return}var r=e&&e.supportTypedArray;if(r){t._messageHandler=u;t._port=a;t._webWorker=a;if(!e.supportTransfers){t.postMessageTransfers=false;b=true}t._readyCapability.resolve();u.send("configure",{verbosity:(0,i.getVerbosityLevel)()})}else{t._setupFakeWorker();u.destroy();a.terminate()}});u.on("console_log",function(e){console.log.apply(console,e)});u.on("console_error",function(e){console.error.apply(console,e)});u.on("ready",function(e){a.removeEventListener("error",c);if(t.destroyed){l();return}try{f()}catch(e){t._setupFakeWorker()}});var f=function e(){var t=(0,s.getDefaultSetting)("postMessageTransfers")&&!b;var r=new Uint8Array([t?255:0]);try{u.send("test",r,[r.buffer])}catch(e){(0,i.info)("Cannot use postMessage transfers");r[0]=0;u.send("test",r)}};f();return}catch(e){(0,i.info)("The worker has been disabled.")}}this._setupFakeWorker()},_setupFakeWorker:function e(){var r=this;if(!m&&!(0,s.getDefaultSetting)("disableWorker")){(0,i.warn)("Setting up fake worker.");m=true}a().then(function(e){if(r.destroyed){r._readyCapability.reject(new Error("Worker was destroyed"));return}var n=Uint8Array!==Float32Array;var a=new O(n);r._port=a;var s="fake"+t++;var o=new i.MessageHandler(s+"_worker",s,a);e.setup(o,a);var u=new i.MessageHandler(s,s+"_worker",a);r._messageHandler=u;r._readyCapability.resolve()})},destroy:function e(){this.destroyed=true;if(this._webWorker){this._webWorker.terminate();this._webWorker=null}u.delete(this._port);this._port=null;if(this._messageHandler){this._messageHandler.destroy();this._messageHandler=null}}};l.fromPort=function(e){if(u.has(e)){return u.get(e)}return new l(null,e)};return l}();var I=function e(){function t(e,t,r,n){this.messageHandler=e;this.loadingTask=t;this.commonObjs=new j;this.fontLoader=new o.FontLoader(t.docId);this.CMapReaderFactory=new n({baseUrl:(0,s.getDefaultSetting)("cMapUrl"),isCompressed:(0,s.getDefaultSetting)("cMapPacked")});this.destroyed=false;this.destroyCapability=null;this._passwordCapability=null;this._networkStream=r;this._fullReader=null;this._lastProgress=null;this.pageCache=[];this.pagePromises=[];this.downloadInfoCapability=(0,i.createPromiseCapability)();this.setupMessageHandler()}t.prototype={destroy:function e(){var t=this;if(this.destroyCapability){return this.destroyCapability.promise}this.destroyed=true;this.destroyCapability=(0,i.createPromiseCapability)();if(this._passwordCapability){this._passwordCapability.reject(new Error("Worker was destroyed during onPassword callback"))}var r=[];this.pageCache.forEach(function(e){if(e){r.push(e._destroy())}});this.pageCache=[];this.pagePromises=[];var n=this.messageHandler.sendWithPromise("Terminate",null);r.push(n);Promise.all(r).then(function(){t.fontLoader.clear();if(t._networkStream){t._networkStream.cancelAllRequests()}if(t.messageHandler){t.messageHandler.destroy();t.messageHandler=null}t.destroyCapability.resolve()},this.destroyCapability.reject);return this.destroyCapability.promise},setupMessageHandler:function e(){var t=this.messageHandler;var r=this.loadingTask;t.on("GetReader",function(e,t){var r=this;(0,i.assert)(this._networkStream);this._fullReader=this._networkStream.getFullReader();this._fullReader.onProgress=function(e){r._lastProgress={loaded:e.loaded,total:e.total}};t.onPull=function(){r._fullReader.read().then(function(e){var r=e.value,n=e.done;if(n){t.close();return}(0,i.assert)((0,i.isArrayBuffer)(r));t.enqueue(new Uint8Array(r),1,[r])}).catch(function(e){t.error(e)})};t.onCancel=function(e){r._fullReader.cancel(e)}},this);t.on("ReaderHeadersReady",function(e){var t=this;var r=(0,i.createPromiseCapability)();var n=this._fullReader;n.headersReady.then(function(){if(!n.isStreamingSupported||!n.isRangeSupported){if(t._lastProgress){var e=t.loadingTask;if(e.onProgress){e.onProgress(t._lastProgress)}}n.onProgress=function(e){var r=t.loadingTask;if(r.onProgress){r.onProgress({loaded:e.loaded,total:e.total})}}}r.resolve({isStreamingSupported:n.isStreamingSupported,isRangeSupported:n.isRangeSupported,contentLength:n.contentLength})},r.reject);return r.promise},this);t.on("GetRangeReader",function(e,t){(0,i.assert)(this._networkStream);var r=this._networkStream.getRangeReader(e.begin,e.end);t.onPull=function(){r.read().then(function(e){var r=e.value,n=e.done;if(n){t.close();return}(0,i.assert)((0,i.isArrayBuffer)(r));t.enqueue(new Uint8Array(r),1,[r])}).catch(function(e){t.error(e)})};t.onCancel=function(e){r.cancel(e)}},this);t.on("GetDoc",function e(t){var r=t.pdfInfo;this.numPages=t.pdfInfo.numPages;var n=this.loadingTask;var a=new T(r,this,n);this.pdfDocument=a;n._capability.resolve(a)},this);t.on("PasswordRequest",function e(t){var n=this;this._passwordCapability=(0,i.createPromiseCapability)();if(r.onPassword){var a=function e(t){n._passwordCapability.resolve({password:t})};r.onPassword(a,t.code)}else{this._passwordCapability.reject(new i.PasswordException(t.message,t.code))}return this._passwordCapability.promise},this);t.on("PasswordException",function e(t){r._capability.reject(new i.PasswordException(t.message,t.code))},this);t.on("InvalidPDF",function e(t){this.loadingTask._capability.reject(new i.InvalidPDFException(t.message))},this);t.on("MissingPDF",function e(t){this.loadingTask._capability.reject(new i.MissingPDFException(t.message))},this);t.on("UnexpectedResponse",function e(t){this.loadingTask._capability.reject(new i.UnexpectedResponseException(t.message,t.status))},this);t.on("UnknownError",function e(t){this.loadingTask._capability.reject(new i.UnknownErrorException(t.message,t.details))},this);t.on("DataLoaded",function e(t){this.downloadInfoCapability.resolve(t)},this);t.on("PDFManagerReady",function e(t){},this);t.on("StartRenderPage",function e(t){if(this.destroyed){return}var r=this.pageCache[t.pageIndex];r.stats.timeEnd("Page Request");r._startRenderPage(t.transparency,t.intent)},this);t.on("RenderPageChunk",function e(t){if(this.destroyed){return}var r=this.pageCache[t.pageIndex];r._renderPageChunk(t.operatorList,t.intent)},this);t.on("commonobj",function e(t){var r=this;if(this.destroyed){return}var n=t[0];var a=t[1];if(this.commonObjs.hasData(n)){return}switch(a){case"Font":var u=t[2];if("error"in u){var l=u.error;(0,i.warn)("Error during font loading: "+l);this.commonObjs.resolve(n,l);break}var f=null;if((0,s.getDefaultSetting)("pdfBug")&&c.default.FontInspector&&c.default["FontInspector"].enabled){f={registerFont:function e(t,r){c.default["FontInspector"].fontAdded(t,r)}}}var d=new o.FontFaceObject(u,{isEvalSupported:(0,s.getDefaultSetting)("isEvalSupported"),disableFontFace:(0,s.getDefaultSetting)("disableFontFace"),fontRegistry:f});var h=function e(t){r.commonObjs.resolve(n,d)};this.fontLoader.bind([d],h);break;case"FontPath":this.commonObjs.resolve(n,t[2]);break;default:throw new Error("Got unknown common object type "+a)}},this);t.on("obj",function e(t){if(this.destroyed){return}var r=t[0];var n=t[1];var a=t[2];var s=this.pageCache[n];var o;if(s.objs.hasData(r)){return}switch(a){case"JpegStream":o=t[3];(0,i.loadJpegStream)(r,o,s.objs);break;case"Image":o=t[3];s.objs.resolve(r,o);var u=8e6;if(o&&"data"in o&&o.data.length>u){s.cleanupAfterRender=true}break;default:throw new Error("Got unknown object type "+a)}},this);t.on("DocProgress",function e(t){if(this.destroyed){return}var r=this.loadingTask;if(r.onProgress){r.onProgress({loaded:t.loaded,total:t.total})}},this);t.on("PageError",function e(t){if(this.destroyed){return}var r=this.pageCache[t.pageNum-1];var n=r.intentStates[t.intent];if(n.displayReadyCapability){n.displayReadyCapability.reject(t.error)}else{throw new Error(t.error)}if(n.operatorList){n.operatorList.lastChunk=true;for(var a=0;a<n.renderTasks.length;a++){n.renderTasks[a].operatorListChanged()}}},this);t.on("UnsupportedFeature",function e(t){if(this.destroyed){return}var r=t.featureId;var n=this.loadingTask;if(n.onUnsupportedFeature){n.onUnsupportedFeature(r)}M.notify(r)},this);t.on("JpegDecode",function(e){if(this.destroyed){return Promise.reject(new Error("Worker was destroyed"))}if(typeof document==="undefined"){return Promise.reject(new Error('"document" is not defined.'))}var t=e[0];var r=e[1];if(r!==3&&r!==1){return Promise.reject(new Error("Only 3 components or 1 component can be returned"))}return new Promise(function(e,n){var a=new Image;a.onload=function(){var t=a.width;var n=a.height;var i=t*n;var s=i*4;var o=new Uint8Array(i*r);var u=document.createElement("canvas");u.width=t;u.height=n;var l=u.getContext("2d");l.drawImage(a,0,0);var c=l.getImageData(0,0,t,n).data;var f,d;if(r===3){for(f=0,d=0;f<s;f+=4,d+=3){o[d]=c[f];o[d+1]=c[f+1];o[d+2]=c[f+2]}}else if(r===1){for(f=0,d=0;f<s;f+=4,d++){o[d]=c[f]}}e({data:o,width:t,height:n})};a.onerror=function(){n(new Error("JpegDecode failed to load image"))};a.src=t})},this);t.on("FetchBuiltInCMap",function(e){if(this.destroyed){return Promise.reject(new Error("Worker was destroyed"))}return this.CMapReaderFactory.fetch({name:e.name})},this)},getData:function e(){return this.messageHandler.sendWithPromise("GetData",null)},getPage:function e(t,r){var n=this;if(!Number.isInteger(t)||t<=0||t>this.numPages){return Promise.reject(new Error("Invalid page request"))}var a=t-1;if(a in this.pagePromises){return this.pagePromises[a]}var i=this.messageHandler.sendWithPromise("GetPage",{pageIndex:a}).then(function(e){if(n.destroyed){throw new Error("Transport destroyed")}var t=new E(a,e,n);n.pageCache[a]=t;return t});this.pagePromises[a]=i;return i},getPageIndex:function e(t){return this.messageHandler.sendWithPromise("GetPageIndex",{ref:t}).catch(function(e){return Promise.reject(new Error(e))})},getAnnotations:function e(t,r){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:t,intent:r})},getDestinations:function e(){return this.messageHandler.sendWithPromise("GetDestinations",null)},getDestination:function e(t){return this.messageHandler.sendWithPromise("GetDestination",{id:t})},getPageLabels:function e(){return this.messageHandler.sendWithPromise("GetPageLabels",null)},getPageMode:function e(){return this.messageHandler.sendWithPromise("GetPageMode",null)},getAttachments:function e(){return this.messageHandler.sendWithPromise("GetAttachments",null)},getJavaScript:function e(){return this.messageHandler.sendWithPromise("GetJavaScript",null)},getOutline:function e(){return this.messageHandler.sendWithPromise("GetOutline",null)},getMetadata:function e(){return this.messageHandler.sendWithPromise("GetMetadata",null).then(function e(t){return{info:t[0],metadata:t[1]?new f.Metadata(t[1]):null}})},getStats:function e(){return this.messageHandler.sendWithPromise("GetStats",null)},startCleanup:function e(){var t=this;this.messageHandler.sendWithPromise("Cleanup",null).then(function(){for(var e=0,r=t.pageCache.length;e<r;e++){var n=t.pageCache[e];if(n){n.cleanup()}}t.commonObjs.clear();t.fontLoader.clear()})}};return t}();var j=function e(){function t(){this.objs=Object.create(null)}t.prototype={ensureObj:function e(t){if(this.objs[t]){return this.objs[t]}var r={capability:(0,i.createPromiseCapability)(),data:null,resolved:false};this.objs[t]=r;return r},get:function e(t,r){if(r){this.ensureObj(t).capability.promise.then(r);return null}var n=this.objs[t];if(!n||!n.resolved){throw new Error("Requesting object that isn't resolved yet "+t)}return n.data},resolve:function e(t,r){var n=this.ensureObj(t);n.resolved=true;n.data=r;n.capability.resolve(r)},isResolved:function e(t){var r=this.objs;if(!r[t]){return false}return r[t].resolved},hasData:function e(t){return this.isResolved(t)},getData:function e(t){var r=this.objs;if(!r[t]||!r[t].resolved){return null}return r[t].data},clear:function e(){this.objs=Object.create(null)}};return t}();var F=function e(){function t(e){this._internalRenderTask=e;this.onContinue=null}t.prototype={get promise(){return this._internalRenderTask.capability.promise},cancel:function e(){this._internalRenderTask.cancel()},then:function e(t,r){return this.promise.then.apply(this.promise,arguments)}};return t}();var D=function e(){var t=new WeakMap;function r(e,t,r,n,a,s,o){this.callback=e;this.params=t;this.objs=r;this.commonObjs=n;this.operatorListIdx=null;this.operatorList=a;this.pageNumber=s;this.canvasFactory=o;this.running=false;this.graphicsReadyCallback=null;this.graphicsReady=false;this.useRequestAnimationFrame=false;this.cancelled=false;this.capability=(0,i.createPromiseCapability)();this.task=new F(this);this._continueBound=this._continue.bind(this);this._scheduleNextBound=this._scheduleNext.bind(this);this._nextBound=this._next.bind(this);this._canvas=t.canvasContext.canvas}r.prototype={initializeGraphics:function e(r){if(this._canvas){if(t.has(this._canvas)){throw new Error("Cannot use the same canvas during multiple render() operations. "+"Use different canvas or ensure previous operations were "+"cancelled or completed.")}t.set(this._canvas,this)}if(this.cancelled){return}if((0,s.getDefaultSetting)("pdfBug")&&c.default.StepperManager&&c.default.StepperManager.enabled){this.stepper=c.default.StepperManager.create(this.pageNumber-1);this.stepper.init(this.operatorList);this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint()}var n=this.params;this.gfx=new u.CanvasGraphics(n.canvasContext,this.commonObjs,this.objs,this.canvasFactory,n.imageLayer);this.gfx.beginDrawing({transform:n.transform,viewport:n.viewport,transparency:r,background:n.background});this.operatorListIdx=0;this.graphicsReady=true;if(this.graphicsReadyCallback){this.graphicsReadyCallback()}},cancel:function e(){this.running=false;this.cancelled=true;if(this._canvas){t.delete(this._canvas)}if((0,s.getDefaultSetting)("pdfjsNext")){this.callback(new s.RenderingCancelledException("Rendering cancelled, page "+this.pageNumber,"canvas"))}else{this.callback("cancelled")}},operatorListChanged:function e(){if(!this.graphicsReady){if(!this.graphicsReadyCallback){this.graphicsReadyCallback=this._continueBound}return}if(this.stepper){this.stepper.updateOperatorList(this.operatorList)}if(this.running){return}this._continue()},_continue:function e(){this.running=true;if(this.cancelled){return}if(this.task.onContinue){this.task.onContinue(this._scheduleNextBound)}else{this._scheduleNext()}},_scheduleNext:function e(){if(this.useRequestAnimationFrame&&typeof window!=="undefined"){window.requestAnimationFrame(this._nextBound)}else{Promise.resolve(undefined).then(this._nextBound)}},_next:function e(){if(this.cancelled){return}this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper);if(this.operatorListIdx===this.operatorList.argsArray.length){this.running=false;if(this.operatorList.lastChunk){this.gfx.endDrawing();if(this._canvas){t.delete(this._canvas)}this.callback()}}}};return r}();var M=function e(){var t=[];return{listen:function e(r){(0,i.deprecated)("Global UnsupportedManager.listen is used: "+" use PDFDocumentLoadingTask.onUnsupportedFeature instead");t.push(r)},notify:function e(r){for(var n=0,a=t.length;n<a;n++){t[n](r)}}}}();var N,q;{t.version=N="1.10.100";t.build=q="ea29ec83"}t.getDocument=k;t.LoopbackPort=O;t.PDFDataRangeTransport=x;t.PDFWorker=L;t.PDFDocumentProxy=T;t.PDFPageProxy=E;t.setPDFNetworkStreamClass=P;t._UnsupportedManager=M;t.version=N;t.build=q},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.WebGLUtils=undefined;var n=r(15);var a=r(0);var i=function e(){function t(e,t,r){var n=e.createShader(r);e.shaderSource(n,t);e.compileShader(n);var a=e.getShaderParameter(n,e.COMPILE_STATUS);if(!a){var i=e.getShaderInfoLog(n);throw new Error("Error during shader compilation: "+i)}return n}function r(e,r){return t(e,r,e.VERTEX_SHADER)}function i(e,r){return t(e,r,e.FRAGMENT_SHADER)}function s(e,t){var r=e.createProgram();for(var n=0,a=t.length;n<a;++n){e.attachShader(r,t[n])}e.linkProgram(r);var i=e.getProgramParameter(r,e.LINK_STATUS);if(!i){var s=e.getProgramInfoLog(r);throw new Error("Error during program linking: "+s)}return r}function o(e,t,r){e.activeTexture(r);var n=e.createTexture();e.bindTexture(e.TEXTURE_2D,n);e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE);e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE);e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,e.NEAREST);e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,e.NEAREST);e.texImage2D(e.TEXTURE_2D,0,e.RGBA,e.RGBA,e.UNSIGNED_BYTE,t);return n}var u,l;function c(){if(u){return}l=document.createElement("canvas");u=l.getContext("webgl",{premultipliedalpha:false})}var f="  attribute vec2 a_position;                                      attribute vec2 a_texCoord;                                                                                                      uniform vec2 u_resolution;                                                                                                      varying vec2 v_texCoord;                                                                                                        void main() {                                                     vec2 clipSpace = (a_position / u_resolution) * 2.0 - 1.0;       gl_Position = vec4(clipSpace * vec2(1, -1), 0, 1);                                                                              v_texCoord = a_texCoord;                                      }                                                             ";var d="  precision mediump float;                                                                                                        uniform vec4 u_backdrop;                                        uniform int u_subtype;                                          uniform sampler2D u_image;                                      uniform sampler2D u_mask;                                                                                                       varying vec2 v_texCoord;                                                                                                        void main() {                                                     vec4 imageColor = texture2D(u_image, v_texCoord);               vec4 maskColor = texture2D(u_mask, v_texCoord);                 if (u_backdrop.a > 0.0) {                                         maskColor.rgb = maskColor.rgb * maskColor.a +                                   u_backdrop.rgb * (1.0 - maskColor.a);         }                                                               float lum;                                                      if (u_subtype == 0) {                                             lum = maskColor.a;                                            } else {                                                          lum = maskColor.r * 0.3 + maskColor.g * 0.59 +                        maskColor.b * 0.11;                                     }                                                               imageColor.a *= lum;                                            imageColor.rgb *= imageColor.a;                                 gl_FragColor = imageColor;                                    }                                                             ";var h=null;function v(){var e,t;c();e=l;l=null;t=u;u=null;var n=r(t,f);var a=i(t,d);var o=s(t,[n,a]);t.useProgram(o);var v={};v.gl=t;v.canvas=e;v.resolutionLocation=t.getUniformLocation(o,"u_resolution");v.positionLocation=t.getAttribLocation(o,"a_position");v.backdropLocation=t.getUniformLocation(o,"u_backdrop");v.subtypeLocation=t.getUniformLocation(o,"u_subtype");var p=t.getAttribLocation(o,"a_texCoord");var m=t.getUniformLocation(o,"u_image");var g=t.getUniformLocation(o,"u_mask");var b=t.createBuffer();t.bindBuffer(t.ARRAY_BUFFER,b);t.bufferData(t.ARRAY_BUFFER,new Float32Array([0,0,1,0,0,1,0,1,1,0,1,1]),t.STATIC_DRAW);t.enableVertexAttribArray(p);t.vertexAttribPointer(p,2,t.FLOAT,false,0,0);t.uniform1i(m,0);t.uniform1i(g,1);h=v}function p(e,t,r){var n=e.width,a=e.height;if(!h){v()}var i=h,s=i.canvas,u=i.gl;s.width=n;s.height=a;u.viewport(0,0,u.drawingBufferWidth,u.drawingBufferHeight);u.uniform2f(i.resolutionLocation,n,a);if(r.backdrop){u.uniform4f(i.resolutionLocation,r.backdrop[0],r.backdrop[1],r.backdrop[2],1)}else{u.uniform4f(i.resolutionLocation,0,0,0,0)}u.uniform1i(i.subtypeLocation,r.subtype==="Luminosity"?1:0);var l=o(u,e,u.TEXTURE0);var c=o(u,t,u.TEXTURE1);var f=u.createBuffer();u.bindBuffer(u.ARRAY_BUFFER,f);u.bufferData(u.ARRAY_BUFFER,new Float32Array([0,0,n,0,0,a,0,a,n,0,n,a]),u.STATIC_DRAW);u.enableVertexAttribArray(i.positionLocation);u.vertexAttribPointer(i.positionLocation,2,u.FLOAT,false,0,0);u.clearColor(0,0,0,0);u.enable(u.BLEND);u.blendFunc(u.ONE,u.ONE_MINUS_SRC_ALPHA);u.clear(u.COLOR_BUFFER_BIT);u.drawArrays(u.TRIANGLES,0,6);u.flush();u.deleteTexture(l);u.deleteTexture(c);u.deleteBuffer(f);return s}var m="  attribute vec2 a_position;                                      attribute vec3 a_color;                                                                                                         uniform vec2 u_resolution;                                      uniform vec2 u_scale;                                           uniform vec2 u_offset;                                                                                                          varying vec4 v_color;                                                                                                           void main() {                                                     vec2 position = (a_position + u_offset) * u_scale;              vec2 clipSpace = (position / u_resolution) * 2.0 - 1.0;         gl_Position = vec4(clipSpace * vec2(1, -1), 0, 1);                                                                              v_color = vec4(a_color / 255.0, 1.0);                         }                                                             ";var g="  precision mediump float;                                                                                                        varying vec4 v_color;                                                                                                           void main() {                                                     gl_FragColor = v_color;                                       }                                                             ";var b=null;function y(){var e,t;c();e=l;l=null;t=u;u=null;var n=r(t,m);var a=i(t,g);var o=s(t,[n,a]);t.useProgram(o);var f={};f.gl=t;f.canvas=e;f.resolutionLocation=t.getUniformLocation(o,"u_resolution");f.scaleLocation=t.getUniformLocation(o,"u_scale");f.offsetLocation=t.getUniformLocation(o,"u_offset");f.positionLocation=t.getAttribLocation(o,"a_position");f.colorLocation=t.getAttribLocation(o,"a_color");b=f}function _(e,t,r,n,a){if(!b){y()}var i=b,s=i.canvas,o=i.gl;s.width=e;s.height=t;o.viewport(0,0,o.drawingBufferWidth,o.drawingBufferHeight);o.uniform2f(i.resolutionLocation,e,t);var u=0;var l,c,f;for(l=0,c=n.length;l<c;l++){switch(n[l].type){case"lattice":f=n[l].coords.length/n[l].verticesPerRow|0;u+=(f-1)*(n[l].verticesPerRow-1)*6;break;case"triangles":u+=n[l].coords.length;break}}var d=new Float32Array(u*2);var h=new Uint8Array(u*3);var v=a.coords,p=a.colors;var m=0,g=0;for(l=0,c=n.length;l<c;l++){var _=n[l],A=_.coords,S=_.colors;switch(_.type){case"lattice":var w=_.verticesPerRow;f=A.length/w|0;for(var P=1;P<f;P++){var k=P*w+1;for(var C=1;C<w;C++,k++){d[m]=v[A[k-w-1]];d[m+1]=v[A[k-w-1]+1];d[m+2]=v[A[k-w]];d[m+3]=v[A[k-w]+1];d[m+4]=v[A[k-1]];d[m+5]=v[A[k-1]+1];h[g]=p[S[k-w-1]];h[g+1]=p[S[k-w-1]+1];h[g+2]=p[S[k-w-1]+2];h[g+3]=p[S[k-w]];h[g+4]=p[S[k-w]+1];h[g+5]=p[S[k-w]+2];h[g+6]=p[S[k-1]];h[g+7]=p[S[k-1]+1];h[g+8]=p[S[k-1]+2];d[m+6]=d[m+2];d[m+7]=d[m+3];d[m+8]=d[m+4];d[m+9]=d[m+5];d[m+10]=v[A[k]];d[m+11]=v[A[k]+1];h[g+9]=h[g+3];h[g+10]=h[g+4];h[g+11]=h[g+5];h[g+12]=h[g+6];h[g+13]=h[g+7];h[g+14]=h[g+8];h[g+15]=p[S[k]];h[g+16]=p[S[k]+1];h[g+17]=p[S[k]+2];m+=12;g+=18}}break;case"triangles":for(var R=0,x=A.length;R<x;R++){d[m]=v[A[R]];d[m+1]=v[A[R]+1];h[g]=p[S[R]];h[g+1]=p[S[R]+1];h[g+2]=p[S[R]+2];m+=2;g+=3}break}}if(r){o.clearColor(r[0]/255,r[1]/255,r[2]/255,1)}else{o.clearColor(0,0,0,0)}o.clear(o.COLOR_BUFFER_BIT);var T=o.createBuffer();o.bindBuffer(o.ARRAY_BUFFER,T);o.bufferData(o.ARRAY_BUFFER,d,o.STATIC_DRAW);o.enableVertexAttribArray(i.positionLocation);o.vertexAttribPointer(i.positionLocation,2,o.FLOAT,false,0,0);var E=o.createBuffer();o.bindBuffer(o.ARRAY_BUFFER,E);o.bufferData(o.ARRAY_BUFFER,h,o.STATIC_DRAW);o.enableVertexAttribArray(i.colorLocation);o.vertexAttribPointer(i.colorLocation,3,o.UNSIGNED_BYTE,false,0,0);o.uniform2f(i.scaleLocation,a.scaleX,a.scaleY);o.uniform2f(i.offsetLocation,a.offsetX,a.offsetY);o.drawArrays(o.TRIANGLES,0,u);o.flush();o.deleteBuffer(T);o.deleteBuffer(E);return s}function A(){if(h&&h.canvas){h.canvas.width=0;h.canvas.height=0}if(b&&b.canvas){b.canvas.width=0;b.canvas.height=0}h=null;b=null}return{get isEnabled(){if((0,n.getDefaultSetting)("disableWebGL")){return false}var e=false;try{c();e=!!u}catch(e){}return(0,a.shadow)(this,"isEnabled",e)},composeSMask:p,drawFigures:_,clear:A}}();t.WebGLUtils=i},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.Metadata=undefined;var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(t,r,n){if(r)e(t.prototype,r);if(n)e(t,n);return t}}();var a=r(0);var i=r(122);function s(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var o=function(){function e(t){s(this,e);(0,a.assert)(typeof t==="string","Metadata: input is not a string");t=this._repair(t);var r=new i.SimpleXMLParser;var n=r.parseFromString(t);this._metadata=Object.create(null);if(n){this._parse(n)}}n(e,[{key:"_repair",value:function e(t){return t.replace(/>\\376\\377([^<]+)/g,function(e,t){var r=t.replace(/\\([0-3])([0-7])([0-7])/g,function(e,t,r,n){return String.fromCharCode(t*64+r*8+n*1)});var n="";for(var a=0,i=r.length;a<i;a+=2){var s=r.charCodeAt(a)*256+r.charCodeAt(a+1);if(s>=32&&s<127&&s!==60&&s!==62&&s!==38){n+=String.fromCharCode(s)}else{n+="&#x"+(65536+s).toString(16).substring(1)+";"}}return">"+n})}},{key:"_parse",value:function e(t){var r=t.documentElement;if(r.nodeName.toLowerCase()!=="rdf:rdf"){r=r.firstChild;while(r&&r.nodeName.toLowerCase()!=="rdf:rdf"){r=r.nextSibling}}var n=r?r.nodeName.toLowerCase():null;if(!r||n!=="rdf:rdf"||!r.hasChildNodes()){return}var a=r.childNodes;for(var i=0,s=a.length;i<s;i++){var o=a[i];if(o.nodeName.toLowerCase()!=="rdf:description"){continue}for(var u=0,l=o.childNodes.length;u<l;u++){if(o.childNodes[u].nodeName.toLowerCase()!=="#text"){var c=o.childNodes[u];var f=c.nodeName.toLowerCase();this._metadata[f]=c.textContent.trim()}}}}},{key:"get",value:function e(t){return this._metadata[t]||null}},{key:"getAll",value:function e(){return this._metadata}},{key:"has",value:function e(t){return typeof this._metadata[t]!=="undefined"}},{key:"metadata",get:function e(){(0,a.deprecated)("`metadata` getter; use `getAll()` instead.");return this.getAll()}}]);return e}();t.Metadata=o},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.AnnotationLayer=undefined;var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(t,r,n){if(r)e(t.prototype,r);if(n)e(t,n);return t}}();var a=r(15);var i=r(0);function s(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function o(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}function u(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var l=function(){function e(){u(this,e)}n(e,null,[{key:"create",value:function e(t){var r=t.data.annotationType;switch(r){case i.AnnotationType.LINK:return new f(t);case i.AnnotationType.TEXT:return new d(t);case i.AnnotationType.WIDGET:var n=t.data.fieldType;switch(n){case"Tx":return new v(t);case"Btn":if(t.data.radioButton){return new m(t)}else if(t.data.checkBox){return new p(t)}(0,i.warn)("Unimplemented button widget annotation: pushbutton");break;case"Ch":return new g(t)}return new h(t);case i.AnnotationType.POPUP:return new b(t);case i.AnnotationType.LINE:return new _(t);case i.AnnotationType.SQUARE:return new A(t);case i.AnnotationType.CIRCLE:return new S(t);case i.AnnotationType.POLYLINE:return new w(t);case i.AnnotationType.POLYGON:return new P(t);case i.AnnotationType.HIGHLIGHT:return new k(t);case i.AnnotationType.UNDERLINE:return new C(t);case i.AnnotationType.SQUIGGLY:return new R(t);case i.AnnotationType.STRIKEOUT:return new x(t);case i.AnnotationType.STAMP:return new T(t);case i.AnnotationType.FILEATTACHMENT:return new E(t);default:return new c(t)}}}]);return e}();var c=function(){function e(t){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:false;var n=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;u(this,e);this.isRenderable=r;this.data=t.data;this.layer=t.layer;this.page=t.page;this.viewport=t.viewport;this.linkService=t.linkService;this.downloadManager=t.downloadManager;this.imageResourcesPath=t.imageResourcesPath;this.renderInteractiveForms=t.renderInteractiveForms;this.svgFactory=t.svgFactory;if(r){this.container=this._createContainer(n)}}n(e,[{key:"_createContainer",value:function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;var r=this.data,n=this.page,s=this.viewport;var o=document.createElement("section");var u=r.rect[2]-r.rect[0];var l=r.rect[3]-r.rect[1];o.setAttribute("data-annotation-id",r.id);var c=i.Util.normalizeRect([r.rect[0],n.view[3]-r.rect[1]+n.view[1],r.rect[2],n.view[3]-r.rect[3]+n.view[1]]);a.CustomStyle.setProp("transform",o,"matrix("+s.transform.join(",")+")");a.CustomStyle.setProp("transformOrigin",o,-c[0]+"px "+-c[1]+"px");if(!t&&r.borderStyle.width>0){o.style.borderWidth=r.borderStyle.width+"px";if(r.borderStyle.style!==i.AnnotationBorderStyleType.UNDERLINE){u=u-2*r.borderStyle.width;l=l-2*r.borderStyle.width}var f=r.borderStyle.horizontalCornerRadius;var d=r.borderStyle.verticalCornerRadius;if(f>0||d>0){var h=f+"px / "+d+"px";a.CustomStyle.setProp("borderRadius",o,h)}switch(r.borderStyle.style){case i.AnnotationBorderStyleType.SOLID:o.style.borderStyle="solid";break;case i.AnnotationBorderStyleType.DASHED:o.style.borderStyle="dashed";break;case i.AnnotationBorderStyleType.BEVELED:(0,i.warn)("Unimplemented border style: beveled");break;case i.AnnotationBorderStyleType.INSET:(0,i.warn)("Unimplemented border style: inset");break;case i.AnnotationBorderStyleType.UNDERLINE:o.style.borderBottomStyle="solid";break;default:break}if(r.color){o.style.borderColor=i.Util.makeCssRgb(r.color[0]|0,r.color[1]|0,r.color[2]|0)}else{o.style.borderWidth=0}}o.style.left=c[0]+"px";o.style.top=c[1]+"px";o.style.width=u+"px";o.style.height=l+"px";return o}},{key:"_createPopup",value:function e(t,r,n){if(!r){r=document.createElement("div");r.style.height=t.style.height;r.style.width=t.style.width;t.appendChild(r)}var a=new y({container:t,trigger:r,color:n.color,title:n.title,contents:n.contents,hideWrapper:true});var i=a.render();i.style.left=t.style.width;t.appendChild(i)}},{key:"render",value:function e(){throw new Error("Abstract method `AnnotationElement.render` called")}}]);return e}();var f=function(e){o(t,e);function t(e){u(this,t);var r=!!(e.data.url||e.data.dest||e.data.action);return s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,r))}n(t,[{key:"render",value:function e(){this.container.className="linkAnnotation";var t=document.createElement("a");(0,a.addLinkAttributes)(t,{url:this.data.url,target:this.data.newWindow?a.LinkTarget.BLANK:undefined});if(!this.data.url){if(this.data.action){this._bindNamedAction(t,this.data.action)}else{this._bindLink(t,this.data.dest)}}this.container.appendChild(t);return this.container}},{key:"_bindLink",value:function e(t,r){var n=this;t.href=this.linkService.getDestinationHash(r);t.onclick=function(){if(r){n.linkService.navigateTo(r)}return false};if(r){t.className="internalLink"}}},{key:"_bindNamedAction",value:function e(t,r){var n=this;t.href=this.linkService.getAnchorUrl("");t.onclick=function(){n.linkService.executeNamedAction(r);return false};t.className="internalLink"}}]);return t}(c);var d=function(e){o(t,e);function t(e){u(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,r))}n(t,[{key:"render",value:function e(){this.container.className="textAnnotation";var t=document.createElement("img");t.style.height=this.container.style.height;t.style.width=this.container.style.width;t.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg";t.alt="[{{type}} Annotation]";t.dataset.l10nId="text_annotation_type";t.dataset.l10nArgs=JSON.stringify({type:this.data.name});if(!this.data.hasPopup){this._createPopup(this.container,t,this.data)}this.container.appendChild(t);return this.container}}]);return t}(c);var h=function(e){o(t,e);function t(){u(this,t);return s(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}n(t,[{key:"render",value:function e(){return this.container}}]);return t}(c);var v=function(e){o(t,e);function t(e){u(this,t);var r=e.renderInteractiveForms||!e.data.hasAppearance&&!!e.data.fieldValue;return s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,r))}n(t,[{key:"render",value:function e(){var t=["left","center","right"];this.container.className="textWidgetAnnotation";var r=null;if(this.renderInteractiveForms){if(this.data.multiLine){r=document.createElement("textarea");r.textContent=this.data.fieldValue}else{r=document.createElement("input");r.type="text";r.setAttribute("value",this.data.fieldValue)}r.disabled=this.data.readOnly;if(this.data.maxLen!==null){r.maxLength=this.data.maxLen}if(this.data.comb){var n=this.data.rect[2]-this.data.rect[0];var a=n/this.data.maxLen;r.classList.add("comb");r.style.letterSpacing="calc("+a+"px - 1ch)"}}else{r=document.createElement("div");r.textContent=this.data.fieldValue;r.style.verticalAlign="middle";r.style.display="table-cell";var i=null;if(this.data.fontRefName){i=this.page.commonObjs.getData(this.data.fontRefName)}this._setTextStyle(r,i)}if(this.data.textAlignment!==null){r.style.textAlign=t[this.data.textAlignment]}this.container.appendChild(r);return this.container}},{key:"_setTextStyle",value:function e(t,r){var n=t.style;n.fontSize=this.data.fontSize+"px";n.direction=this.data.fontDirection<0?"rtl":"ltr";if(!r){return}n.fontWeight=r.black?r.bold?"900":"bold":r.bold?"bold":"normal";n.fontStyle=r.italic?"italic":"normal";var a=r.loadedName?'"'+r.loadedName+'", ':"";var i=r.fallbackName||"Helvetica, sans-serif";n.fontFamily=a+i}}]);return t}(h);var p=function(e){o(t,e);function t(e){u(this,t);return s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,e.renderInteractiveForms))}n(t,[{key:"render",value:function e(){this.container.className="buttonWidgetAnnotation checkBox";var t=document.createElement("input");t.disabled=this.data.readOnly;t.type="checkbox";if(this.data.fieldValue&&this.data.fieldValue!=="Off"){t.setAttribute("checked",true)}this.container.appendChild(t);return this.container}}]);return t}(h);var m=function(e){o(t,e);function t(e){u(this,t);return s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,e.renderInteractiveForms))}n(t,[{key:"render",value:function e(){this.container.className="buttonWidgetAnnotation radioButton";var t=document.createElement("input");t.disabled=this.data.readOnly;t.type="radio";t.name=this.data.fieldName;if(this.data.fieldValue===this.data.buttonValue){t.setAttribute("checked",true)}this.container.appendChild(t);return this.container}}]);return t}(h);var g=function(e){o(t,e);function t(e){u(this,t);return s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,e.renderInteractiveForms))}n(t,[{key:"render",value:function e(){this.container.className="choiceWidgetAnnotation";var t=document.createElement("select");t.disabled=this.data.readOnly;if(!this.data.combo){t.size=this.data.options.length;if(this.data.multiSelect){t.multiple=true}}for(var r=0,n=this.data.options.length;r<n;r++){var a=this.data.options[r];var i=document.createElement("option");i.textContent=a.displayValue;i.value=a.exportValue;if(this.data.fieldValue.indexOf(a.displayValue)>=0){i.setAttribute("selected",true)}t.appendChild(i)}this.container.appendChild(t);return this.container}}]);return t}(h);var b=function(e){o(t,e);function t(e){u(this,t);var r=!!(e.data.title||e.data.contents);return s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,r))}n(t,[{key:"render",value:function e(){var t=["Line","Square","Circle","PolyLine","Polygon"];this.container.className="popupAnnotation";if(t.indexOf(this.data.parentType)>=0){return this.container}var r='[data-annotation-id="'+this.data.parentId+'"]';var n=this.layer.querySelector(r);if(!n){return this.container}var i=new y({container:this.container,trigger:n,color:this.data.color,title:this.data.title,contents:this.data.contents});var s=parseFloat(n.style.left);var o=parseFloat(n.style.width);a.CustomStyle.setProp("transformOrigin",this.container,-(s+o)+"px -"+n.style.top);this.container.style.left=s+o+"px";this.container.appendChild(i.render());return this.container}}]);return t}(c);var y=function(){function e(t){u(this,e);this.container=t.container;this.trigger=t.trigger;this.color=t.color;this.title=t.title;this.contents=t.contents;this.hideWrapper=t.hideWrapper||false;this.pinned=false}n(e,[{key:"render",value:function e(){var t=.7;var r=document.createElement("div");r.className="popupWrapper";this.hideElement=this.hideWrapper?r:this.container;this.hideElement.setAttribute("hidden",true);var n=document.createElement("div");n.className="popup";var a=this.color;if(a){var s=t*(255-a[0])+a[0];var o=t*(255-a[1])+a[1];var u=t*(255-a[2])+a[2];n.style.backgroundColor=i.Util.makeCssRgb(s|0,o|0,u|0)}var l=this._formatContents(this.contents);var c=document.createElement("h1");c.textContent=this.title;this.trigger.addEventListener("click",this._toggle.bind(this));this.trigger.addEventListener("mouseover",this._show.bind(this,false));this.trigger.addEventListener("mouseout",this._hide.bind(this,false));n.addEventListener("click",this._hide.bind(this,true));n.appendChild(c);n.appendChild(l);r.appendChild(n);return r}},{key:"_formatContents",value:function e(t){var r=document.createElement("p");var n=t.split(/(?:\r\n?|\n)/);for(var a=0,i=n.length;a<i;++a){var s=n[a];r.appendChild(document.createTextNode(s));if(a<i-1){r.appendChild(document.createElement("br"))}}return r}},{key:"_toggle",value:function e(){if(this.pinned){this._hide(true)}else{this._show(true)}}},{key:"_show",value:function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;if(t){this.pinned=true}if(this.hideElement.hasAttribute("hidden")){this.hideElement.removeAttribute("hidden");this.container.style.zIndex+=1}}},{key:"_hide",value:function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:true;if(t){this.pinned=false}if(!this.hideElement.hasAttribute("hidden")&&!this.pinned){this.hideElement.setAttribute("hidden",true);this.container.style.zIndex-=1}}}]);return e}();var _=function(e){o(t,e);function t(e){u(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,r,true))}n(t,[{key:"render",value:function e(){this.container.className="lineAnnotation";var t=this.data;var r=t.rect[2]-t.rect[0];var n=t.rect[3]-t.rect[1];var a=this.svgFactory.create(r,n);var i=this.svgFactory.createElement("svg:line");i.setAttribute("x1",t.rect[2]-t.lineCoordinates[0]);i.setAttribute("y1",t.rect[3]-t.lineCoordinates[1]);i.setAttribute("x2",t.rect[2]-t.lineCoordinates[2]);i.setAttribute("y2",t.rect[3]-t.lineCoordinates[3]);i.setAttribute("stroke-width",t.borderStyle.width);i.setAttribute("stroke","transparent");a.appendChild(i);this.container.append(a);this._createPopup(this.container,i,t);return this.container}}]);return t}(c);var A=function(e){o(t,e);function t(e){u(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,r,true))}n(t,[{key:"render",value:function e(){this.container.className="squareAnnotation";var t=this.data;var r=t.rect[2]-t.rect[0];var n=t.rect[3]-t.rect[1];var a=this.svgFactory.create(r,n);var i=t.borderStyle.width;var s=this.svgFactory.createElement("svg:rect");s.setAttribute("x",i/2);s.setAttribute("y",i/2);s.setAttribute("width",r-i);s.setAttribute("height",n-i);s.setAttribute("stroke-width",i);s.setAttribute("stroke","transparent");s.setAttribute("fill","none");a.appendChild(s);this.container.append(a);this._createPopup(this.container,s,t);return this.container}}]);return t}(c);var S=function(e){o(t,e);function t(e){u(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,r,true))}n(t,[{key:"render",value:function e(){this.container.className="circleAnnotation";var t=this.data;var r=t.rect[2]-t.rect[0];var n=t.rect[3]-t.rect[1];var a=this.svgFactory.create(r,n);var i=t.borderStyle.width;var s=this.svgFactory.createElement("svg:ellipse");s.setAttribute("cx",r/2);s.setAttribute("cy",n/2);s.setAttribute("rx",r/2-i/2);s.setAttribute("ry",n/2-i/2);s.setAttribute("stroke-width",i);s.setAttribute("stroke","transparent");s.setAttribute("fill","none");a.appendChild(s);this.container.append(a);this._createPopup(this.container,s,t);return this.container}}]);return t}(c);var w=function(e){o(t,e);function t(e){u(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);var n=s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,r,true));n.containerClassName="polylineAnnotation";n.svgElementName="svg:polyline";return n}n(t,[{key:"render",value:function e(){this.container.className=this.containerClassName;var t=this.data;var r=t.rect[2]-t.rect[0];var n=t.rect[3]-t.rect[1];var a=this.svgFactory.create(r,n);var i=t.vertices;var s=[];for(var o=0,u=i.length;o<u;o++){var l=i[o].x-t.rect[0];var c=t.rect[3]-i[o].y;s.push(l+","+c)}s=s.join(" ");var f=t.borderStyle.width;var d=this.svgFactory.createElement(this.svgElementName);d.setAttribute("points",s);d.setAttribute("stroke-width",f);d.setAttribute("stroke","transparent");d.setAttribute("fill","none");a.appendChild(d);this.container.append(a);this._createPopup(this.container,d,t);return this.container}}]);return t}(c);var P=function(e){o(t,e);function t(e){u(this,t);var r=s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));r.containerClassName="polygonAnnotation";r.svgElementName="svg:polygon";return r}return t}(w);var k=function(e){o(t,e);function t(e){u(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,r,true))}n(t,[{key:"render",value:function e(){this.container.className="highlightAnnotation";if(!this.data.hasPopup){this._createPopup(this.container,null,this.data)}return this.container}}]);return t}(c);var C=function(e){o(t,e);function t(e){u(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,r,true))}n(t,[{key:"render",value:function e(){this.container.className="underlineAnnotation";if(!this.data.hasPopup){this._createPopup(this.container,null,this.data)}return this.container}}]);return t}(c);var R=function(e){o(t,e);function t(e){u(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,r,true))}n(t,[{key:"render",value:function e(){this.container.className="squigglyAnnotation";if(!this.data.hasPopup){this._createPopup(this.container,null,this.data)}return this.container}}]);return t}(c);var x=function(e){o(t,e);function t(e){u(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,r,true))}n(t,[{key:"render",value:function e(){this.container.className="strikeoutAnnotation";if(!this.data.hasPopup){this._createPopup(this.container,null,this.data)}return this.container}}]);return t}(c);var T=function(e){o(t,e);function t(e){u(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,r,true))}n(t,[{key:"render",value:function e(){this.container.className="stampAnnotation";if(!this.data.hasPopup){this._createPopup(this.container,null,this.data)}return this.container}}]);return t}(c);var E=function(e){o(t,e);function t(e){u(this,t);var r=s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,true));var n=r.data.file;r.filename=(0,a.getFilenameFromUrl)(n.filename);r.content=n.content;r.linkService.onFileAttachmentAnnotation({id:(0,i.stringToPDFString)(n.filename),filename:n.filename,content:n.content});return r}n(t,[{key:"render",value:function e(){this.container.className="fileAttachmentAnnotation";var t=document.createElement("div");t.style.height=this.container.style.height;t.style.width=this.container.style.width;t.addEventListener("dblclick",this._download.bind(this));if(!this.data.hasPopup&&(this.data.title||this.data.contents)){this._createPopup(this.container,t,this.data)}this.container.appendChild(t);return this.container}},{key:"_download",value:function e(){if(!this.downloadManager){(0,i.warn)("Download cannot be started due to unavailable download manager");return}this.downloadManager.downloadData(this.content,this.filename,"")}}]);return t}(c);var O=function(){function e(){u(this,e)}n(e,null,[{key:"render",value:function e(t){for(var r=0,n=t.annotations.length;r<n;r++){var i=t.annotations[r];if(!i){continue}var s=l.create({data:i,layer:t.div,page:t.page,viewport:t.viewport,linkService:t.linkService,downloadManager:t.downloadManager,imageResourcesPath:t.imageResourcesPath||(0,a.getDefaultSetting)("imageResourcesPath"),renderInteractiveForms:t.renderInteractiveForms||false,svgFactory:new a.DOMSVGFactory});if(s.isRenderable){t.div.appendChild(s.render())}}}},{key:"update",value:function e(t){for(var r=0,n=t.annotations.length;r<n;r++){var i=t.annotations[r];var s=t.div.querySelector('[data-annotation-id="'+i.id+'"]');if(s){a.CustomStyle.setProp("transform",s,"matrix("+t.viewport.transform.join(",")+")")}}t.div.removeAttribute("hidden")}}]);return e}();t.AnnotationLayer=O},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.renderTextLayer=undefined;var n=r(0);var a=r(15);var i=function e(){var t=1e5;var r=/\S/;function i(e){return!r.test(e)}var s=["left: ",0,"px; top: ",0,"px; font-size: ",0,"px; font-family: ","",";"];function o(e,t,r){var o=document.createElement("div");var u={style:null,angle:0,canvasWidth:0,isWhitespace:false,originalTransform:null,paddingBottom:0,paddingLeft:0,paddingRight:0,paddingTop:0,scale:1};e._textDivs.push(o);if(i(t.str)){u.isWhitespace=true;e._textDivProperties.set(o,u);return}var l=n.Util.transform(e._viewport.transform,t.transform);var c=Math.atan2(l[1],l[0]);var f=r[t.fontName];if(f.vertical){c+=Math.PI/2}var d=Math.sqrt(l[2]*l[2]+l[3]*l[3]);var h=d;if(f.ascent){h=f.ascent*h}else if(f.descent){h=(1+f.descent)*h}var v;var p;if(c===0){v=l[4];p=l[5]-h}else{v=l[4]+h*Math.sin(c);p=l[5]-h*Math.cos(c)}s[1]=v;s[3]=p;s[5]=d;s[7]=f.fontFamily;u.style=s.join("");o.setAttribute("style",u.style);o.textContent=t.str;if((0,a.getDefaultSetting)("pdfBug")){o.dataset.fontName=t.fontName}if(c!==0){u.angle=c*(180/Math.PI)}if(t.str.length>1){if(f.vertical){u.canvasWidth=t.height*e._viewport.scale}else{u.canvasWidth=t.width*e._viewport.scale}}e._textDivProperties.set(o,u);if(e._textContentStream){e._layoutText(o)}if(e._enhanceTextSelection){var m=1,g=0;if(c!==0){m=Math.cos(c);g=Math.sin(c)}var b=(f.vertical?t.height:t.width)*e._viewport.scale;var y=d;var _,A;if(c!==0){_=[m,g,-g,m,v,p];A=n.Util.getAxialAlignedBoundingBox([0,0,b,y],_)}else{A=[v,p,v+b,p+y]}e._bounds.push({left:A[0],top:A[1],right:A[2],bottom:A[3],div:o,size:[b,y],m:_})}}function u(e){if(e._canceled){return}var r=e._textDivs;var n=e._capability;var a=r.length;if(a>t){e._renderingDone=true;n.resolve();return}if(!e._textContentStream){for(var i=0;i<a;i++){e._layoutText(r[i])}}e._renderingDone=true;n.resolve()}function l(e){var t=e._bounds;var r=e._viewport;var a=c(r.width,r.height,t);for(var i=0;i<a.length;i++){var s=t[i].div;var o=e._textDivProperties.get(s);if(o.angle===0){o.paddingLeft=t[i].left-a[i].left;o.paddingTop=t[i].top-a[i].top;o.paddingRight=a[i].right-t[i].right;o.paddingBottom=a[i].bottom-t[i].bottom;e._textDivProperties.set(s,o);continue}var u=a[i],l=t[i];var f=l.m,d=f[0],h=f[1];var v=[[0,0],[0,l.size[1]],[l.size[0],0],l.size];var p=new Float64Array(64);v.forEach(function(e,t){var r=n.Util.applyTransform(e,f);p[t+0]=d&&(u.left-r[0])/d;p[t+4]=h&&(u.top-r[1])/h;p[t+8]=d&&(u.right-r[0])/d;p[t+12]=h&&(u.bottom-r[1])/h;p[t+16]=h&&(u.left-r[0])/-h;p[t+20]=d&&(u.top-r[1])/d;p[t+24]=h&&(u.right-r[0])/-h;p[t+28]=d&&(u.bottom-r[1])/d;p[t+32]=d&&(u.left-r[0])/-d;p[t+36]=h&&(u.top-r[1])/-h;p[t+40]=d&&(u.right-r[0])/-d;p[t+44]=h&&(u.bottom-r[1])/-h;p[t+48]=h&&(u.left-r[0])/h;p[t+52]=d&&(u.top-r[1])/-d;p[t+56]=h&&(u.right-r[0])/h;p[t+60]=d&&(u.bottom-r[1])/-d});var m=function e(t,r,n){var a=0;for(var i=0;i<n;i++){var s=t[r++];if(s>0){a=a?Math.min(s,a):s}}return a};var g=1+Math.min(Math.abs(d),Math.abs(h));o.paddingLeft=m(p,32,16)/g;o.paddingTop=m(p,48,16)/g;o.paddingRight=m(p,0,16)/g;o.paddingBottom=m(p,16,16)/g;e._textDivProperties.set(s,o)}}function c(e,t,r){var n=r.map(function(e,t){return{x1:e.left,y1:e.top,x2:e.right,y2:e.bottom,index:t,x1New:undefined,x2New:undefined}});f(e,n);var a=new Array(r.length);n.forEach(function(e){var t=e.index;a[t]={left:e.x1New,top:0,right:e.x2New,bottom:0}});r.map(function(t,r){var i=a[r],s=n[r];s.x1=t.top;s.y1=e-i.right;s.x2=t.bottom;s.y2=e-i.left;s.index=r;s.x1New=undefined;s.x2New=undefined});f(t,n);n.forEach(function(e){var t=e.index;a[t].top=e.x1New;a[t].bottom=e.x2New});return a}function f(e,t){t.sort(function(e,t){return e.x1-t.x1||e.index-t.index});var r={x1:-Infinity,y1:-Infinity,x2:0,y2:Infinity,index:-1,x1New:0,x2New:0};var n=[{start:-Infinity,end:Infinity,boundary:r}];t.forEach(function(e){var t=0;while(t<n.length&&n[t].end<=e.y1){t++}var r=n.length-1;while(r>=0&&n[r].start>=e.y2){r--}var a,i;var s,o,u=-Infinity;for(s=t;s<=r;s++){a=n[s];i=a.boundary;var l;if(i.x2>e.x1){l=i.index>e.index?i.x1New:e.x1}else if(i.x2New===undefined){l=(i.x2+e.x1)/2}else{l=i.x2New}if(l>u){u=l}}e.x1New=u;for(s=t;s<=r;s++){a=n[s];i=a.boundary;if(i.x2New===undefined){if(i.x2>e.x1){if(i.index>e.index){i.x2New=i.x2}}else{i.x2New=u}}else if(i.x2New>u){i.x2New=Math.max(u,i.x2)}}var c=[],f=null;for(s=t;s<=r;s++){a=n[s];i=a.boundary;var d=i.x2>e.x2?i:e;if(f===d){c[c.length-1].end=a.end}else{c.push({start:a.start,end:a.end,boundary:d});f=d}}if(n[t].start<e.y1){c[0].start=e.y1;c.unshift({start:n[t].start,end:e.y1,boundary:n[t].boundary})}if(e.y2<n[r].end){c[c.length-1].end=e.y2;c.push({start:e.y2,end:n[r].end,boundary:n[r].boundary})}for(s=t;s<=r;s++){a=n[s];i=a.boundary;if(i.x2New!==undefined){continue}var h=false;for(o=t-1;!h&&o>=0&&n[o].start>=i.y1;o--){h=n[o].boundary===i}for(o=r+1;!h&&o<n.length&&n[o].end<=i.y2;o++){h=n[o].boundary===i}for(o=0;!h&&o<c.length;o++){h=c[o].boundary===i}if(!h){i.x2New=u}}Array.prototype.splice.apply(n,[t,r-t+1].concat(c))});n.forEach(function(t){var r=t.boundary;if(r.x2New===undefined){r.x2New=Math.max(e,r.x2)}})}function d(e){var t=e.textContent,r=e.textContentStream,a=e.container,i=e.viewport,s=e.textDivs,o=e.textContentItemsStr,u=e.enhanceTextSelection;this._textContent=t;this._textContentStream=r;this._container=a;this._viewport=i;this._textDivs=s||[];this._textContentItemsStr=o||[];this._enhanceTextSelection=!!u;this._reader=null;this._layoutTextLastFontSize=null;this._layoutTextLastFontFamily=null;this._layoutTextCtx=null;this._textDivProperties=new WeakMap;this._renderingDone=false;this._canceled=false;this._capability=(0,n.createPromiseCapability)();this._renderTimer=null;this._bounds=[]}d.prototype={get promise(){return this._capability.promise},cancel:function e(){if(this._reader){this._reader.cancel(new n.AbortException("text layer task cancelled"));this._reader=null}this._canceled=true;if(this._renderTimer!==null){clearTimeout(this._renderTimer);this._renderTimer=null}this._capability.reject("canceled")},_processItems:function e(t,r){for(var n=0,a=t.length;n<a;n++){this._textContentItemsStr.push(t[n].str);o(this,t[n],r)}},_layoutText:function e(t){var r=this._container;var n=this._textDivProperties.get(t);if(n.isWhitespace){return}var i=t.style.fontSize;var s=t.style.fontFamily;if(i!==this._layoutTextLastFontSize||s!==this._layoutTextLastFontFamily){this._layoutTextCtx.font=i+" "+s;this._lastFontSize=i;this._lastFontFamily=s}var o=this._layoutTextCtx.measureText(t.textContent).width;var u="";if(n.canvasWidth!==0&&o>0){n.scale=n.canvasWidth/o;u="scaleX("+n.scale+")"}if(n.angle!==0){u="rotate("+n.angle+"deg) "+u}if(u!==""){n.originalTransform=u;a.CustomStyle.setProp("transform",t,u)}this._textDivProperties.set(t,n);r.appendChild(t)},_render:function e(t){var r=this;var a=(0,n.createPromiseCapability)();var i=Object.create(null);var s=document.createElement("canvas");s.mozOpaque=true;this._layoutTextCtx=s.getContext("2d",{alpha:false});if(this._textContent){var o=this._textContent.items;var l=this._textContent.styles;this._processItems(o,l);a.resolve()}else if(this._textContentStream){var c=function e(){r._reader.read().then(function(t){var s=t.value,o=t.done;if(o){a.resolve();return}n.Util.extendObj(i,s.styles);r._processItems(s.items,i);e()},a.reject)};this._reader=this._textContentStream.getReader();c()}else{throw new Error('Neither "textContent" nor "textContentStream"'+" parameters specified.")}a.promise.then(function(){i=null;if(!t){u(r)}else{r._renderTimer=setTimeout(function(){u(r);r._renderTimer=null},t)}},this._capability.reject)},expandTextDivs:function e(t){if(!this._enhanceTextSelection||!this._renderingDone){return}if(this._bounds!==null){l(this);this._bounds=null}for(var r=0,n=this._textDivs.length;r<n;r++){var i=this._textDivs[r];var s=this._textDivProperties.get(i);if(s.isWhitespace){continue}if(t){var o="",u="";if(s.scale!==1){o="scaleX("+s.scale+")"}if(s.angle!==0){o="rotate("+s.angle+"deg) "+o}if(s.paddingLeft!==0){u+=" padding-left: "+s.paddingLeft/s.scale+"px;";o+=" translateX("+-s.paddingLeft/s.scale+"px)"}if(s.paddingTop!==0){u+=" padding-top: "+s.paddingTop+"px;";o+=" translateY("+-s.paddingTop+"px)"}if(s.paddingRight!==0){u+=" padding-right: "+s.paddingRight/s.scale+"px;"}if(s.paddingBottom!==0){u+=" padding-bottom: "+s.paddingBottom+"px;"}if(u!==""){i.setAttribute("style",s.style+u)}if(o!==""){a.CustomStyle.setProp("transform",i,o)}}else{i.style.padding=0;a.CustomStyle.setProp("transform",i,s.originalTransform||"")}}}};function h(e){var t=new d({textContent:e.textContent,textContentStream:e.textContentStream,container:e.container,viewport:e.viewport,textDivs:e.textDivs,textContentItemsStr:e.textContentItemsStr,enhanceTextSelection:e.enhanceTextSelection});t._render(e.timeout);return t}return h}();t.renderTextLayer=i},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.SVGGraphics=undefined;var n=r(0);var a=r(15);var i=function e(){throw new Error("Not implemented: SVGGraphics")};{var s={fontStyle:"normal",fontWeight:"normal",fillColor:"#000000"};var o=function e(){var t=new Uint8Array([137,80,78,71,13,10,26,10]);var r=12;var a=new Int32Array(256);for(var i=0;i<256;i++){var s=i;for(var o=0;o<8;o++){if(s&1){s=3988292384^s>>1&2147483647}else{s=s>>1&2147483647}}a[i]=s}function u(e,t,r){var n=-1;for(var i=t;i<r;i++){var s=(n^e[i])&255;var o=a[s];n=n>>>8^o}return n^-1}function l(e,t,r,n){var a=n;var i=t.length;r[a]=i>>24&255;r[a+1]=i>>16&255;r[a+2]=i>>8&255;r[a+3]=i&255;a+=4;r[a]=e.charCodeAt(0)&255;r[a+1]=e.charCodeAt(1)&255;r[a+2]=e.charCodeAt(2)&255;r[a+3]=e.charCodeAt(3)&255;a+=4;r.set(t,a);a+=t.length;var s=u(r,n+4,a);r[a]=s>>24&255;r[a+1]=s>>16&255;r[a+2]=s>>8&255;r[a+3]=s&255}function c(e,t,r){var n=1;var a=0;for(var i=t;i<r;++i){n=(n+(e[i]&255))%65521;a=(a+n)%65521}return a<<16|n}function f(e){if(!(0,n.isNodeJS)()){return d(e)}try{var t;if(parseInt(process.versions.node)>=8){t=e}else{t=new Buffer(e)}var r=require("zlib").deflateSync(t,{level:9});return r instanceof Uint8Array?r:new Uint8Array(r)}catch(e){(0,n.warn)("Not compressing PNG because zlib.deflateSync is unavailable: "+e)}return d(e)}function d(e){var t=e.length;var r=65535;var n=Math.ceil(t/r);var a=new Uint8Array(2+t+n*5+4);var i=0;a[i++]=120;a[i++]=156;var s=0;while(t>r){a[i++]=0;a[i++]=255;a[i++]=255;a[i++]=0;a[i++]=0;a.set(e.subarray(s,s+r),i);i+=r;s+=r;t-=r}a[i++]=1;a[i++]=t&255;a[i++]=t>>8&255;a[i++]=~t&65535&255;a[i++]=(~t&65535)>>8&255;a.set(e.subarray(s),i);i+=e.length-s;var o=c(e,0,e.length);a[i++]=o>>24&255;a[i++]=o>>16&255;a[i++]=o>>8&255;a[i++]=o&255;return a}function h(e,a,i){var s=e.width;var o=e.height;var u,c,d;var h=e.data;switch(a){case n.ImageKind.GRAYSCALE_1BPP:c=0;u=1;d=s+7>>3;break;case n.ImageKind.RGB_24BPP:c=2;u=8;d=s*3;break;case n.ImageKind.RGBA_32BPP:c=6;u=8;d=s*4;break;default:throw new Error("invalid format")}var v=new Uint8Array((1+d)*o);var p=0,m=0;var g,b;for(g=0;g<o;++g){v[p++]=0;v.set(h.subarray(m,m+d),p);m+=d;p+=d}if(a===n.ImageKind.GRAYSCALE_1BPP){p=0;for(g=0;g<o;g++){p++;for(b=0;b<d;b++){v[p++]^=255}}}var y=new Uint8Array([s>>24&255,s>>16&255,s>>8&255,s&255,o>>24&255,o>>16&255,o>>8&255,o&255,u,c,0,0,0]);var _=f(v);var A=t.length+r*3+y.length+_.length;var S=new Uint8Array(A);var w=0;S.set(t,w);w+=t.length;l("IHDR",y,S,w);w+=r+y.length;l("IDATA",_,S,w);w+=r+_.length;l("IEND",new Uint8Array(0),S,w);return(0,n.createObjectURL)(S,"image/png",i)}return function e(t,r){var a=t.kind===undefined?n.ImageKind.GRAYSCALE_1BPP:t.kind;return h(t,a,r)}}();var u=function e(){function t(){this.fontSizeScale=1;this.fontWeight=s.fontWeight;this.fontSize=0;this.textMatrix=n.IDENTITY_MATRIX;this.fontMatrix=n.FONT_IDENTITY_MATRIX;this.leading=0;this.x=0;this.y=0;this.lineX=0;this.lineY=0;this.charSpacing=0;this.wordSpacing=0;this.textHScale=1;this.textRise=0;this.fillColor=s.fillColor;this.strokeColor="#000000";this.fillAlpha=1;this.strokeAlpha=1;this.lineWidth=1;this.lineJoin="";this.lineCap="";this.miterLimit=0;this.dashArray=[];this.dashPhase=0;this.dependencies=[];this.activeClipUrl=null;this.clipGroup=null;this.maskId=""}t.prototype={clone:function e(){return Object.create(this)},setCurrentPoint:function e(t,r){this.x=t;this.y=r}};return t}();t.SVGGraphics=i=function e(){function t(e){var t=[];var r=[];var n=e.length;for(var a=0;a<n;a++){if(e[a].fn==="save"){t.push({fnId:92,fn:"group",items:[]});r.push(t);t=t[t.length-1].items;continue}if(e[a].fn==="restore"){t=r.pop()}else{t.push(e[a])}}return t}function r(e){if(Number.isInteger(e)){return e.toString()}var t=e.toFixed(10);var r=t.length-1;if(t[r]!=="0"){return t}do{r--}while(t[r]==="0");return t.substr(0,t[r]==="."?r:r+1)}function i(e){if(e[4]===0&&e[5]===0){if(e[1]===0&&e[2]===0){if(e[0]===1&&e[3]===1){return""}return"scale("+r(e[0])+" "+r(e[3])+")"}if(e[0]===e[3]&&e[1]===-e[2]){var t=Math.acos(e[0])*180/Math.PI;return"rotate("+r(t)+")"}}else{if(e[0]===1&&e[1]===0&&e[2]===0&&e[3]===1){return"translate("+r(e[4])+" "+r(e[5])+")"}}return"matrix("+r(e[0])+" "+r(e[1])+" "+r(e[2])+" "+r(e[3])+" "+r(e[4])+" "+r(e[5])+")"}function l(e,t,r){this.svgFactory=new a.DOMSVGFactory;this.current=new u;this.transformMatrix=n.IDENTITY_MATRIX;this.transformStack=[];this.extraStack=[];this.commonObjs=e;this.objs=t;this.pendingClip=null;this.pendingEOFill=false;this.embedFonts=false;this.embeddedFonts=Object.create(null);this.cssStyle=null;this.forceDataSchema=!!r}var c="http://www.w3.org/XML/1998/namespace";var f="http://www.w3.org/1999/xlink";var d=["butt","round","square"];var h=["miter","round","bevel"];var v=0;var p=0;l.prototype={save:function e(){this.transformStack.push(this.transformMatrix);var t=this.current;this.extraStack.push(t);this.current=t.clone()},restore:function e(){this.transformMatrix=this.transformStack.pop();this.current=this.extraStack.pop();this.pendingClip=null;this.tgrp=null},group:function e(t){this.save();this.executeOpTree(t);this.restore()},loadDependencies:function e(t){var r=this;var a=t.fnArray;var i=a.length;var s=t.argsArray;for(var o=0;o<i;o++){if(n.OPS.dependency===a[o]){var u=s[o];for(var l=0,c=u.length;l<c;l++){var f=u[l];var d=f.substring(0,2)==="g_";var h;if(d){h=new Promise(function(e){r.commonObjs.get(f,e)})}else{h=new Promise(function(e){r.objs.get(f,e)})}this.current.dependencies.push(h)}}}return Promise.all(this.current.dependencies)},transform:function e(t,r,a,i,s,o){var u=[t,r,a,i,s,o];this.transformMatrix=n.Util.transform(this.transformMatrix,u);this.tgrp=null},getSVG:function e(t,r){var a=this;this.viewport=r;var i=this._initialize(r);return this.loadDependencies(t).then(function(){a.transformMatrix=n.IDENTITY_MATRIX;var e=a.convertOpList(t);a.executeOpTree(e);return i})},convertOpList:function e(r){var a=r.argsArray;var i=r.fnArray;var s=i.length;var o=[];var u=[];for(var l in n.OPS){o[n.OPS[l]]=l}for(var c=0;c<s;c++){var f=i[c];u.push({fnId:f,fn:o[f],args:a[c]})}return t(u)},executeOpTree:function e(t){var r=t.length;for(var a=0;a<r;a++){var i=t[a].fn;var s=t[a].fnId;var o=t[a].args;switch(s|0){case n.OPS.beginText:this.beginText();break;case n.OPS.setLeading:this.setLeading(o);break;case n.OPS.setLeadingMoveText:this.setLeadingMoveText(o[0],o[1]);break;case n.OPS.setFont:this.setFont(o);break;case n.OPS.showText:this.showText(o[0]);break;case n.OPS.showSpacedText:this.showText(o[0]);break;case n.OPS.endText:this.endText();break;case n.OPS.moveText:this.moveText(o[0],o[1]);break;case n.OPS.setCharSpacing:this.setCharSpacing(o[0]);break;case n.OPS.setWordSpacing:this.setWordSpacing(o[0]);break;case n.OPS.setHScale:this.setHScale(o[0]);break;case n.OPS.setTextMatrix:this.setTextMatrix(o[0],o[1],o[2],o[3],o[4],o[5]);break;case n.OPS.setTextRise:this.setTextRise(o[0]);break;case n.OPS.setLineWidth:this.setLineWidth(o[0]);break;case n.OPS.setLineJoin:this.setLineJoin(o[0]);break;case n.OPS.setLineCap:this.setLineCap(o[0]);break;case n.OPS.setMiterLimit:this.setMiterLimit(o[0]);break;case n.OPS.setFillRGBColor:this.setFillRGBColor(o[0],o[1],o[2]);break;case n.OPS.setStrokeRGBColor:this.setStrokeRGBColor(o[0],o[1],o[2]);break;case n.OPS.setDash:this.setDash(o[0],o[1]);break;case n.OPS.setGState:this.setGState(o[0]);break;case n.OPS.fill:this.fill();break;case n.OPS.eoFill:this.eoFill();break;case n.OPS.stroke:this.stroke();break;case n.OPS.fillStroke:this.fillStroke();break;case n.OPS.eoFillStroke:this.eoFillStroke();break;case n.OPS.clip:this.clip("nonzero");break;case n.OPS.eoClip:this.clip("evenodd");break;case n.OPS.paintSolidColorImageMask:this.paintSolidColorImageMask();break;case n.OPS.paintJpegXObject:this.paintJpegXObject(o[0],o[1],o[2]);break;case n.OPS.paintImageXObject:this.paintImageXObject(o[0]);break;case n.OPS.paintInlineImageXObject:this.paintInlineImageXObject(o[0]);break;case n.OPS.paintImageMaskXObject:this.paintImageMaskXObject(o[0]);break;case n.OPS.paintFormXObjectBegin:this.paintFormXObjectBegin(o[0],o[1]);break;case n.OPS.paintFormXObjectEnd:this.paintFormXObjectEnd();break;case n.OPS.closePath:this.closePath();break;case n.OPS.closeStroke:this.closeStroke();break;case n.OPS.closeFillStroke:this.closeFillStroke();break;case n.OPS.nextLine:this.nextLine();break;case n.OPS.transform:this.transform(o[0],o[1],o[2],o[3],o[4],o[5]);break;case n.OPS.constructPath:this.constructPath(o[0],o[1]);break;case n.OPS.endPath:this.endPath();break;case 92:this.group(t[a].items);break;default:(0,n.warn)("Unimplemented operator "+i);break}}},setWordSpacing:function e(t){this.current.wordSpacing=t},setCharSpacing:function e(t){this.current.charSpacing=t},nextLine:function e(){this.moveText(0,this.current.leading)},setTextMatrix:function e(t,n,a,i,s,o){var u=this.current;this.current.textMatrix=this.current.lineMatrix=[t,n,a,i,s,o];this.current.x=this.current.lineX=0;this.current.y=this.current.lineY=0;u.xcoords=[];u.tspan=this.svgFactory.createElement("svg:tspan");u.tspan.setAttributeNS(null,"font-family",u.fontFamily);u.tspan.setAttributeNS(null,"font-size",r(u.fontSize)+"px");u.tspan.setAttributeNS(null,"y",r(-u.y));u.txtElement=this.svgFactory.createElement("svg:text");u.txtElement.appendChild(u.tspan)},beginText:function e(){this.current.x=this.current.lineX=0;this.current.y=this.current.lineY=0;this.current.textMatrix=n.IDENTITY_MATRIX;this.current.lineMatrix=n.IDENTITY_MATRIX;this.current.tspan=this.svgFactory.createElement("svg:tspan");this.current.txtElement=this.svgFactory.createElement("svg:text");this.current.txtgrp=this.svgFactory.createElement("svg:g");this.current.xcoords=[]},moveText:function e(t,n){var a=this.current;this.current.x=this.current.lineX+=t;this.current.y=this.current.lineY+=n;a.xcoords=[];a.tspan=this.svgFactory.createElement("svg:tspan");a.tspan.setAttributeNS(null,"font-family",a.fontFamily);a.tspan.setAttributeNS(null,"font-size",r(a.fontSize)+"px");a.tspan.setAttributeNS(null,"y",r(-a.y))},showText:function e(t){var a=this.current;var o=a.font;var u=a.fontSize;if(u===0){return}var l=a.charSpacing;var f=a.wordSpacing;var d=a.fontDirection;var h=a.textHScale*d;var v=t.length;var p=o.vertical;var m=u*a.fontMatrix[0];var g=0,b;for(b=0;b<v;++b){var y=t[b];if(y===null){g+=d*f;continue}else if((0,n.isNum)(y)){g+=-y*u*.001;continue}var _=y.width;var A=y.fontChar;var S=(y.isSpace?f:0)+l;var w=_*m+S*d;if(!y.isInFont&&!o.missingFile){g+=w;continue}a.xcoords.push(a.x+g*h);a.tspan.textContent+=A;g+=w}if(p){a.y-=g*h}else{a.x+=g*h}a.tspan.setAttributeNS(null,"x",a.xcoords.map(r).join(" "));a.tspan.setAttributeNS(null,"y",r(-a.y));a.tspan.setAttributeNS(null,"font-family",a.fontFamily);a.tspan.setAttributeNS(null,"font-size",r(a.fontSize)+"px");if(a.fontStyle!==s.fontStyle){a.tspan.setAttributeNS(null,"font-style",a.fontStyle)}if(a.fontWeight!==s.fontWeight){a.tspan.setAttributeNS(null,"font-weight",a.fontWeight)}if(a.fillColor!==s.fillColor){a.tspan.setAttributeNS(null,"fill",a.fillColor)}var P=a.textMatrix;if(a.textRise!==0){P=P.slice();P[5]+=a.textRise}a.txtElement.setAttributeNS(null,"transform",i(P)+" scale(1, -1)");a.txtElement.setAttributeNS(c,"xml:space","preserve");a.txtElement.appendChild(a.tspan);a.txtgrp.appendChild(a.txtElement);this._ensureTransformGroup().appendChild(a.txtElement)},setLeadingMoveText:function e(t,r){this.setLeading(-r);this.moveText(t,r)},addFontStyle:function e(t){if(!this.cssStyle){this.cssStyle=this.svgFactory.createElement("svg:style");this.cssStyle.setAttributeNS(null,"type","text/css");this.defs.appendChild(this.cssStyle)}var r=(0,n.createObjectURL)(t.data,t.mimetype,this.forceDataSchema);this.cssStyle.textContent+='@font-face { font-family: "'+t.loadedName+'";'+" src: url("+r+"); }\n"},setFont:function e(t){var a=this.current;var i=this.commonObjs.get(t[0]);var s=t[1];this.current.font=i;if(this.embedFonts&&i.data&&!this.embeddedFonts[i.loadedName]){this.addFontStyle(i);this.embeddedFonts[i.loadedName]=i}a.fontMatrix=i.fontMatrix?i.fontMatrix:n.FONT_IDENTITY_MATRIX;var o=i.black?i.bold?"bolder":"bold":i.bold?"bold":"normal";var u=i.italic?"italic":"normal";if(s<0){s=-s;a.fontDirection=-1}else{a.fontDirection=1}a.fontSize=s;a.fontFamily=i.loadedName;a.fontWeight=o;a.fontStyle=u;a.tspan=this.svgFactory.createElement("svg:tspan");a.tspan.setAttributeNS(null,"y",r(-a.y));a.xcoords=[]},endText:function e(){},setLineWidth:function e(t){this.current.lineWidth=t},setLineCap:function e(t){this.current.lineCap=d[t]},setLineJoin:function e(t){this.current.lineJoin=h[t]},setMiterLimit:function e(t){this.current.miterLimit=t},setStrokeAlpha:function e(t){this.current.strokeAlpha=t},setStrokeRGBColor:function e(t,r,a){var i=n.Util.makeCssRgb(t,r,a);this.current.strokeColor=i},setFillAlpha:function e(t){this.current.fillAlpha=t},setFillRGBColor:function e(t,r,a){var i=n.Util.makeCssRgb(t,r,a);this.current.fillColor=i;this.current.tspan=this.svgFactory.createElement("svg:tspan");this.current.xcoords=[]},setDash:function e(t,r){this.current.dashArray=t;this.current.dashPhase=r},constructPath:function e(t,a){var i=this.current;var s=i.x,o=i.y;i.path=this.svgFactory.createElement("svg:path");var u=[];var l=t.length;for(var c=0,f=0;c<l;c++){switch(t[c]|0){case n.OPS.rectangle:s=a[f++];o=a[f++];var d=a[f++];var h=a[f++];var v=s+d;var p=o+h;u.push("M",r(s),r(o),"L",r(v),r(o),"L",r(v),r(p),"L",r(s),r(p),"Z");break;case n.OPS.moveTo:s=a[f++];o=a[f++];u.push("M",r(s),r(o));break;case n.OPS.lineTo:s=a[f++];o=a[f++];u.push("L",r(s),r(o));break;case n.OPS.curveTo:s=a[f+4];o=a[f+5];u.push("C",r(a[f]),r(a[f+1]),r(a[f+2]),r(a[f+3]),r(s),r(o));f+=6;break;case n.OPS.curveTo2:s=a[f+2];o=a[f+3];u.push("C",r(s),r(o),r(a[f]),r(a[f+1]),r(a[f+2]),r(a[f+3]));f+=4;break;case n.OPS.curveTo3:s=a[f+2];o=a[f+3];u.push("C",r(a[f]),r(a[f+1]),r(s),r(o),r(s),r(o));f+=4;break;case n.OPS.closePath:u.push("Z");break}}i.path.setAttributeNS(null,"d",u.join(" "));i.path.setAttributeNS(null,"fill","none");this._ensureTransformGroup().appendChild(i.path);i.element=i.path;i.setCurrentPoint(s,o)},endPath:function e(){if(!this.pendingClip){return}var t=this.current;var r="clippath"+v;v++;var n=this.svgFactory.createElement("svg:clipPath");n.setAttributeNS(null,"id",r);n.setAttributeNS(null,"transform",i(this.transformMatrix));var a=t.element.cloneNode();if(this.pendingClip==="evenodd"){a.setAttributeNS(null,"clip-rule","evenodd")}else{a.setAttributeNS(null,"clip-rule","nonzero")}this.pendingClip=null;n.appendChild(a);this.defs.appendChild(n);if(t.activeClipUrl){t.clipGroup=null;this.extraStack.forEach(function(e){e.clipGroup=null})}t.activeClipUrl="url(#"+r+")";this.tgrp=null},clip:function e(t){this.pendingClip=t},closePath:function e(){var t=this.current;var r=t.path.getAttributeNS(null,"d");r+="Z";t.path.setAttributeNS(null,"d",r)},setLeading:function e(t){this.current.leading=-t},setTextRise:function e(t){this.current.textRise=t},setHScale:function e(t){this.current.textHScale=t/100},setGState:function e(t){for(var r=0,a=t.length;r<a;r++){var i=t[r];var s=i[0];var o=i[1];switch(s){case"LW":this.setLineWidth(o);break;case"LC":this.setLineCap(o);break;case"LJ":this.setLineJoin(o);break;case"ML":this.setMiterLimit(o);break;case"D":this.setDash(o[0],o[1]);break;case"Font":this.setFont(o);break;case"CA":this.setStrokeAlpha(o);break;case"ca":this.setFillAlpha(o);break;default:(0,n.warn)("Unimplemented graphic state "+s);break}}},fill:function e(){var t=this.current;t.element.setAttributeNS(null,"fill",t.fillColor);t.element.setAttributeNS(null,"fill-opacity",t.fillAlpha)},stroke:function e(){var t=this.current;t.element.setAttributeNS(null,"stroke",t.strokeColor);t.element.setAttributeNS(null,"stroke-opacity",t.strokeAlpha);t.element.setAttributeNS(null,"stroke-miterlimit",r(t.miterLimit));t.element.setAttributeNS(null,"stroke-linecap",t.lineCap);t.element.setAttributeNS(null,"stroke-linejoin",t.lineJoin);t.element.setAttributeNS(null,"stroke-width",r(t.lineWidth)+"px");t.element.setAttributeNS(null,"stroke-dasharray",t.dashArray.map(r).join(" "));t.element.setAttributeNS(null,"stroke-dashoffset",r(t.dashPhase)+"px");t.element.setAttributeNS(null,"fill","none")},eoFill:function e(){this.current.element.setAttributeNS(null,"fill-rule","evenodd");this.fill()},fillStroke:function e(){this.stroke();this.fill()},eoFillStroke:function e(){this.current.element.setAttributeNS(null,"fill-rule","evenodd");this.fillStroke()},closeStroke:function e(){this.closePath();this.stroke()},closeFillStroke:function e(){this.closePath();this.fillStroke()},paintSolidColorImageMask:function e(){var t=this.current;var r=this.svgFactory.createElement("svg:rect");r.setAttributeNS(null,"x","0");r.setAttributeNS(null,"y","0");r.setAttributeNS(null,"width","1px");r.setAttributeNS(null,"height","1px");r.setAttributeNS(null,"fill",t.fillColor);this._ensureTransformGroup().appendChild(r)},paintJpegXObject:function e(t,n,a){var i=this.objs.get(t);var s=this.svgFactory.createElement("svg:image");s.setAttributeNS(f,"xlink:href",i.src);s.setAttributeNS(null,"width",r(n));s.setAttributeNS(null,"height",r(a));s.setAttributeNS(null,"x","0");s.setAttributeNS(null,"y",r(-a));s.setAttributeNS(null,"transform","scale("+r(1/n)+" "+r(-1/a)+")");this._ensureTransformGroup().appendChild(s)},paintImageXObject:function e(t){var r=this.objs.get(t);if(!r){(0,n.warn)("Dependent image isn't ready yet");return}this.paintInlineImageXObject(r)},paintInlineImageXObject:function e(t,n){var a=t.width;var i=t.height;var s=o(t,this.forceDataSchema);var u=this.svgFactory.createElement("svg:rect");u.setAttributeNS(null,"x","0");u.setAttributeNS(null,"y","0");u.setAttributeNS(null,"width",r(a));u.setAttributeNS(null,"height",r(i));this.current.element=u;this.clip("nonzero");var l=this.svgFactory.createElement("svg:image");l.setAttributeNS(f,"xlink:href",s);l.setAttributeNS(null,"x","0");l.setAttributeNS(null,"y",r(-i));l.setAttributeNS(null,"width",r(a)+"px");l.setAttributeNS(null,"height",r(i)+"px");l.setAttributeNS(null,"transform","scale("+r(1/a)+" "+r(-1/i)+")");if(n){n.appendChild(l)}else{this._ensureTransformGroup().appendChild(l)}},paintImageMaskXObject:function e(t){var n=this.current;var a=t.width;var i=t.height;var s=n.fillColor;n.maskId="mask"+p++;var o=this.svgFactory.createElement("svg:mask");o.setAttributeNS(null,"id",n.maskId);var u=this.svgFactory.createElement("svg:rect");u.setAttributeNS(null,"x","0");u.setAttributeNS(null,"y","0");u.setAttributeNS(null,"width",r(a));u.setAttributeNS(null,"height",r(i));u.setAttributeNS(null,"fill",s);u.setAttributeNS(null,"mask","url(#"+n.maskId+")");this.defs.appendChild(o);this._ensureTransformGroup().appendChild(u);this.paintInlineImageXObject(t,o)},paintFormXObjectBegin:function e(t,n){if(Array.isArray(t)&&t.length===6){this.transform(t[0],t[1],t[2],t[3],t[4],t[5])}if(Array.isArray(n)&&n.length===4){var a=n[2]-n[0];var i=n[3]-n[1];var s=this.svgFactory.createElement("svg:rect");s.setAttributeNS(null,"x",n[0]);s.setAttributeNS(null,"y",n[1]);s.setAttributeNS(null,"width",r(a));s.setAttributeNS(null,"height",r(i));this.current.element=s;this.clip("nonzero");this.endPath()}},paintFormXObjectEnd:function e(){},_initialize:function e(t){var r=this.svgFactory.create(t.width,t.height);var n=this.svgFactory.createElement("svg:defs");r.appendChild(n);this.defs=n;var a=this.svgFactory.createElement("svg:g");a.setAttributeNS(null,"transform",i(t.transform));r.appendChild(a);this.svg=a;return r},_ensureClipGroup:function e(){if(!this.current.clipGroup){var t=this.svgFactory.createElement("svg:g");t.setAttributeNS(null,"clip-path",this.current.activeClipUrl);this.svg.appendChild(t);this.current.clipGroup=t}return this.current.clipGroup},_ensureTransformGroup:function e(){if(!this.tgrp){this.tgrp=this.svgFactory.createElement("svg:g");this.tgrp.setAttributeNS(null,"transform",i(this.transformMatrix));if(this.current.activeClipUrl){this._ensureClipGroup().appendChild(this.tgrp)}else{this.svg.appendChild(this.tgrp)}}return this.tgrp}};return l}()}t.SVGGraphics=i},function(e,t,r){"use strict";var n="1.10.100";var a="ea29ec83";var i=r(0);var s=r(118);var o=r(69);var u=r(73);var l=r(72);var c=r(15);var f=r(74);{if(i.isNodeJS()){var d=r(124).PDFNodeStream;o.setPDFNetworkStreamClass(d)}else if(typeof Response!=="undefined"&&"body"in Response.prototype&&typeof ReadableStream!=="undefined"){var h=r(125).PDFFetchStream;o.setPDFNetworkStreamClass(h)}else{var v=r(126).PDFNetworkStream;o.setPDFNetworkStreamClass(v)}}t.PDFJS=s.PDFJS;t.build=o.build;t.version=o.version;t.getDocument=o.getDocument;t.LoopbackPort=o.LoopbackPort;t.PDFDataRangeTransport=o.PDFDataRangeTransport;t.PDFWorker=o.PDFWorker;t.renderTextLayer=u.renderTextLayer;t.AnnotationLayer=l.AnnotationLayer;t.CustomStyle=c.CustomStyle;t.createPromiseCapability=i.createPromiseCapability;t.PasswordResponses=i.PasswordResponses;t.InvalidPDFException=i.InvalidPDFException;t.MissingPDFException=i.MissingPDFException;t.SVGGraphics=f.SVGGraphics;t.NativeImageDecoding=i.NativeImageDecoding;t.UnexpectedResponseException=i.UnexpectedResponseException;t.OPS=i.OPS;t.UNSUPPORTED_FEATURES=i.UNSUPPORTED_FEATURES;t.isValidUrl=c.isValidUrl;t.createValidAbsoluteUrl=i.createValidAbsoluteUrl;t.createObjectURL=i.createObjectURL;t.removeNullCharacters=i.removeNullCharacters;t.shadow=i.shadow;t.createBlob=i.createBlob;t.RenderingCancelledException=c.RenderingCancelledException;t.getFilenameFromUrl=c.getFilenameFromUrl;t.addLinkAttributes=c.addLinkAttributes;t.StatTimer=i.StatTimer},function(e,t,r){"use strict";var n=typeof Symbol==="function"&&typeof Symbol.iterator==="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol==="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};if(typeof PDFJS==="undefined"||!PDFJS.compatibilityChecked){var a=r(20);var i=typeof navigator!=="undefined"&&navigator.userAgent||"";var s=/Android/.test(i);var o=/Android\s[0-2][^\d]/.test(i);var u=/Android\s[0-4][^\d]/.test(i);var l=i.indexOf("Chrom")>=0;var c=/Chrome\/(39|40)\./.test(i);var f=i.indexOf("CriOS")>=0;var d=i.indexOf("Trident")>=0;var h=/\b(iPad|iPhone|iPod)(?=;)/.test(i);var v=i.indexOf("Opera")>=0;var p=/Safari\//.test(i)&&!/(Chrome\/|Android\s)/.test(i);var m=(typeof window==="undefined"?"undefined":n(window))==="object"&&(typeof document==="undefined"?"undefined":n(document))==="object";if(typeof PDFJS==="undefined"){a.PDFJS={}}PDFJS.compatibilityChecked=true;(function e(){if(typeof Uint8ClampedArray==="undefined"){a.Uint8ClampedArray=r(77)}if(typeof Uint8Array!=="undefined"){if(typeof Uint8Array.prototype.subarray==="undefined"){Uint8Array.prototype.subarray=function e(t,r){return new Uint8Array(this.slice(t,r))};Float32Array.prototype.subarray=function e(t,r){return new Float32Array(this.slice(t,r))}}if(typeof Float64Array==="undefined"){a.Float64Array=Float32Array}return}function t(e,t){return new c(this.slice(e,t))}function i(e,t){if(arguments.length<2){t=0}for(var r=0,n=e.length;r<n;++r,++t){this[t]=e[r]&255}}function s(e,t){this.buffer=e;this.byteLength=e.length;this.length=t;l(this.length)}s.prototype=Object.create(null);var o=0;function u(e){return{get:function t(){var r=this.buffer,n=e<<2;return(r[n]|r[n+1]<<8|r[n+2]<<16|r[n+3]<<24)>>>0},set:function t(r){var n=this.buffer,a=e<<2;n[a]=r&255;n[a+1]=r>>8&255;n[a+2]=r>>16&255;n[a+3]=r>>>24&255}}}function l(e){while(o<e){Object.defineProperty(s.prototype,o,u(o));o++}}function c(e){var r,a,s;if(typeof e==="number"){r=[];for(a=0;a<e;++a){r[a]=0}}else if("slice"in e){r=e.slice(0)}else{r=[];for(a=0,s=e.length;a<s;++a){r[a]=e[a]}}r.subarray=t;r.buffer=r;r.byteLength=r.length;r.set=i;if((typeof e==="undefined"?"undefined":n(e))==="object"&&e.buffer){r.buffer=e.buffer}return r}a.Uint8Array=c;a.Int8Array=c;a.Int32Array=c;a.Uint16Array=c;a.Float32Array=c;a.Float64Array=c;a.Uint32Array=function(){if(arguments.length===3){if(arguments[1]!==0){throw new Error("offset !== 0 is not supported")}return new s(arguments[0],arguments[2])}return c.apply(this,arguments)}})();(function e(){if(!m||!window.CanvasPixelArray){return}var t=window.CanvasPixelArray.prototype;if("buffer"in t){return}Object.defineProperty(t,"buffer",{get:function e(){return this},enumerable:false,configurable:true});Object.defineProperty(t,"byteLength",{get:function e(){return this.length},enumerable:false,configurable:true})})();(function e(){if(!a.URL){a.URL=a.webkitURL}})();(function e(){if(typeof Object.defineProperty!=="undefined"){var t=true;try{if(m){Object.defineProperty(new Image,"id",{value:"test"})}var r=function e(){};r.prototype={get id(){}};Object.defineProperty(new r,"id",{value:"",configurable:true,enumerable:true,writable:false})}catch(e){t=false}if(t){return}}Object.defineProperty=function e(t,r,n){delete t[r];if("get"in n){t.__defineGetter__(r,n["get"])}if("set"in n){t.__defineSetter__(r,n["set"])}if("value"in n){t.__defineSetter__(r,function e(t){this.__defineGetter__(r,function e(){return t});return t});t[r]=n.value}}})();(function e(){if(typeof XMLHttpRequest==="undefined"){return}var t=XMLHttpRequest.prototype;var r=new XMLHttpRequest;if(!("overrideMimeType"in r)){Object.defineProperty(t,"overrideMimeType",{value:function e(t){}})}if("responseType"in r){return}Object.defineProperty(t,"responseType",{get:function e(){return this._responseType||"text"},set:function e(t){if(t==="text"||t==="arraybuffer"){this._responseType=t;if(t==="arraybuffer"&&typeof this.overrideMimeType==="function"){this.overrideMimeType("text/plain; charset=x-user-defined")}}}});if(typeof VBArray!=="undefined"){Object.defineProperty(t,"response",{get:function e(){if(this.responseType==="arraybuffer"){return new Uint8Array(new VBArray(this.responseBody).toArray())}return this.responseText}});return}Object.defineProperty(t,"response",{get:function e(){if(this.responseType!=="arraybuffer"){return this.responseText}var t=this.responseText;var r,n=t.length;var a=new Uint8Array(n);for(r=0;r<n;++r){a[r]=t.charCodeAt(r)&255}return a.buffer}})})();(function e(){if("btoa"in a){return}var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";a.btoa=function(e){var r="";var n,a;for(n=0,a=e.length;n<a;n+=3){var i=e.charCodeAt(n)&255;var s=e.charCodeAt(n+1)&255;var o=e.charCodeAt(n+2)&255;var u=i>>2,l=(i&3)<<4|s>>4;var c=n+1<a?(s&15)<<2|o>>6:64;var f=n+2<a?o&63:64;r+=t.charAt(u)+t.charAt(l)+t.charAt(c)+t.charAt(f)}return r}})();(function e(){if("atob"in a){return}var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";a.atob=function(e){e=e.replace(/=+$/,"");if(e.length%4===1){throw new Error("bad atob input")}for(var r=0,n,a,i=0,s="";a=e.charAt(i++);~a&&(n=r%4?n*64+a:a,r++%4)?s+=String.fromCharCode(255&n>>(-2*r&6)):0){a=t.indexOf(a)}return s}})();(function e(){if(typeof Function.prototype.bind!=="undefined"){return}Function.prototype.bind=function e(t){var r=this,n=Array.prototype.slice.call(arguments,1);var a=function e(){var a=n.concat(Array.prototype.slice.call(arguments));return r.apply(t,a)};return a}})();(function e(){if(!m){return}var t=document.createElement("div");if("dataset"in t){return}Object.defineProperty(HTMLElement.prototype,"dataset",{get:function e(){if(this._dataset){return this._dataset}var t={};for(var r=0,n=this.attributes.length;r<n;r++){var a=this.attributes[r];if(a.name.substring(0,5)!=="data-"){continue}var i=a.name.substring(5).replace(/\-([a-z])/g,function(e,t){return t.toUpperCase()});t[i]=a.value}Object.defineProperty(this,"_dataset",{value:t,writable:false,enumerable:false});return t},enumerable:true})})();(function e(){function t(e,t,r,n){var a=e.className||"";var i=a.split(/\s+/g);if(i[0]===""){i.shift()}var s=i.indexOf(t);if(s<0&&r){i.push(t)}if(s>=0&&n){i.splice(s,1)}e.className=i.join(" ");return s>=0}if(!m){return}var r=document.createElement("div");if("classList"in r){return}var n={add:function e(r){t(this.element,r,true,false)},contains:function e(r){return t(this.element,r,false,false)},remove:function e(r){t(this.element,r,false,true)},toggle:function e(r){t(this.element,r,true,true)}};Object.defineProperty(HTMLElement.prototype,"classList",{get:function e(){if(this._classList){return this._classList}var t=Object.create(n,{element:{value:this,writable:false,enumerable:true}});Object.defineProperty(this,"_classList",{value:t,writable:false,enumerable:false});return t},enumerable:true})})();(function e(){if(typeof importScripts==="undefined"||"console"in a){return}var t={};var r={log:function e(){var t=Array.prototype.slice.call(arguments);a.postMessage({targetName:"main",action:"console_log",data:t})},error:function e(){var t=Array.prototype.slice.call(arguments);a.postMessage({targetName:"main",action:"console_error",data:t})},time:function e(r){t[r]=Date.now()},timeEnd:function e(r){var n=t[r];if(!n){throw new Error("Unknown timer name "+r)}this.log("Timer:",r,Date.now()-n)}};a.console=r})();(function e(){if(!m){return}if(!("console"in window)){window.console={log:function e(){},error:function e(){},warn:function e(){}};return}if(!("bind"in console.log)){console.log=function(e){return function(t){return e(t)}}(console.log);console.error=function(e){return function(t){return e(t)}}(console.error);console.warn=function(e){return function(t){return e(t)}}(console.warn);return}})();(function e(){function t(e){if(r(e.target)){e.stopPropagation()}}function r(e){return e.disabled||e.parentNode&&r(e.parentNode)}if(v){document.addEventListener("click",t,true)}})();(function e(){if(d||f){PDFJS.disableCreateObjectURL=true}})();(function e(){if(typeof navigator==="undefined"){return}if("language"in navigator){return}PDFJS.locale=navigator.userLanguage||"en-US"})();(function e(){if(p||o||c||h){PDFJS.disableRange=true;PDFJS.disableStream=true}})();(function e(){if(!m){return}if(!history.pushState||o){PDFJS.disableHistory=true}})();(function e(){if(!m){return}if(window.CanvasPixelArray){if(typeof window.CanvasPixelArray.prototype.set!=="function"){window.CanvasPixelArray.prototype.set=function(e){for(var t=0,r=this.length;t<r;t++){this[t]=e[t]}}}}else{var t=false,r;if(l){r=i.match(/Chrom(e|ium)\/([0-9]+)\./);t=r&&parseInt(r[2])<21}else if(s){t=u}else if(p){r=i.match(/Version\/([0-9]+)\.([0-9]+)\.([0-9]+) Safari\//);t=r&&parseInt(r[1])<6}if(t){var n=window.CanvasRenderingContext2D.prototype;var a=n.createImageData;n.createImageData=function(e,t){var r=a.call(this,e,t);r.data.set=function(e){for(var t=0,r=this.length;t<r;t++){this[t]=e[t]}};return r};n=null}}})();(function e(){function t(){window.requestAnimationFrame=function(e){return window.setTimeout(e,20)};window.cancelAnimationFrame=function(e){window.clearTimeout(e)}}if(!m){return}if(h){t();return}if("requestAnimationFrame"in window){return}window.requestAnimationFrame=window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame;if(window.requestAnimationFrame){return}t()})();(function e(){if(h||s){PDFJS.maxCanvasPixels=5242880}})();(function e(){if(!m){return}if(d&&window.parent!==window){PDFJS.disableFullscreen=true}})();(function e(){if(!m){return}if("currentScript"in document){return}Object.defineProperty(document,"currentScript",{get:function e(){var t=document.getElementsByTagName("script");return t[t.length-1]},enumerable:true,configurable:true})})();(function e(){if(!m){return}var t=document.createElement("input");try{t.type="number"}catch(e){var r=t.constructor.prototype;var n=Object.getOwnPropertyDescriptor(r,"type");Object.defineProperty(r,"type",{get:function e(){return n.get.call(this)},set:function e(t){n.set.call(this,t==="number"?"text":t)},enumerable:true,configurable:true})}})();(function e(){if(!m){return}if(!document.attachEvent){return}var t=document.constructor.prototype;var r=Object.getOwnPropertyDescriptor(t,"readyState");Object.defineProperty(t,"readyState",{get:function e(){var t=r.get.call(this);return t==="interactive"?"loading":t},set:function e(t){r.set.call(this,t)},enumerable:true,configurable:true})})();(function e(){if(!m){return}if(typeof Element.prototype.remove!=="undefined"){return}Element.prototype.remove=function(){if(this.parentNode){this.parentNode.removeChild(this)}}})();(function e(){if(Number.isNaN){return}Number.isNaN=r(89)})();(function e(){if(Number.isInteger){return}Number.isInteger=r(91)})();(function e(){if(a.Promise){return}a.Promise=r(94)})();(function e(){if(a.WeakMap){return}a.WeakMap=r(104)})();(function e(){var t=false;try{if(typeof URL==="function"&&n(URL.prototype)==="object"&&"origin"in URL.prototype){var r=new URL("b","http://a");r.pathname="c%20d";t=r.href==="http://a/c%20d"}}catch(e){}if(t){return}var i=Object.create(null);i["ftp"]=21;i["file"]=0;i["gopher"]=70;i["http"]=80;i["https"]=443;i["ws"]=80;i["wss"]=443;var s=Object.create(null);s["%2e"]=".";s[".%2e"]="..";s["%2e."]="..";s["%2e%2e"]="..";function o(e){return i[e]!==undefined}function u(){m.call(this);this._isInvalid=true}function l(e){if(e===""){u.call(this)}return e.toLowerCase()}function c(e){var t=e.charCodeAt(0);if(t>32&&t<127&&[34,35,60,62,63,96].indexOf(t)===-1){return e}return encodeURIComponent(e)}function f(e){var t=e.charCodeAt(0);if(t>32&&t<127&&[34,35,60,62,96].indexOf(t)===-1){return e}return encodeURIComponent(e)}var d,h=/[a-zA-Z]/,v=/[a-zA-Z0-9\+\-\.]/;function p(e,t,r){function n(e){y.push(e)}var a=t||"scheme start",p=0,m="",g=false,b=false,y=[];e:while((e[p-1]!==d||p===0)&&!this._isInvalid){var _=e[p];switch(a){case"scheme start":if(_&&h.test(_)){m+=_.toLowerCase();a="scheme"}else if(!t){m="";a="no scheme";continue}else{n("Invalid scheme.");break e}break;case"scheme":if(_&&v.test(_)){m+=_.toLowerCase()}else if(_===":"){this._scheme=m;m="";if(t){break e}if(o(this._scheme)){this._isRelative=true}if(this._scheme==="file"){a="relative"}else if(this._isRelative&&r&&r._scheme===this._scheme){a="relative or authority"}else if(this._isRelative){a="authority first slash"}else{a="scheme data"}}else if(!t){m="";p=0;a="no scheme";continue}else if(_===d){break e}else{n("Code point not allowed in scheme: "+_);break e}break;case"scheme data":if(_==="?"){this._query="?";a="query"}else if(_==="#"){this._fragment="#";a="fragment"}else{if(_!==d&&_!=="\t"&&_!=="\n"&&_!=="\r"){this._schemeData+=c(_)}}break;case"no scheme":if(!r||!o(r._scheme)){n("Missing scheme.");u.call(this)}else{a="relative";continue}break;case"relative or authority":if(_==="/"&&e[p+1]==="/"){a="authority ignore slashes"}else{n("Expected /, got: "+_);a="relative";continue}break;case"relative":this._isRelative=true;if(this._scheme!=="file"){this._scheme=r._scheme}if(_===d){this._host=r._host;this._port=r._port;this._path=r._path.slice();this._query=r._query;this._username=r._username;this._password=r._password;break e}else if(_==="/"||_==="\\"){if(_==="\\"){n("\\ is an invalid code point.")}a="relative slash"}else if(_==="?"){this._host=r._host;this._port=r._port;this._path=r._path.slice();this._query="?";this._username=r._username;this._password=r._password;a="query"}else if(_==="#"){this._host=r._host;this._port=r._port;this._path=r._path.slice();this._query=r._query;this._fragment="#";this._username=r._username;this._password=r._password;a="fragment"}else{var A=e[p+1];var S=e[p+2];if(this._scheme!=="file"||!h.test(_)||A!==":"&&A!=="|"||S!==d&&S!=="/"&&S!=="\\"&&S!=="?"&&S!=="#"){this._host=r._host;this._port=r._port;this._username=r._username;this._password=r._password;this._path=r._path.slice();this._path.pop()}a="relative path";continue}break;case"relative slash":if(_==="/"||_==="\\"){if(_==="\\"){n("\\ is an invalid code point.")}if(this._scheme==="file"){a="file host"}else{a="authority ignore slashes"}}else{if(this._scheme!=="file"){this._host=r._host;this._port=r._port;this._username=r._username;this._password=r._password}a="relative path";continue}break;case"authority first slash":if(_==="/"){a="authority second slash"}else{n("Expected '/', got: "+_);a="authority ignore slashes";continue}break;case"authority second slash":a="authority ignore slashes";if(_!=="/"){n("Expected '/', got: "+_);continue}break;case"authority ignore slashes":if(_!=="/"&&_!=="\\"){a="authority";continue}else{n("Expected authority, got: "+_)}break;case"authority":if(_==="@"){if(g){n("@ already seen.");m+="%40"}g=true;for(var w=0;w<m.length;w++){var P=m[w];if(P==="\t"||P==="\n"||P==="\r"){n("Invalid whitespace in authority.");continue}if(P===":"&&this._password===null){this._password="";continue}var k=c(P);if(this._password!==null){this._password+=k}else{this._username+=k}}m=""}else if(_===d||_==="/"||_==="\\"||_==="?"||_==="#"){p-=m.length;m="";a="host";continue}else{m+=_}break;case"file host":if(_===d||_==="/"||_==="\\"||_==="?"||_==="#"){if(m.length===2&&h.test(m[0])&&(m[1]===":"||m[1]==="|")){a="relative path"}else if(m.length===0){a="relative path start"}else{this._host=l.call(this,m);m="";a="relative path start"}continue}else if(_==="\t"||_==="\n"||_==="\r"){n("Invalid whitespace in file host.")}else{m+=_}break;case"host":case"hostname":if(_===":"&&!b){this._host=l.call(this,m);m="";a="port";if(t==="hostname"){break e}}else if(_===d||_==="/"||_==="\\"||_==="?"||_==="#"){this._host=l.call(this,m);m="";a="relative path start";if(t){break e}continue}else if(_!=="\t"&&_!=="\n"&&_!=="\r"){if(_==="["){b=true}else if(_==="]"){b=false}m+=_}else{n("Invalid code point in host/hostname: "+_)}break;case"port":if(/[0-9]/.test(_)){m+=_}else if(_===d||_==="/"||_==="\\"||_==="?"||_==="#"||t){if(m!==""){var C=parseInt(m,10);if(C!==i[this._scheme]){this._port=C+""}m=""}if(t){break e}a="relative path start";continue}else if(_==="\t"||_==="\n"||_==="\r"){n("Invalid code point in port: "+_)}else{u.call(this)}break;case"relative path start":if(_==="\\"){n("'\\' not allowed in path.")}a="relative path";if(_!=="/"&&_!=="\\"){continue}break;case"relative path":if(_===d||_==="/"||_==="\\"||!t&&(_==="?"||_==="#")){if(_==="\\"){n("\\ not allowed in relative path.")}var R;if(R=s[m.toLowerCase()]){m=R}if(m===".."){this._path.pop();if(_!=="/"&&_!=="\\"){this._path.push("")}}else if(m==="."&&_!=="/"&&_!=="\\"){this._path.push("")}else if(m!=="."){if(this._scheme==="file"&&this._path.length===0&&m.length===2&&h.test(m[0])&&m[1]==="|"){m=m[0]+":"}this._path.push(m)}m="";if(_==="?"){this._query="?";a="query"}else if(_==="#"){this._fragment="#";a="fragment"}}else if(_!=="\t"&&_!=="\n"&&_!=="\r"){m+=c(_)}break;case"query":if(!t&&_==="#"){this._fragment="#";a="fragment"}else if(_!==d&&_!=="\t"&&_!=="\n"&&_!=="\r"){this._query+=f(_)}break;case"fragment":if(_!==d&&_!=="\t"&&_!=="\n"&&_!=="\r"){this._fragment+=_}break}p++}}function m(){this._scheme="";this._schemeData="";this._username="";this._password=null;this._host="";this._port="";this._path=[];this._query="";this._fragment="";this._isInvalid=false;this._isRelative=false}function g(e,t){if(t!==undefined&&!(t instanceof g)){t=new g(String(t))}this._url=e;m.call(this);var r=e.replace(/^[ \t\r\n\f]+|[ \t\r\n\f]+$/g,"");p.call(this,r,null,t)}g.prototype={toString:function e(){return this.href},get href(){if(this._isInvalid){return this._url}var e="";if(this._username!==""||this._password!==null){e=this._username+(this._password!==null?":"+this._password:"")+"@"}return this.protocol+(this._isRelative?"//"+e+this.host:"")+this.pathname+this._query+this._fragment},set href(e){m.call(this);p.call(this,e)},get protocol(){return this._scheme+":"},set protocol(e){if(this._isInvalid){return}p.call(this,e+":","scheme start")},get host(){return this._isInvalid?"":this._port?this._host+":"+this._port:this._host},set host(e){if(this._isInvalid||!this._isRelative){return}p.call(this,e,"host")},get hostname(){return this._host},set hostname(e){if(this._isInvalid||!this._isRelative){return}p.call(this,e,"hostname")},get port(){return this._port},set port(e){if(this._isInvalid||!this._isRelative){return}p.call(this,e,"port")},get pathname(){return this._isInvalid?"":this._isRelative?"/"+this._path.join("/"):this._schemeData},set pathname(e){if(this._isInvalid||!this._isRelative){return}this._path=[];p.call(this,e,"relative path start")},get search(){return this._isInvalid||!this._query||this._query==="?"?"":this._query},set search(e){if(this._isInvalid||!this._isRelative){return}this._query="?";if(e[0]==="?"){e=e.slice(1)}p.call(this,e,"query")},get hash(){return this._isInvalid||!this._fragment||this._fragment==="#"?"":this._fragment},set hash(e){if(this._isInvalid){return}this._fragment="#";if(e[0]==="#"){e=e.slice(1)}p.call(this,e,"fragment")},get origin(){var e;if(this._isInvalid||!this._scheme){return""}switch(this._scheme){case"data":case"file":case"javascript":case"mailto":return"null";case"blob":try{return new g(this._schemeData).origin||"null"}catch(e){}return"null"}e=this.host;if(!e){return""}return this._scheme+"://"+e}};var b=a.URL;if(b){g.createObjectURL=function(e){return b.createObjectURL.apply(b,arguments)};g.revokeObjectURL=function(e){b.revokeObjectURL(e)}}a.URL=g})()}},function(e,t,r){"use strict";r(78);e.exports=r(4).Uint8ClampedArray},function(e,t,r){"use strict";r(79)("Uint8",1,function(e){return function t(r,n,a){return e(this,r,n,a)}},true)},function(e,t,r){"use strict";var n=typeof Symbol==="function"&&typeof Symbol.iterator==="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol==="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};if(r(8)){var a=r(21);var i=r(1);var s=r(9);var o=r(5);var u=r(46);var l=r(80);var c=r(11);var f=r(24);var d=r(27);var h=r(6);var v=r(23);var p=r(17);var m=r(12);var g=r(47);var b=r(29);var y=r(33);var _=r(10);var A=r(30);var S=r(2);var w=r(18);var P=r(53);var k=r(54);var C=r(56);var R=r(48).f;var x=r(57);var T=r(16);var E=r(3);var O=r(39);var L=r(50);var I=r(40);var j=r(58);var F=r(19);var D=r(41);var M=r(60);var N=r(52);var q=r(88);var U=r(13);var W=r(61);var B=U.f;var z=W.f;var G=i.RangeError;var H=i.TypeError;var X=i.Uint8Array;var V="ArrayBuffer";var Y="Shared"+V;var J="BYTES_PER_ELEMENT";var Q="prototype";var K=Array[Q];var Z=l.ArrayBuffer;var $=l.DataView;var ee=O(0);var te=O(2);var re=O(3);var ne=O(4);var ae=O(5);var ie=O(6);var se=L(true);var oe=L(false);var ue=j.values;var le=j.keys;var ce=j.entries;var fe=K.lastIndexOf;var de=K.reduce;var he=K.reduceRight;var ve=K.join;var pe=K.sort;var me=K.slice;var ge=K.toString;var be=K.toLocaleString;var ye=E("iterator");var _e=E("toStringTag");var Ae=T("typed_constructor");var Se=T("def_constructor");var we=u.CONSTR;var Pe=u.TYPED;var ke=u.VIEW;var Ce="Wrong length!";var Re=O(1,function(e,t){return Le(I(e,e[Se]),t)});var xe=s(function(){return new X(new Uint16Array([1]).buffer)[0]===1});var Te=!!X&&!!X[Q].set&&s(function(){new X(1).set({})});var Ee=function e(t,r){var n=p(t);if(n<0||n%r)throw G("Wrong offset!");return n};var Oe=function e(t){if(S(t)&&Pe in t)return t;throw H(t+" is not a typed array!")};var Le=function e(t,r){if(!(S(t)&&Ae in t)){throw H("It is not a typed array constructor!")}return new t(r)};var Ie=function e(t,r){return je(I(t,t[Se]),r)};var je=function e(t,r){var n=0;var a=r.length;var i=Le(t,a);while(a>n){i[n]=r[n++]}return i};var Fe=function e(t,r,n){B(t,r,{get:function e(){return this._d[n]}})};var De=function e(t){var r=w(t);var n=arguments.length;var a=n>1?arguments[1]:undefined;var i=a!==undefined;var s=x(r);var o,u,l,f,d,h;if(s!=undefined&&!P(s)){for(h=s.call(r),l=[],o=0;!(d=h.next()).done;o++){l.push(d.value)}r=l}if(i&&n>2)a=c(a,arguments[2],2);for(o=0,u=m(r.length),f=Le(this,u);u>o;o++){f[o]=i?a(r[o],o):r[o]}return f};var Me=function e(){var t=0;var r=arguments.length;var n=Le(this,r);while(r>t){n[t]=arguments[t++]}return n};var Ne=!!X&&s(function(){be.call(new X(1))});var qe=function e(){return be.apply(Ne?me.call(Oe(this)):Oe(this),arguments)};var Ue={copyWithin:function e(t,r){return q.call(Oe(this),t,r,arguments.length>2?arguments[2]:undefined)},every:function e(t){return ne(Oe(this),t,arguments.length>1?arguments[1]:undefined)},fill:function e(t){return N.apply(Oe(this),arguments)},filter:function e(t){return Ie(this,te(Oe(this),t,arguments.length>1?arguments[1]:undefined))},find:function e(t){return ae(Oe(this),t,arguments.length>1?arguments[1]:undefined)},findIndex:function e(t){return ie(Oe(this),t,arguments.length>1?arguments[1]:undefined)},forEach:function e(t){ee(Oe(this),t,arguments.length>1?arguments[1]:undefined)},indexOf:function e(t){return oe(Oe(this),t,arguments.length>1?arguments[1]:undefined)},includes:function e(t){return se(Oe(this),t,arguments.length>1?arguments[1]:undefined)},join:function e(t){return ve.apply(Oe(this),arguments)},lastIndexOf:function e(t){return fe.apply(Oe(this),arguments)},map:function e(t){return Re(Oe(this),t,arguments.length>1?arguments[1]:undefined)},reduce:function e(t){return de.apply(Oe(this),arguments)},reduceRight:function e(t){return he.apply(Oe(this),arguments)},reverse:function e(){var t=this;var r=Oe(t).length;var n=Math.floor(r/2);var a=0;var i;while(a<n){i=t[a];t[a++]=t[--r];t[r]=i}return t},some:function e(t){return re(Oe(this),t,arguments.length>1?arguments[1]:undefined)},sort:function e(t){return pe.call(Oe(this),t)},subarray:function e(t,r){var n=Oe(this);var a=n.length;var i=b(t,a);return new(I(n,n[Se]))(n.buffer,n.byteOffset+i*n.BYTES_PER_ELEMENT,m((r===undefined?a:b(r,a))-i))}};var We=function e(t,r){return Ie(this,me.call(Oe(this),t,r))};var Be=function e(t){Oe(this);var r=Ee(arguments[1],1);var n=this.length;var a=w(t);var i=m(a.length);var s=0;if(i+r>n)throw G(Ce);while(s<i){this[r+s]=a[s++]}};var ze={entries:function e(){return ce.call(Oe(this))},keys:function e(){return le.call(Oe(this))},values:function e(){return ue.call(Oe(this))}};var Ge=function e(t,r){return S(t)&&t[Pe]&&(typeof r==="undefined"?"undefined":n(r))!="symbol"&&r in t&&String(+r)==String(r)};var He=function e(t,r){return Ge(t,r=y(r,true))?d(2,t[r]):z(t,r)};var Xe=function e(t,r,n){if(Ge(t,r=y(r,true))&&S(n)&&_(n,"value")&&!_(n,"get")&&!_(n,"set")&&!n.configurable&&(!_(n,"writable")||n.writable)&&(!_(n,"enumerable")||n.enumerable)){t[r]=n.value;return t}return B(t,r,n)};if(!we){W.f=He;U.f=Xe}o(o.S+o.F*!we,"Object",{getOwnPropertyDescriptor:He,defineProperty:Xe});if(s(function(){ge.call({})})){ge=be=function e(){return ve.call(this)}}var Ve=v({},Ue);v(Ve,ze);h(Ve,ye,ze.values);v(Ve,{slice:We,set:Be,constructor:function e(){},toString:ge,toLocaleString:qe});Fe(Ve,"buffer","b");Fe(Ve,"byteOffset","o");Fe(Ve,"byteLength","l");Fe(Ve,"length","e");B(Ve,_e,{get:function e(){return this[Pe]}});e.exports=function(e,t,r,n){n=!!n;var l=e+(n?"Clamped":"")+"Array";var c="get"+e;var d="set"+e;var v=i[l];var p=v||{};var b=v&&C(v);var y=!v||!u.ABV;var _={};var w=v&&v[Q];var P=function e(r,n){var a=r._d;return a.v[c](n*t+a.o,xe)};var x=function e(r,a,i){var s=r._d;if(n)i=(i=Math.round(i))<0?0:i>255?255:i&255;s.v[d](a*t+s.o,i,xe)};var T=function e(t,r){B(t,r,{get:function e(){return P(this,r)},set:function e(t){return x(this,r,t)},enumerable:true})};if(y){v=r(function(e,r,n,a){f(e,v,l,"_d");var i=0;var s=0;var o,u,c,d;if(!S(r)){c=g(r);u=c*t;o=new Z(u)}else if(r instanceof Z||(d=A(r))==V||d==Y){o=r;s=Ee(n,t);var p=r.byteLength;if(a===undefined){if(p%t)throw G(Ce);u=p-s;if(u<0)throw G(Ce)}else{u=m(a)*t;if(u+s>p)throw G(Ce)}c=u/t}else if(Pe in r){return je(v,r)}else{return De.call(v,r)}h(e,"_d",{b:o,o:s,l:u,e:c,v:new $(o)});while(i<c){T(e,i++)}});w=v[Q]=k(Ve);h(w,"constructor",v)}else if(!s(function(){v(1)})||!s(function(){new v(-1)})||!D(function(e){new v;new v(null);new v(1.5);new v(e)},true)){v=r(function(e,r,n,a){f(e,v,l);var i;if(!S(r))return new p(g(r));if(r instanceof Z||(i=A(r))==V||i==Y){return a!==undefined?new p(r,Ee(n,t),a):n!==undefined?new p(r,Ee(n,t)):new p(r)}if(Pe in r)return je(v,r);return De.call(v,r)});ee(b!==Function.prototype?R(p).concat(R(b)):R(p),function(e){if(!(e in v))h(v,e,p[e])});v[Q]=w;if(!a)w.constructor=v}var E=w[ye];var O=!!E&&(E.name=="values"||E.name==undefined);var L=ze.values;h(v,Ae,true);h(w,Pe,l);h(w,ke,true);h(w,Se,v);if(n?new v(1)[_e]!=l:!(_e in w)){B(w,_e,{get:function e(){return l}})}_[l]=v;o(o.G+o.W+o.F*(v!=p),_);o(o.S,l,{BYTES_PER_ELEMENT:t});o(o.S+o.F*s(function(){p.of.call(v,1)}),l,{from:De,of:Me});if(!(J in w))h(w,J,t);o(o.P,l,Ue);M(l);o(o.P+o.F*Te,l,{set:Be});o(o.P+o.F*!O,l,ze);if(!a&&w.toString!=ge)w.toString=ge;o(o.P+o.F*s(function(){new v(1).slice()}),l,{slice:We});o(o.P+o.F*(s(function(){return[1,2].toLocaleString()!=new v([1,2]).toLocaleString()})||!s(function(){w.toLocaleString.call([1,2])})),l,{toLocaleString:qe});F[l]=O?E:L;if(!a&&!O)h(w,ye,L)}}else e.exports=function(){}},function(e,t,r){"use strict";var n=r(1);var a=r(8);var i=r(21);var s=r(46);var o=r(6);var u=r(23);var l=r(9);var c=r(24);var f=r(17);var d=r(12);var h=r(47);var v=r(48).f;var p=r(13).f;var m=r(52);var g=r(26);var b="ArrayBuffer";var y="DataView";var _="prototype";var A="Wrong length!";var S="Wrong index!";var w=n[b];var P=n[y];var k=n.Math;var C=n.RangeError;var R=n.Infinity;var x=w;var T=k.abs;var E=k.pow;var O=k.floor;var L=k.log;var I=k.LN2;var j="buffer";var F="byteLength";var D="byteOffset";var M=a?"_b":j;var N=a?"_l":F;var q=a?"_o":D;function U(e,t,r){var n=new Array(r);var a=r*8-t-1;var i=(1<<a)-1;var s=i>>1;var o=t===23?E(2,-24)-E(2,-77):0;var u=0;var l=e<0||e===0&&1/e<0?1:0;var c,f,d;e=T(e);if(e!=e||e===R){f=e!=e?1:0;c=i}else{c=O(L(e)/I);if(e*(d=E(2,-c))<1){c--;d*=2}if(c+s>=1){e+=o/d}else{e+=o*E(2,1-s)}if(e*d>=2){c++;d/=2}if(c+s>=i){f=0;c=i}else if(c+s>=1){f=(e*d-1)*E(2,t);c=c+s}else{f=e*E(2,s-1)*E(2,t);c=0}}for(;t>=8;n[u++]=f&255,f/=256,t-=8){}c=c<<t|f;a+=t;for(;a>0;n[u++]=c&255,c/=256,a-=8){}n[--u]|=l*128;return n}function W(e,t,r){var n=r*8-t-1;var a=(1<<n)-1;var i=a>>1;var s=n-7;var o=r-1;var u=e[o--];var l=u&127;var c;u>>=7;for(;s>0;l=l*256+e[o],o--,s-=8){}c=l&(1<<-s)-1;l>>=-s;s+=t;for(;s>0;c=c*256+e[o],o--,s-=8){}if(l===0){l=1-i}else if(l===a){return c?NaN:u?-R:R}else{c=c+E(2,t);l=l-i}return(u?-1:1)*c*E(2,l-t)}function B(e){return e[3]<<24|e[2]<<16|e[1]<<8|e[0]}function z(e){return[e&255]}function G(e){return[e&255,e>>8&255]}function H(e){return[e&255,e>>8&255,e>>16&255,e>>24&255]}function X(e){return U(e,52,8)}function V(e){return U(e,23,4)}function Y(e,t,r){p(e[_],t,{get:function e(){return this[r]}})}function J(e,t,r,n){var a=+r;var i=h(a);if(i+t>e[N])throw C(S);var s=e[M]._b;var o=i+e[q];var u=s.slice(o,o+t);return n?u:u.reverse()}function Q(e,t,r,n,a,i){var s=+r;var o=h(s);if(o+t>e[N])throw C(S);var u=e[M]._b;var l=o+e[q];var c=n(+a);for(var f=0;f<t;f++){u[l+f]=c[i?f:t-f-1]}}if(!s.ABV){w=function e(t){c(this,w,b);var r=h(t);this._b=m.call(new Array(r),0);this[N]=r};P=function e(t,r,n){c(this,P,y);c(t,w,y);var a=t[N];var i=f(r);if(i<0||i>a)throw C("Wrong offset!");n=n===undefined?a-i:d(n);if(i+n>a)throw C(A);this[M]=t;this[q]=i;this[N]=n};if(a){Y(w,F,"_l");Y(P,j,"_b");Y(P,F,"_l");Y(P,D,"_o")}u(P[_],{getInt8:function e(t){return J(this,1,t)[0]<<24>>24},getUint8:function e(t){return J(this,1,t)[0]},getInt16:function e(t){var r=J(this,2,t,arguments[1]);return(r[1]<<8|r[0])<<16>>16},getUint16:function e(t){var r=J(this,2,t,arguments[1]);return r[1]<<8|r[0]},getInt32:function e(t){return B(J(this,4,t,arguments[1]))},getUint32:function e(t){return B(J(this,4,t,arguments[1]))>>>0},getFloat32:function e(t){return W(J(this,4,t,arguments[1]),23,4)},getFloat64:function e(t){return W(J(this,8,t,arguments[1]),52,8)},setInt8:function e(t,r){Q(this,1,t,z,r)},setUint8:function e(t,r){Q(this,1,t,z,r)},setInt16:function e(t,r){Q(this,2,t,G,r,arguments[2])},setUint16:function e(t,r){Q(this,2,t,G,r,arguments[2])},setInt32:function e(t,r){Q(this,4,t,H,r,arguments[2])},setUint32:function e(t,r){Q(this,4,t,H,r,arguments[2])},setFloat32:function e(t,r){Q(this,4,t,V,r,arguments[2])},setFloat64:function e(t,r){Q(this,8,t,X,r,arguments[2])}})}else{if(!l(function(){w(1)})||!l(function(){new w(-1)})||l(function(){new w;new w(1.5);new w(NaN);return w.name!=b})){w=function e(t){c(this,w);return new x(h(t))};var K=w[_]=x[_];for(var Z=v(x),$=0,ee;Z.length>$;){if(!((ee=Z[$++])in w))o(w,ee,x[ee])}if(!i)K.constructor=w}var te=new P(new w(2));var re=P[_].setInt8;te.setInt8(0,2147483648);te.setInt8(1,2147483649);if(te.getInt8(0)||!te.getInt8(1))u(P[_],{setInt8:function e(t,r){re.call(this,t,r<<24>>24)},setUint8:function e(t,r){re.call(this,t,r<<24>>24)}},true)}g(w,b);g(P,y);o(P[_],s.VIEW,true);t[b]=w;t[y]=P},function(e,t,r){"use strict";var n=r(13);var a=r(7);var i=r(38);e.exports=r(8)?Object.defineProperties:function e(t,r){a(t);var s=i(r);var o=s.length;var u=0;var l;while(o>u){n.f(t,l=s[u++],r[l])}return t}},function(e,t,r){"use strict";var n=r(83);e.exports=function(e,t){return new(n(e))(t)}},function(e,t,r){"use strict";var n=r(2);var a=r(84);var i=r(3)("species");e.exports=function(e){var t;if(a(e)){t=e.constructor;if(typeof t=="function"&&(t===Array||a(t.prototype)))t=undefined;if(n(t)){t=t[i];if(t===null)t=undefined}}return t===undefined?Array:t}},function(e,t,r){"use strict";var n=r(25);e.exports=Array.isArray||function e(t){return n(t)=="Array"}},function(e,t,r){"use strict";var n=r(3)("unscopables");var a=Array.prototype;if(a[n]==undefined)r(6)(a,n,{});e.exports=function(e){a[n][e]=true}},function(e,t,r){"use strict";e.exports=function(e,t){return{value:t,done:!!e}}},function(e,t,r){"use strict";var n=r(54);var a=r(27);var i=r(26);var s={};r(6)(s,r(3)("iterator"),function(){return this});e.exports=function(e,t,r){e.prototype=n(s,{next:a(1,r)});i(e,t+" Iterator")}},function(e,t,r){"use strict";var n=r(18);var a=r(29);var i=r(12);e.exports=[].copyWithin||function e(t,r){var s=n(this);var o=i(s.length);var u=a(t,o);var l=a(r,o);var c=arguments.length>2?arguments[2]:undefined;var f=Math.min((c===undefined?o:a(c,o))-l,o-u);var d=1;if(l<u&&u<l+f){d=-1;l+=f-1;u+=f-1}while(f-- >0){if(l in s)s[u]=s[l];else delete s[u];u+=d;l+=d}return s}},function(e,t,r){"use strict";r(90);e.exports=r(4).Number.isNaN},function(e,t,r){"use strict";var n=r(5);n(n.S,"Number",{isNaN:function e(t){return t!=t}})},function(e,t,r){"use strict";r(92);e.exports=r(4).Number.isInteger},function(e,t,r){"use strict";var n=r(5);n(n.S,"Number",{isInteger:r(93)})},function(e,t,r){"use strict";var n=r(2);var a=Math.floor;e.exports=function e(t){return!n(t)&&isFinite(t)&&a(t)===t}},function(e,t,r){"use strict";r(63);r(95);r(64);r(97);r(102);r(103);e.exports=r(4).Promise},function(e,t,r){"use strict";var n=r(96)(true);r(59)(String,"String",function(e){this._t=String(e);this._i=0},function(){var e=this._t;var t=this._i;var r;if(t>=e.length)return{value:undefined,done:true};r=n(e,t);this._i+=r.length;return{value:r,done:false}})},function(e,t,r){"use strict";var n=r(17);var a=r(35);e.exports=function(e){return function(t,r){var i=String(a(t));var s=n(r);var o=i.length;var u,l;if(s<0||s>=o)return e?"":undefined;u=i.charCodeAt(s);return u<55296||u>56319||s+1===o||(l=i.charCodeAt(s+1))<56320||l>57343?e?i.charAt(s):u:e?i.slice(s,s+2):(u-55296<<10)+(l-56320)+65536}}},function(e,t,r){"use strict";var n=r(21);var a=r(1);var i=r(11);var s=r(30);var o=r(5);var u=r(2);var l=r(22);var c=r(24);var f=r(31);var d=r(40);var h=r(65).set;var v=r(100)();var p=r(42);var m=r(66);var g=r(101);var b=r(67);var y="Promise";var _=a.TypeError;var A=a.process;var S=A&&A.versions;var w=S&&S.v8||"";var P=a[y];var k=s(A)=="process";var C=function e(){};var R,x,T,E;var O=x=p.f;var L=!!function(){try{var e=P.resolve(1);var t=(e.constructor={})[r(3)("species")]=function(e){e(C,C)};return(k||typeof PromiseRejectionEvent=="function")&&e.then(C)instanceof t&&w.indexOf("6.6")!==0&&g.indexOf("Chrome/66")===-1}catch(e){}}();var I=function e(t){var r;return u(t)&&typeof(r=t.then)=="function"?r:false};var j=function e(t,r){if(t._n)return;t._n=true;var n=t._c;v(function(){var e=t._v;var a=t._s==1;var i=0;var s=function r(n){var i=a?n.ok:n.fail;var s=n.resolve;var o=n.reject;var u=n.domain;var l,c,f;try{if(i){if(!a){if(t._h==2)M(t);t._h=1}if(i===true)l=e;else{if(u)u.enter();l=i(e);if(u){u.exit();f=true}}if(l===n.promise){o(_("Promise-chain cycle"))}else if(c=I(l)){c.call(l,s,o)}else s(l)}else o(e)}catch(e){if(u&&!f)u.exit();o(e)}};while(n.length>i){s(n[i++])}t._c=[];t._n=false;if(r&&!t._h)F(t)})};var F=function e(t){h.call(a,function(){var e=t._v;var r=D(t);var n,i,s;if(r){n=m(function(){if(k){A.emit("unhandledRejection",e,t)}else if(i=a.onunhandledrejection){i({promise:t,reason:e})}else if((s=a.console)&&s.error){s.error("Unhandled promise rejection",e)}});t._h=k||D(t)?2:1}t._a=undefined;if(r&&n.e)throw n.v})};var D=function e(t){return t._h!==1&&(t._a||t._c).length===0};var M=function e(t){h.call(a,function(){var e;if(k){A.emit("rejectionHandled",t)}else if(e=a.onrejectionhandled){e({promise:t,reason:t._v})}})};var N=function e(t){var r=this;if(r._d)return;r._d=true;r=r._w||r;r._v=t;r._s=2;if(!r._a)r._a=r._c.slice();j(r,true)};var q=function e(t){var r=this;var n;if(r._d)return;r._d=true;r=r._w||r;try{if(r===t)throw _("Promise can't be resolved itself");if(n=I(t)){v(function(){var a={_w:r,_d:false};try{n.call(t,i(e,a,1),i(N,a,1))}catch(e){N.call(a,e)}})}else{r._v=t;r._s=1;j(r,false)}}catch(e){N.call({_w:r,_d:false},e)}};if(!L){P=function e(t){c(this,P,y,"_h");l(t);R.call(this);try{t(i(q,this,1),i(N,this,1))}catch(e){N.call(this,e)}};R=function e(t){this._c=[];this._a=undefined;this._s=0;this._d=false;this._v=undefined;this._h=0;this._n=false};R.prototype=r(23)(P.prototype,{then:function e(t,r){var n=O(d(this,P));n.ok=typeof t=="function"?t:true;n.fail=typeof r=="function"&&r;n.domain=k?A.domain:undefined;this._c.push(n);if(this._a)this._a.push(n);if(this._s)j(this,false);return n.promise},catch:function e(t){return this.then(undefined,t)}});T=function e(){var t=new R;this.promise=t;this.resolve=i(q,t,1);this.reject=i(N,t,1)};p.f=O=function e(t){return t===P||t===E?new T(t):x(t)}}o(o.G+o.W+o.F*!L,{Promise:P});r(26)(P,y);r(60)(y);E=r(4)[y];o(o.S+o.F*!L,y,{reject:function e(t){var r=O(this);var n=r.reject;n(t);return r.promise}});o(o.S+o.F*(n||!L),y,{resolve:function e(t){return b(n&&this===E?P:this,t)}});o(o.S+o.F*!(L&&r(41)(function(e){P.all(e)["catch"](C)})),y,{all:function e(t){var r=this;var n=O(r);var a=n.resolve;var i=n.reject;var s=m(function(){var e=[];var n=0;var s=1;f(t,false,function(t){var o=n++;var u=false;e.push(undefined);s++;r.resolve(t).then(function(t){if(u)return;u=true;e[o]=t;--s||a(e)},i)});--s||a(e)});if(s.e)i(s.v);return n.promise},race:function e(t){var r=this;var n=O(r);var a=n.reject;var i=m(function(){f(t,false,function(e){r.resolve(e).then(n.resolve,a)})});if(i.e)a(i.v);return n.promise}})},function(e,t,r){"use strict";var n=r(7);e.exports=function(e,t,r,a){try{return a?t(n(r)[0],r[1]):t(r)}catch(t){var i=e["return"];if(i!==undefined)n(i.call(e));throw t}}},function(e,t,r){"use strict";e.exports=function(e,t,r){var n=r===undefined;switch(t.length){case 0:return n?e():e.call(r);case 1:return n?e(t[0]):e.call(r,t[0]);case 2:return n?e(t[0],t[1]):e.call(r,t[0],t[1]);case 3:return n?e(t[0],t[1],t[2]):e.call(r,t[0],t[1],t[2]);case 4:return n?e(t[0],t[1],t[2],t[3]):e.call(r,t[0],t[1],t[2],t[3])}return e.apply(r,t)}},function(e,t,r){"use strict";var n=r(1);var a=r(65).set;var i=n.MutationObserver||n.WebKitMutationObserver;var s=n.process;var o=n.Promise;var u=r(25)(s)=="process";e.exports=function(){var e,t,r;var l=function n(){var a,i;if(u&&(a=s.domain))a.exit();while(e){i=e.fn;e=e.next;try{i()}catch(n){if(e)r();else t=undefined;throw n}}t=undefined;if(a)a.enter()};if(u){r=function e(){s.nextTick(l)}}else if(i&&!(n.navigator&&n.navigator.standalone)){var c=true;var f=document.createTextNode("");new i(l).observe(f,{characterData:true});r=function e(){f.data=c=!c}}else if(o&&o.resolve){var d=o.resolve(undefined);r=function e(){d.then(l)}}else{r=function e(){a.call(n,l)}}return function(n){var a={fn:n,next:undefined};if(t)t.next=a;if(!e){e=a;r()}t=a}}},function(e,t,r){"use strict";var n=r(1);var a=n.navigator;e.exports=a&&a.userAgent||""},function(e,t,r){"use strict";var n=r(5);var a=r(4);var i=r(1);var s=r(40);var o=r(67);n(n.P+n.R,"Promise",{finally:function e(t){var r=s(this,a.Promise||i.Promise);var n=typeof t=="function";return this.then(n?function(e){return o(r,t()).then(function(){return e})}:t,n?function(e){return o(r,t()).then(function(){throw e})}:t)}})},function(e,t,r){"use strict";var n=r(5);var a=r(42);var i=r(66);n(n.S,"Promise",{try:function e(t){var r=a.f(this);var n=i(t);(n.e?r.reject:r.resolve)(n.v);return r.promise}})},function(e,t,r){"use strict";r(63);r(64);r(105);r(112);r(114);e.exports=r(4).WeakMap},function(e,t,r){"use strict";var n=r(39)(0);var a=r(14);var i=r(43);var s=r(106);var o=r(108);var u=r(2);var l=r(9);var c=r(68);var f="WeakMap";var d=i.getWeak;var h=Object.isExtensible;var v=o.ufstore;var p={};var m;var g=function e(t){return function e(){return t(this,arguments.length>0?arguments[0]:undefined)}};var b={get:function e(t){if(u(t)){var r=d(t);if(r===true)return v(c(this,f)).get(t);return r?r[this._i]:undefined}},set:function e(t,r){return o.def(c(this,f),t,r)}};var y=e.exports=r(109)(f,g,b,o,true,true);if(l(function(){return(new y).set((Object.freeze||Object)(p),7).get(p)!=7})){m=o.getConstructor(g,f);s(m.prototype,b);i.NEED=true;n(["delete","has","get","set"],function(e){var t=y.prototype;var r=t[e];a(t,e,function(t,n){if(u(t)&&!h(t)){if(!this._f)this._f=new m;var a=this._f[e](t,n);return e=="set"?this:a}return r.call(this,t,n)})})}},function(e,t,r){"use strict";var n=r(38);var a=r(107);var i=r(62);var s=r(18);var o=r(34);var u=Object.assign;e.exports=!u||r(9)(function(){var e={};var t={};var r=Symbol();var n="abcdefghijklmnopqrst";e[r]=7;n.split("").forEach(function(e){t[e]=e});return u({},e)[r]!=7||Object.keys(u({},t)).join("")!=n})?function e(t,r){var u=s(t);var l=arguments.length;var c=1;var f=a.f;var d=i.f;while(l>c){var h=o(arguments[c++]);var v=f?n(h).concat(f(h)):n(h);var p=v.length;var m=0;var g;while(p>m){if(d.call(h,g=v[m++]))u[g]=h[g]}}return u}:u},function(e,t,r){"use strict";t.f=Object.getOwnPropertySymbols},function(e,t,r){"use strict";var n=r(23);var a=r(43).getWeak;var i=r(7);var s=r(2);var o=r(24);var u=r(31);var l=r(39);var c=r(10);var f=r(68);var d=l(5);var h=l(6);var v=0;var p=function e(t){return t._l||(t._l=new m)};var m=function e(){this.a=[]};var g=function e(t,r){return d(t.a,function(e){return e[0]===r})};m.prototype={get:function e(t){var r=g(this,t);if(r)return r[1]},has:function e(t){return!!g(this,t)},set:function e(t,r){var n=g(this,t);if(n)n[1]=r;else this.a.push([t,r])},delete:function e(t){var r=h(this.a,function(e){return e[0]===t});if(~r)this.a.splice(r,1);return!!~r}};e.exports={getConstructor:function e(t,r,i,l){var d=t(function(e,t){o(e,d,r,"_i");e._t=r;e._i=v++;e._l=undefined;if(t!=undefined)u(t,i,e[l],e)});n(d.prototype,{delete:function e(t){if(!s(t))return false;var n=a(t);if(n===true)return p(f(this,r))["delete"](t);return n&&c(n,this._i)&&delete n[this._i]},has:function e(t){if(!s(t))return false;var n=a(t);if(n===true)return p(f(this,r)).has(t);return n&&c(n,this._i)}});return d},def:function e(t,r,n){var s=a(i(r),true);if(s===true)p(t).set(r,n);else s[t._i]=n;return t},ufstore:p}},function(e,t,r){"use strict";var n=r(1);var a=r(5);var i=r(14);var s=r(23);var o=r(43);var u=r(31);var l=r(24);var c=r(2);var f=r(9);var d=r(41);var h=r(26);var v=r(110);e.exports=function(e,t,r,p,m,g){var b=n[e];var y=b;var _=m?"set":"add";var A=y&&y.prototype;var S={};var w=function e(t){var r=A[t];i(A,t,t=="delete"?function(e){return g&&!c(e)?false:r.call(this,e===0?0:e)}:t=="has"?function e(t){return g&&!c(t)?false:r.call(this,t===0?0:t)}:t=="get"?function e(t){return g&&!c(t)?undefined:r.call(this,t===0?0:t)}:t=="add"?function e(t){r.call(this,t===0?0:t);return this}:function e(t,n){r.call(this,t===0?0:t,n);return this})};if(typeof y!="function"||!(g||A.forEach&&!f(function(){(new y).entries().next()}))){y=p.getConstructor(t,e,m,_);s(y.prototype,r);o.NEED=true}else{var P=new y;var k=P[_](g?{}:-0,1)!=P;var C=f(function(){P.has(1)});var R=d(function(e){new y(e)});var x=!g&&f(function(){var e=new y;var t=5;while(t--){e[_](t,t)}return!e.has(-0)});if(!R){y=t(function(t,r){l(t,y,e);var n=v(new b,t,y);if(r!=undefined)u(r,m,n[_],n);return n});y.prototype=A;A.constructor=y}if(C||x){w("delete");w("has");m&&w("get")}if(x||k)w(_);if(g&&A.clear)delete A.clear}h(y,e);S[e]=y;a(a.G+a.W+a.F*(y!=b),S);if(!g)p.setStrong(y,e,m);return y}},function(e,t,r){"use strict";var n=r(2);var a=r(111).set;e.exports=function(e,t,r){var i=t.constructor;var s;if(i!==r&&typeof i=="function"&&(s=i.prototype)!==r.prototype&&n(s)&&a){a(e,s)}return e}},function(e,t,r){"use strict";var n=r(2);var a=r(7);var i=function e(t,r){a(t);if(!n(r)&&r!==null)throw TypeError(r+": can't set as prototype!")};e.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(e,t,n){try{n=r(11)(Function.call,r(61).f(Object.prototype,"__proto__").set,2);n(e,[]);t=!(e instanceof Array)}catch(e){t=true}return function e(r,a){i(r,a);if(t)r.__proto__=a;else n(r,a);return r}}({},false):undefined),check:i}},function(e,t,r){"use strict";r(113)("WeakMap")},function(e,t,r){"use strict";var n=r(5);e.exports=function(e){n(n.S,e,{of:function e(){var t=arguments.length;var r=new Array(t);while(t--){r[t]=arguments[t]}return new this(r)}})}},function(e,t,r){"use strict";r(115)("WeakMap")},function(e,t,r){"use strict";var n=r(5);var a=r(22);var i=r(11);var s=r(31);e.exports=function(e){n(n.S,e,{from:function e(t){var r=arguments[1];var n,o,u,l;a(this);n=r!==undefined;if(n)a(r);if(t==undefined)return new this;o=[];if(n){u=0;l=i(r,arguments[2],2);s(t,false,function(e){o.push(l(e,u++))})}else{s(t,false,o.push,o)}return new this(o)}})}},function(e,t,r){"use strict";var n=false;if(typeof ReadableStream!=="undefined"){try{new ReadableStream({start:function e(t){t.close()}});n=true}catch(e){}}if(n){t.ReadableStream=ReadableStream}else{t.ReadableStream=r(117).ReadableStream}},function(e,t,r){"use strict";var n=typeof Symbol==="function"&&typeof Symbol.iterator==="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol==="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};(function(e,t){for(var r in t){e[r]=t[r]}})(t,function(e){var t={};function r(n){if(t[n])return t[n].exports;var a=t[n]={i:n,l:false,exports:{}};e[n].call(a.exports,a,a.exports,r);a.l=true;return a.exports}r.m=e;r.c=t;r.i=function(e){return e};r.d=function(e,t,n){if(!r.o(e,t)){Object.defineProperty(e,t,{configurable:false,enumerable:true,get:n})}};r.n=function(e){var t=e&&e.__esModule?function t(){return e["default"]}:function t(){return e};r.d(t,"a",t);return t};r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)};r.p="";return r(r.s=7)}([function(e,t,r){"use strict";var a=typeof Symbol==="function"&&n(Symbol.iterator)==="symbol"?function(e){return typeof e==="undefined"?"undefined":n(e)}:function(e){return e&&typeof Symbol==="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e==="undefined"?"undefined":n(e)};var i=r(1),s=i.assert;function o(e){return typeof e==="string"||(typeof e==="undefined"?"undefined":a(e))==="symbol"}t.typeIsObject=function(e){return(typeof e==="undefined"?"undefined":a(e))==="object"&&e!==null||typeof e==="function"};t.createDataProperty=function(e,r,n){s(t.typeIsObject(e));Object.defineProperty(e,r,{value:n,writable:true,enumerable:true,configurable:true})};t.createArrayFromList=function(e){return e.slice()};t.ArrayBufferCopy=function(e,t,r,n,a){new Uint8Array(e).set(new Uint8Array(r,n,a),t)};t.CreateIterResultObject=function(e,t){s(typeof t==="boolean");var r={};Object.defineProperty(r,"value",{value:e,enumerable:true,writable:true,configurable:true});Object.defineProperty(r,"done",{value:t,enumerable:true,writable:true,configurable:true});return r};t.IsFiniteNonNegativeNumber=function(e){if(Number.isNaN(e)){return false}if(e===Infinity){return false}if(e<0){return false}return true};function u(e,t,r){if(typeof e!=="function"){throw new TypeError("Argument is not a function")}return Function.prototype.apply.call(e,t,r)}t.InvokeOrNoop=function(e,t,r){s(e!==undefined);s(o(t));s(Array.isArray(r));var n=e[t];if(n===undefined){return undefined}return u(n,e,r)};t.PromiseInvokeOrNoop=function(e,r,n){s(e!==undefined);s(o(r));s(Array.isArray(n));try{return Promise.resolve(t.InvokeOrNoop(e,r,n))}catch(e){return Promise.reject(e)}};t.PromiseInvokeOrPerformFallback=function(e,t,r,n,a){s(e!==undefined);s(o(t));s(Array.isArray(r));s(Array.isArray(a));var i=void 0;try{i=e[t]}catch(e){return Promise.reject(e)}if(i===undefined){return n.apply(null,a)}try{return Promise.resolve(u(i,e,r))}catch(e){return Promise.reject(e)}};t.TransferArrayBuffer=function(e){return e.slice()};t.ValidateAndNormalizeHighWaterMark=function(e){e=Number(e);if(Number.isNaN(e)||e<0){throw new RangeError("highWaterMark property of a queuing strategy must be non-negative and non-NaN")}return e};t.ValidateAndNormalizeQueuingStrategy=function(e,r){if(e!==undefined&&typeof e!=="function"){throw new TypeError("size property of a queuing strategy must be a function")}r=t.ValidateAndNormalizeHighWaterMark(r);return{size:e,highWaterMark:r}}},function(e,t,r){"use strict";function n(e){if(e&&e.constructor===a){setTimeout(function(){throw e},0)}}function a(e){this.name="AssertionError";this.message=e||"";this.stack=(new Error).stack}a.prototype=Object.create(Error.prototype);a.prototype.constructor=a;function i(e,t){if(!e){throw new a(t)}}e.exports={rethrowAssertionErrorRejection:n,AssertionError:a,assert:i}},function(e,t,r){"use strict";var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(t,r,n){if(r)e(t.prototype,r);if(n)e(t,n);return t}}();function a(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var i=r(0),s=i.InvokeOrNoop,o=i.PromiseInvokeOrNoop,u=i.ValidateAndNormalizeQueuingStrategy,l=i.typeIsObject;var c=r(1),f=c.assert,d=c.rethrowAssertionErrorRejection;var h=r(3),v=h.DequeueValue,p=h.EnqueueValueWithSize,m=h.PeekQueueValue,g=h.ResetQueue;var b=function(){function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{},n=r.size,i=r.highWaterMark,s=i===undefined?1:i;a(this,e);this._state="writable";this._storedError=undefined;this._writer=undefined;this._writableStreamController=undefined;this._writeRequests=[];this._inFlightWriteRequest=undefined;this._closeRequest=undefined;this._inFlightCloseRequest=undefined;this._pendingAbortRequest=undefined;this._backpressure=false;var o=t.type;if(o!==undefined){throw new RangeError("Invalid type is specified")}this._writableStreamController=new V(this,t,n,s);this._writableStreamController.__startSteps()}n(e,[{key:"abort",value:function e(t){if(_(this)===false){return Promise.reject(ie("abort"))}if(A(this)===true){return Promise.reject(new TypeError("Cannot abort a stream that already has a writer"))}return S(this,t)}},{key:"getWriter",value:function e(){if(_(this)===false){throw ie("getWriter")}return y(this)}},{key:"locked",get:function e(){if(_(this)===false){throw ie("locked")}return A(this)}}]);return e}();e.exports={AcquireWritableStreamDefaultWriter:y,IsWritableStream:_,IsWritableStreamLocked:A,WritableStream:b,WritableStreamAbort:S,WritableStreamDefaultControllerError:ae,WritableStreamDefaultWriterCloseWithErrorPropagation:W,WritableStreamDefaultWriterRelease:H,WritableStreamDefaultWriterWrite:X,WritableStreamCloseQueuedOrInFlight:O};function y(e){return new M(e)}function _(e){if(!l(e)){return false}if(!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")){return false}return true}function A(e){f(_(e)===true,"IsWritableStreamLocked should only be used on known writable streams");if(e._writer===undefined){return false}return true}function S(e,t){var r=e._state;if(r==="closed"){return Promise.resolve(undefined)}if(r==="errored"){return Promise.reject(e._storedError)}var n=new TypeError("Requested to abort");if(e._pendingAbortRequest!==undefined){return Promise.reject(n)}f(r==="writable"||r==="erroring","state must be writable or erroring");var a=false;if(r==="erroring"){a=true;t=undefined}var i=new Promise(function(r,n){e._pendingAbortRequest={_resolve:r,_reject:n,_reason:t,_wasAlreadyErroring:a}});if(a===false){k(e,n)}return i}function w(e){f(A(e)===true);f(e._state==="writable");var t=new Promise(function(t,r){var n={_resolve:t,_reject:r};e._writeRequests.push(n)});return t}function P(e,t){var r=e._state;if(r==="writable"){k(e,t);return}f(r==="erroring");C(e)}function k(e,t){f(e._storedError===undefined,"stream._storedError === undefined");f(e._state==="writable","state must be writable");var r=e._writableStreamController;f(r!==undefined,"controller must not be undefined");e._state="erroring";e._storedError=t;var n=e._writer;if(n!==undefined){z(n,t)}if(L(e)===false&&r._started===true){C(e)}}function C(e){f(e._state==="erroring","stream._state === erroring");f(L(e)===false,"WritableStreamHasOperationMarkedInFlight(stream) === false");e._state="errored";e._writableStreamController.__errorSteps();var t=e._storedError;for(var r=0;r<e._writeRequests.length;r++){var n=e._writeRequests[r];n._reject(t)}e._writeRequests=[];if(e._pendingAbortRequest===undefined){F(e);return}var a=e._pendingAbortRequest;e._pendingAbortRequest=undefined;if(a._wasAlreadyErroring===true){a._reject(t);F(e);return}var i=e._writableStreamController.__abortSteps(a._reason);i.then(function(){a._resolve();F(e)},function(t){a._reject(t);F(e)})}function R(e){f(e._inFlightWriteRequest!==undefined);e._inFlightWriteRequest._resolve(undefined);e._inFlightWriteRequest=undefined}function x(e,t){f(e._inFlightWriteRequest!==undefined);e._inFlightWriteRequest._reject(t);e._inFlightWriteRequest=undefined;f(e._state==="writable"||e._state==="erroring");P(e,t)}function T(e){f(e._inFlightCloseRequest!==undefined);e._inFlightCloseRequest._resolve(undefined);e._inFlightCloseRequest=undefined;var t=e._state;f(t==="writable"||t==="erroring");if(t==="erroring"){e._storedError=undefined;if(e._pendingAbortRequest!==undefined){e._pendingAbortRequest._resolve();e._pendingAbortRequest=undefined}}e._state="closed";var r=e._writer;if(r!==undefined){he(r)}f(e._pendingAbortRequest===undefined,"stream._pendingAbortRequest === undefined");f(e._storedError===undefined,"stream._storedError === undefined")}function E(e,t){f(e._inFlightCloseRequest!==undefined);e._inFlightCloseRequest._reject(t);e._inFlightCloseRequest=undefined;f(e._state==="writable"||e._state==="erroring");if(e._pendingAbortRequest!==undefined){e._pendingAbortRequest._reject(t);e._pendingAbortRequest=undefined}P(e,t)}function O(e){if(e._closeRequest===undefined&&e._inFlightCloseRequest===undefined){return false}return true}function L(e){if(e._inFlightWriteRequest===undefined&&e._inFlightCloseRequest===undefined){return false}return true}function I(e){f(e._inFlightCloseRequest===undefined);f(e._closeRequest!==undefined);e._inFlightCloseRequest=e._closeRequest;e._closeRequest=undefined}function j(e){f(e._inFlightWriteRequest===undefined,"there must be no pending write request");f(e._writeRequests.length!==0,"writeRequests must not be empty");e._inFlightWriteRequest=e._writeRequests.shift()}function F(e){f(e._state==="errored",'_stream_.[[state]] is `"errored"`');if(e._closeRequest!==undefined){f(e._inFlightCloseRequest===undefined);e._closeRequest._reject(e._storedError);e._closeRequest=undefined}var t=e._writer;if(t!==undefined){fe(t,e._storedError);t._closedPromise.catch(function(){})}}function D(e,t){f(e._state==="writable");f(O(e)===false);var r=e._writer;if(r!==undefined&&t!==e._backpressure){if(t===true){be(r)}else{f(t===false);_e(r)}}e._backpressure=t}var M=function(){function e(t){a(this,e);if(_(t)===false){throw new TypeError("WritableStreamDefaultWriter can only be constructed with a WritableStream instance")}if(A(t)===true){throw new TypeError("This stream has already been locked for exclusive writing by another writer")}this._ownerWritableStream=t;t._writer=this;var r=t._state;if(r==="writable"){if(O(t)===false&&t._backpressure===true){ve(this)}else{me(this)}ue(this)}else if(r==="erroring"){pe(this,t._storedError);this._readyPromise.catch(function(){});ue(this)}else if(r==="closed"){me(this);ce(this)}else{f(r==="errored","state must be errored");var n=t._storedError;pe(this,n);this._readyPromise.catch(function(){});le(this,n);this._closedPromise.catch(function(){})}}n(e,[{key:"abort",value:function e(t){if(N(this)===false){return Promise.reject(se("abort"))}if(this._ownerWritableStream===undefined){return Promise.reject(oe("abort"))}return q(this,t)}},{key:"close",value:function e(){if(N(this)===false){return Promise.reject(se("close"))}var t=this._ownerWritableStream;if(t===undefined){return Promise.reject(oe("close"))}if(O(t)===true){return Promise.reject(new TypeError("cannot close an already-closing stream"))}return U(this)}},{key:"releaseLock",value:function e(){if(N(this)===false){throw se("releaseLock")}var t=this._ownerWritableStream;if(t===undefined){return}f(t._writer!==undefined);H(this)}},{key:"write",value:function e(t){if(N(this)===false){return Promise.reject(se("write"))}if(this._ownerWritableStream===undefined){return Promise.reject(oe("write to"))}return X(this,t)}},{key:"closed",get:function e(){if(N(this)===false){return Promise.reject(se("closed"))}return this._closedPromise}},{key:"desiredSize",get:function e(){if(N(this)===false){throw se("desiredSize")}if(this._ownerWritableStream===undefined){throw oe("desiredSize")}return G(this)}},{key:"ready",get:function e(){if(N(this)===false){return Promise.reject(se("ready"))}return this._readyPromise}}]);return e}();function N(e){if(!l(e)){return false}if(!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")){return false}return true}function q(e,t){var r=e._ownerWritableStream;f(r!==undefined);return S(r,t)}function U(e){var t=e._ownerWritableStream;f(t!==undefined);var r=t._state;if(r==="closed"||r==="errored"){return Promise.reject(new TypeError("The stream (in "+r+" state) is not in the writable state and cannot be closed"))}f(r==="writable"||r==="erroring");f(O(t)===false);var n=new Promise(function(e,r){var n={_resolve:e,_reject:r};t._closeRequest=n});if(t._backpressure===true&&r==="writable"){_e(e)}Y(t._writableStreamController);return n}function W(e){var t=e._ownerWritableStream;f(t!==undefined);var r=t._state;if(O(t)===true||r==="closed"){return Promise.resolve()}if(r==="errored"){return Promise.reject(t._storedError)}f(r==="writable"||r==="erroring");return U(e)}function B(e,t){if(e._closedPromiseState==="pending"){fe(e,t)}else{de(e,t)}e._closedPromise.catch(function(){})}function z(e,t){if(e._readyPromiseState==="pending"){ge(e,t)}else{ye(e,t)}e._readyPromise.catch(function(){})}function G(e){var t=e._ownerWritableStream;var r=t._state;if(r==="errored"||r==="erroring"){return null}if(r==="closed"){return 0}return Q(t._writableStreamController)}function H(e){var t=e._ownerWritableStream;f(t!==undefined);f(t._writer===e);var r=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");z(e,r);B(e,r);t._writer=undefined;e._ownerWritableStream=undefined}function X(e,t){var r=e._ownerWritableStream;f(r!==undefined);var n=r._writableStreamController;var a=J(n,t);if(r!==e._ownerWritableStream){return Promise.reject(oe("write to"))}var i=r._state;if(i==="errored"){return Promise.reject(r._storedError)}if(O(r)===true||i==="closed"){return Promise.reject(new TypeError("The stream is closing or closed and cannot be written to"))}if(i==="erroring"){return Promise.reject(r._storedError)}f(i==="writable");var s=w(r);K(n,t,a);return s}var V=function(){function e(t,r,n,i){a(this,e);if(_(t)===false){throw new TypeError("WritableStreamDefaultController can only be constructed with a WritableStream instance")}if(t._writableStreamController!==undefined){throw new TypeError("WritableStreamDefaultController instances can only be created by the WritableStream constructor")}this._controlledWritableStream=t;this._underlyingSink=r;this._queue=undefined;this._queueTotalSize=undefined;g(this);this._started=false;var s=u(n,i);this._strategySize=s.size;this._strategyHWM=s.highWaterMark;var o=ne(this);D(t,o)}n(e,[{key:"error",value:function e(t){if(Z(this)===false){throw new TypeError("WritableStreamDefaultController.prototype.error can only be used on a WritableStreamDefaultController")}var r=this._controlledWritableStream._state;if(r!=="writable"){return}ae(this,t)}},{key:"__abortSteps",value:function e(t){return o(this._underlyingSink,"abort",[t])}},{key:"__errorSteps",value:function e(){g(this)}},{key:"__startSteps",value:function e(){var t=this;var r=s(this._underlyingSink,"start",[this]);var n=this._controlledWritableStream;Promise.resolve(r).then(function(){f(n._state==="writable"||n._state==="erroring");t._started=true;$(t)},function(e){f(n._state==="writable"||n._state==="erroring");t._started=true;P(n,e)}).catch(d)}}]);return e}();function Y(e){p(e,"close",0);$(e)}function J(e,t){var r=e._strategySize;if(r===undefined){return 1}try{return r(t)}catch(t){ee(e,t);return 1}}function Q(e){return e._strategyHWM-e._queueTotalSize}function K(e,t,r){var n={chunk:t};try{p(e,n,r)}catch(t){ee(e,t);return}var a=e._controlledWritableStream;if(O(a)===false&&a._state==="writable"){var i=ne(e);D(a,i)}$(e)}function Z(e){if(!l(e)){return false}if(!Object.prototype.hasOwnProperty.call(e,"_underlyingSink")){return false}return true}function $(e){var t=e._controlledWritableStream;if(e._started===false){return}if(t._inFlightWriteRequest!==undefined){return}var r=t._state;if(r==="closed"||r==="errored"){return}if(r==="erroring"){C(t);return}if(e._queue.length===0){return}var n=m(e);if(n==="close"){te(e)}else{re(e,n.chunk)}}function ee(e,t){if(e._controlledWritableStream._state==="writable"){ae(e,t)}}function te(e){var t=e._controlledWritableStream;I(t);v(e);f(e._queue.length===0,"queue must be empty once the final write record is dequeued");var r=o(e._underlyingSink,"close",[]);r.then(function(){T(t)},function(e){E(t,e)}).catch(d)}function re(e,t){var r=e._controlledWritableStream;j(r);var n=o(e._underlyingSink,"write",[t,e]);n.then(function(){R(r);var t=r._state;f(t==="writable"||t==="erroring");v(e);if(O(r)===false&&t==="writable"){var n=ne(e);D(r,n)}$(e)},function(e){x(r,e)}).catch(d)}function ne(e){var t=Q(e);return t<=0}function ae(e,t){var r=e._controlledWritableStream;f(r._state==="writable");k(r,t)}function ie(e){return new TypeError("WritableStream.prototype."+e+" can only be used on a WritableStream")}function se(e){return new TypeError("WritableStreamDefaultWriter.prototype."+e+" can only be used on a WritableStreamDefaultWriter")}function oe(e){return new TypeError("Cannot "+e+" a stream using a released writer")}function ue(e){e._closedPromise=new Promise(function(t,r){e._closedPromise_resolve=t;e._closedPromise_reject=r;e._closedPromiseState="pending"})}function le(e,t){e._closedPromise=Promise.reject(t);e._closedPromise_resolve=undefined;e._closedPromise_reject=undefined;e._closedPromiseState="rejected"}function ce(e){e._closedPromise=Promise.resolve(undefined);e._closedPromise_resolve=undefined;e._closedPromise_reject=undefined;e._closedPromiseState="resolved"}function fe(e,t){f(e._closedPromise_resolve!==undefined,"writer._closedPromise_resolve !== undefined");f(e._closedPromise_reject!==undefined,"writer._closedPromise_reject !== undefined");f(e._closedPromiseState==="pending","writer._closedPromiseState is pending");e._closedPromise_reject(t);e._closedPromise_resolve=undefined;e._closedPromise_reject=undefined;e._closedPromiseState="rejected"}function de(e,t){f(e._closedPromise_resolve===undefined,"writer._closedPromise_resolve === undefined");f(e._closedPromise_reject===undefined,"writer._closedPromise_reject === undefined");f(e._closedPromiseState!=="pending","writer._closedPromiseState is not pending");e._closedPromise=Promise.reject(t);e._closedPromiseState="rejected"}function he(e){f(e._closedPromise_resolve!==undefined,"writer._closedPromise_resolve !== undefined");f(e._closedPromise_reject!==undefined,"writer._closedPromise_reject !== undefined");f(e._closedPromiseState==="pending","writer._closedPromiseState is pending");e._closedPromise_resolve(undefined);e._closedPromise_resolve=undefined;e._closedPromise_reject=undefined;e._closedPromiseState="resolved"}function ve(e){e._readyPromise=new Promise(function(t,r){e._readyPromise_resolve=t;e._readyPromise_reject=r});e._readyPromiseState="pending"}function pe(e,t){e._readyPromise=Promise.reject(t);e._readyPromise_resolve=undefined;e._readyPromise_reject=undefined;e._readyPromiseState="rejected"}function me(e){e._readyPromise=Promise.resolve(undefined);e._readyPromise_resolve=undefined;e._readyPromise_reject=undefined;e._readyPromiseState="fulfilled"}function ge(e,t){f(e._readyPromise_resolve!==undefined,"writer._readyPromise_resolve !== undefined");f(e._readyPromise_reject!==undefined,"writer._readyPromise_reject !== undefined");e._readyPromise_reject(t);e._readyPromise_resolve=undefined;e._readyPromise_reject=undefined;e._readyPromiseState="rejected"}function be(e){f(e._readyPromise_resolve===undefined,"writer._readyPromise_resolve === undefined");f(e._readyPromise_reject===undefined,"writer._readyPromise_reject === undefined");e._readyPromise=new Promise(function(t,r){e._readyPromise_resolve=t;e._readyPromise_reject=r});e._readyPromiseState="pending"}function ye(e,t){f(e._readyPromise_resolve===undefined,"writer._readyPromise_resolve === undefined");f(e._readyPromise_reject===undefined,"writer._readyPromise_reject === undefined");e._readyPromise=Promise.reject(t);e._readyPromiseState="rejected"}function _e(e){f(e._readyPromise_resolve!==undefined,"writer._readyPromise_resolve !== undefined");f(e._readyPromise_reject!==undefined,"writer._readyPromise_reject !== undefined");e._readyPromise_resolve(undefined);e._readyPromise_resolve=undefined;e._readyPromise_reject=undefined;e._readyPromiseState="fulfilled"}},function(e,t,r){"use strict";var n=r(0),a=n.IsFiniteNonNegativeNumber;var i=r(1),s=i.assert;t.DequeueValue=function(e){s("_queue"in e&&"_queueTotalSize"in e,"Spec-level failure: DequeueValue should only be used on containers with [[queue]] and [[queueTotalSize]].");s(e._queue.length>0,"Spec-level failure: should never dequeue from an empty queue.");var t=e._queue.shift();e._queueTotalSize-=t.size;if(e._queueTotalSize<0){e._queueTotalSize=0}return t.value};t.EnqueueValueWithSize=function(e,t,r){s("_queue"in e&&"_queueTotalSize"in e,"Spec-level failure: EnqueueValueWithSize should only be used on containers with [[queue]] and "+"[[queueTotalSize]].");r=Number(r);if(!a(r)){throw new RangeError("Size must be a finite, non-NaN, non-negative number.")}e._queue.push({value:t,size:r});e._queueTotalSize+=r};t.PeekQueueValue=function(e){s("_queue"in e&&"_queueTotalSize"in e,"Spec-level failure: PeekQueueValue should only be used on containers with [[queue]] and [[queueTotalSize]].");s(e._queue.length>0,"Spec-level failure: should never peek at an empty queue.");var t=e._queue[0];return t.value};t.ResetQueue=function(e){s("_queue"in e&&"_queueTotalSize"in e,"Spec-level failure: ResetQueue should only be used on containers with [[queue]] and [[queueTotalSize]].");e._queue=[];e._queueTotalSize=0}},function(e,t,r){"use strict";var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(t,r,n){if(r)e(t.prototype,r);if(n)e(t,n);return t}}();function a(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var i=r(0),s=i.ArrayBufferCopy,o=i.CreateIterResultObject,u=i.IsFiniteNonNegativeNumber,l=i.InvokeOrNoop,c=i.PromiseInvokeOrNoop,f=i.TransferArrayBuffer,d=i.ValidateAndNormalizeQueuingStrategy,h=i.ValidateAndNormalizeHighWaterMark;var v=r(0),p=v.createArrayFromList,m=v.createDataProperty,g=v.typeIsObject;var b=r(1),y=b.assert,_=b.rethrowAssertionErrorRejection;var A=r(3),S=A.DequeueValue,w=A.EnqueueValueWithSize,P=A.ResetQueue;var k=r(2),C=k.AcquireWritableStreamDefaultWriter,R=k.IsWritableStream,x=k.IsWritableStreamLocked,T=k.WritableStreamAbort,E=k.WritableStreamDefaultWriterCloseWithErrorPropagation,O=k.WritableStreamDefaultWriterRelease,L=k.WritableStreamDefaultWriterWrite,I=k.WritableStreamCloseQueuedOrInFlight;var j=function(){function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{},n=r.size,i=r.highWaterMark;a(this,e);this._state="readable";this._reader=undefined;this._storedError=undefined;this._disturbed=false;this._readableStreamController=undefined;var s=t.type;var o=String(s);if(o==="bytes"){if(i===undefined){i=0}this._readableStreamController=new _e(this,t,i)}else if(s===undefined){if(i===undefined){i=1}this._readableStreamController=new ce(this,t,n,i)}else{throw new RangeError("Invalid type is specified")}}n(e,[{key:"cancel",value:function e(t){if(M(this)===false){return Promise.reject(He("cancel"))}if(q(this)===true){return Promise.reject(new TypeError("Cannot cancel a stream that already has a reader"))}return X(this,t)}},{key:"getReader",value:function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{},r=t.mode;if(M(this)===false){throw He("getReader")}if(r===undefined){return D(this)}r=String(r);if(r==="byob"){return F(this)}throw new RangeError("Invalid mode is specified")}},{key:"pipeThrough",value:function e(t,r){var n=t.writable,a=t.readable;var i=this.pipeTo(n,r);at(i);return a}},{key:"pipeTo",value:function e(t){var r=this;var n=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{},a=n.preventClose,i=n.preventAbort,s=n.preventCancel;if(M(this)===false){return Promise.reject(He("pipeTo"))}if(R(t)===false){return Promise.reject(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"))}a=Boolean(a);i=Boolean(i);s=Boolean(s);if(q(this)===true){return Promise.reject(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream"))}if(x(t)===true){return Promise.reject(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream"))}var o=D(this);var u=C(t);var l=false;var c=Promise.resolve();return new Promise(function(e,n){function f(){c=Promise.resolve();if(l===true){return Promise.resolve()}return u._readyPromise.then(function(){return le(o).then(function(e){var t=e.value,r=e.done;if(r===true){return}c=L(u,t).catch(function(){})})}).then(f)}v(r,o._closedPromise,function(e){if(i===false){m(function(){return T(t,e)},true,e)}else{g(true,e)}});v(t,u._closedPromise,function(e){if(s===false){m(function(){return X(r,e)},true,e)}else{g(true,e)}});p(r,o._closedPromise,function(){if(a===false){m(function(){return E(u)})}else{g()}});if(I(t)===true||t._state==="closed"){var d=new TypeError("the destination writable stream closed before all data could be piped to it");if(s===false){m(function(){return X(r,d)},true,d)}else{g(true,d)}}f().catch(function(e){c=Promise.resolve();_(e)});function h(){var e=c;return c.then(function(){return e!==c?h():undefined})}function v(e,t,r){if(e._state==="errored"){r(e._storedError)}else{t.catch(r).catch(_)}}function p(e,t,r){if(e._state==="closed"){r()}else{t.then(r).catch(_)}}function m(e,r,n){if(l===true){return}l=true;if(t._state==="writable"&&I(t)===false){h().then(a)}else{a()}function a(){e().then(function(){return b(r,n)},function(e){return b(true,e)}).catch(_)}}function g(e,r){if(l===true){return}l=true;if(t._state==="writable"&&I(t)===false){h().then(function(){return b(e,r)}).catch(_)}else{b(e,r)}}function b(t,r){O(u);oe(o);if(t){n(r)}else{e(undefined)}}})}},{key:"tee",value:function e(){if(M(this)===false){throw He("tee")}var t=U(this,false);return p(t)}},{key:"locked",get:function e(){if(M(this)===false){throw He("locked")}return q(this)}}]);return e}();e.exports={ReadableStream:j,IsReadableStreamDisturbed:N,ReadableStreamDefaultControllerClose:ve,ReadableStreamDefaultControllerEnqueue:pe,ReadableStreamDefaultControllerError:me,ReadableStreamDefaultControllerGetDesiredSize:be};function F(e){return new re(e)}function D(e){return new te(e)}function M(e){if(!g(e)){return false}if(!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")){return false}return true}function N(e){y(M(e)===true,"IsReadableStreamDisturbed should only be used on known readable streams");return e._disturbed}function q(e){y(M(e)===true,"IsReadableStreamLocked should only be used on known readable streams");if(e._reader===undefined){return false}return true}function U(e,t){y(M(e)===true);y(typeof t==="boolean");var r=D(e);var n={closedOrErrored:false,canceled1:false,canceled2:false,reason1:undefined,reason2:undefined};n.promise=new Promise(function(e){n._resolve=e});var a=W();a._reader=r;a._teeState=n;a._cloneForBranch2=t;var i=B();i._stream=e;i._teeState=n;var s=z();s._stream=e;s._teeState=n;var o=Object.create(Object.prototype);m(o,"pull",a);m(o,"cancel",i);var u=new j(o);var l=Object.create(Object.prototype);m(l,"pull",a);m(l,"cancel",s);var c=new j(l);a._branch1=u._readableStreamController;a._branch2=c._readableStreamController;r._closedPromise.catch(function(e){if(n.closedOrErrored===true){return}me(a._branch1,e);me(a._branch2,e);n.closedOrErrored=true});return[u,c]}function W(){function e(){var t=e._reader,r=e._branch1,n=e._branch2,a=e._teeState;return le(t).then(function(e){y(g(e));var t=e.value;var i=e.done;y(typeof i==="boolean");if(i===true&&a.closedOrErrored===false){if(a.canceled1===false){ve(r)}if(a.canceled2===false){ve(n)}a.closedOrErrored=true}if(a.closedOrErrored===true){return}var s=t;var o=t;if(a.canceled1===false){pe(r,s)}if(a.canceled2===false){pe(n,o)}})}return e}function B(){function e(t){var r=e._stream,n=e._teeState;n.canceled1=true;n.reason1=t;if(n.canceled2===true){var a=p([n.reason1,n.reason2]);var i=X(r,a);n._resolve(i)}return n.promise}return e}function z(){function e(t){var r=e._stream,n=e._teeState;n.canceled2=true;n.reason2=t;if(n.canceled1===true){var a=p([n.reason1,n.reason2]);var i=X(r,a);n._resolve(i)}return n.promise}return e}function G(e){y(ne(e._reader)===true);y(e._state==="readable"||e._state==="closed");var t=new Promise(function(t,r){var n={_resolve:t,_reject:r};e._reader._readIntoRequests.push(n)});return t}function H(e){y(ae(e._reader)===true);y(e._state==="readable");var t=new Promise(function(t,r){var n={_resolve:t,_reject:r};e._reader._readRequests.push(n)});return t}function X(e,t){e._disturbed=true;if(e._state==="closed"){return Promise.resolve(undefined)}if(e._state==="errored"){return Promise.reject(e._storedError)}V(e);var r=e._readableStreamController.__cancelSteps(t);return r.then(function(){return undefined})}function V(e){y(e._state==="readable");e._state="closed";var t=e._reader;if(t===undefined){return undefined}if(ae(t)===true){for(var r=0;r<t._readRequests.length;r++){var n=t._readRequests[r]._resolve;n(o(undefined,true))}t._readRequests=[]}$e(t);return undefined}function Y(e,t){y(M(e)===true,"stream must be ReadableStream");y(e._state==="readable","state must be readable");e._state="errored";e._storedError=t;var r=e._reader;if(r===undefined){return undefined}if(ae(r)===true){for(var n=0;n<r._readRequests.length;n++){var a=r._readRequests[n];a._reject(t)}r._readRequests=[]}else{y(ne(r),"reader must be ReadableStreamBYOBReader");for(var i=0;i<r._readIntoRequests.length;i++){var s=r._readIntoRequests[i];s._reject(t)}r._readIntoRequests=[]}Ke(r,t);r._closedPromise.catch(function(){})}function J(e,t,r){var n=e._reader;y(n._readIntoRequests.length>0);var a=n._readIntoRequests.shift();a._resolve(o(t,r))}function Q(e,t,r){var n=e._reader;y(n._readRequests.length>0);var a=n._readRequests.shift();a._resolve(o(t,r))}function K(e){return e._reader._readIntoRequests.length}function Z(e){return e._reader._readRequests.length}function $(e){var t=e._reader;if(t===undefined){return false}if(ne(t)===false){return false}return true}function ee(e){var t=e._reader;if(t===undefined){return false}if(ae(t)===false){return false}return true}var te=function(){function e(t){a(this,e);if(M(t)===false){throw new TypeError("ReadableStreamDefaultReader can only be constructed with a ReadableStream instance")}if(q(t)===true){throw new TypeError("This stream has already been locked for exclusive reading by another reader")}ie(this,t);this._readRequests=[]}n(e,[{key:"cancel",value:function e(t){if(ae(this)===false){return Promise.reject(Ve("cancel"))}if(this._ownerReadableStream===undefined){return Promise.reject(Xe("cancel"))}return se(this,t)}},{key:"read",value:function e(){if(ae(this)===false){return Promise.reject(Ve("read"))}if(this._ownerReadableStream===undefined){return Promise.reject(Xe("read from"))}return le(this)}},{key:"releaseLock",value:function e(){if(ae(this)===false){throw Ve("releaseLock")}if(this._ownerReadableStream===undefined){return}if(this._readRequests.length>0){throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled")}oe(this)}},{key:"closed",get:function e(){if(ae(this)===false){return Promise.reject(Ve("closed"))}return this._closedPromise}}]);return e}();var re=function(){function e(t){a(this,e);if(!M(t)){throw new TypeError("ReadableStreamBYOBReader can only be constructed with a ReadableStream instance given a "+"byte source")}if(Ae(t._readableStreamController)===false){throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte "+"source")}if(q(t)){throw new TypeError("This stream has already been locked for exclusive reading by another reader")}ie(this,t);this._readIntoRequests=[]}n(e,[{key:"cancel",value:function e(t){if(!ne(this)){return Promise.reject(et("cancel"))}if(this._ownerReadableStream===undefined){return Promise.reject(Xe("cancel"))}return se(this,t)}},{key:"read",value:function e(t){if(!ne(this)){return Promise.reject(et("read"))}if(this._ownerReadableStream===undefined){return Promise.reject(Xe("read from"))}if(!ArrayBuffer.isView(t)){return Promise.reject(new TypeError("view must be an array buffer view"))}if(t.byteLength===0){return Promise.reject(new TypeError("view must have non-zero byteLength"))}return ue(this,t)}},{key:"releaseLock",value:function e(){if(!ne(this)){throw et("releaseLock")}if(this._ownerReadableStream===undefined){return}if(this._readIntoRequests.length>0){throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled")}oe(this)}},{key:"closed",get:function e(){if(!ne(this)){return Promise.reject(et("closed"))}return this._closedPromise}}]);return e}();function ne(e){if(!g(e)){return false}if(!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")){return false}return true}function ae(e){if(!g(e)){return false}if(!Object.prototype.hasOwnProperty.call(e,"_readRequests")){return false}return true}function ie(e,t){e._ownerReadableStream=t;t._reader=e;if(t._state==="readable"){Ye(e)}else if(t._state==="closed"){Qe(e)}else{y(t._state==="errored","state must be errored");Je(e,t._storedError);e._closedPromise.catch(function(){})}}function se(e,t){var r=e._ownerReadableStream;y(r!==undefined);return X(r,t)}function oe(e){y(e._ownerReadableStream!==undefined);y(e._ownerReadableStream._reader===e);if(e._ownerReadableStream._state==="readable"){Ke(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness"))}else{Ze(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness"))}e._closedPromise.catch(function(){});e._ownerReadableStream._reader=undefined;e._ownerReadableStream=undefined}function ue(e,t){var r=e._ownerReadableStream;y(r!==undefined);r._disturbed=true;if(r._state==="errored"){return Promise.reject(r._storedError)}return Ie(r._readableStreamController,t)}function le(e){var t=e._ownerReadableStream;y(t!==undefined);t._disturbed=true;if(t._state==="closed"){return Promise.resolve(o(undefined,true))}if(t._state==="errored"){return Promise.reject(t._storedError)}y(t._state==="readable");return t._readableStreamController.__pullSteps()}var ce=function(){function e(t,r,n,i){a(this,e);if(M(t)===false){throw new TypeError("ReadableStreamDefaultController can only be constructed with a ReadableStream instance")}if(t._readableStreamController!==undefined){throw new TypeError("ReadableStreamDefaultController instances can only be created by the ReadableStream constructor")}this._controlledReadableStream=t;this._underlyingSource=r;this._queue=undefined;this._queueTotalSize=undefined;P(this);this._started=false;this._closeRequested=false;this._pullAgain=false;this._pulling=false;var s=d(n,i);this._strategySize=s.size;this._strategyHWM=s.highWaterMark;var o=this;var u=l(r,"start",[this]);Promise.resolve(u).then(function(){o._started=true;y(o._pulling===false);y(o._pullAgain===false);de(o)},function(e){ge(o,e)}).catch(_)}n(e,[{key:"close",value:function e(){if(fe(this)===false){throw tt("close")}if(this._closeRequested===true){throw new TypeError("The stream has already been closed; do not close it again!")}var t=this._controlledReadableStream._state;if(t!=="readable"){throw new TypeError("The stream (in "+t+" state) is not in the readable state and cannot be closed")}ve(this)}},{key:"enqueue",value:function e(t){if(fe(this)===false){throw tt("enqueue")}if(this._closeRequested===true){throw new TypeError("stream is closed or draining")}var r=this._controlledReadableStream._state;if(r!=="readable"){throw new TypeError("The stream (in "+r+" state) is not in the readable state and cannot be enqueued to")}return pe(this,t)}},{key:"error",value:function e(t){if(fe(this)===false){throw tt("error")}var r=this._controlledReadableStream;if(r._state!=="readable"){throw new TypeError("The stream is "+r._state+" and so cannot be errored")}me(this,t)}},{key:"__cancelSteps",value:function e(t){P(this);return c(this._underlyingSource,"cancel",[t])}},{key:"__pullSteps",value:function e(){var t=this._controlledReadableStream;if(this._queue.length>0){var r=S(this);if(this._closeRequested===true&&this._queue.length===0){V(t)}else{de(this)}return Promise.resolve(o(r,false))}var n=H(t);de(this);return n}},{key:"desiredSize",get:function e(){if(fe(this)===false){throw tt("desiredSize")}return be(this)}}]);return e}();function fe(e){if(!g(e)){return false}if(!Object.prototype.hasOwnProperty.call(e,"_underlyingSource")){return false}return true}function de(e){var t=he(e);if(t===false){return undefined}if(e._pulling===true){e._pullAgain=true;return undefined}y(e._pullAgain===false);e._pulling=true;var r=c(e._underlyingSource,"pull",[e]);r.then(function(){e._pulling=false;if(e._pullAgain===true){e._pullAgain=false;return de(e)}return undefined},function(t){ge(e,t)}).catch(_);return undefined}function he(e){var t=e._controlledReadableStream;if(t._state==="closed"||t._state==="errored"){return false}if(e._closeRequested===true){return false}if(e._started===false){return false}if(q(t)===true&&Z(t)>0){return true}var r=be(e);if(r>0){return true}return false}function ve(e){var t=e._controlledReadableStream;y(e._closeRequested===false);y(t._state==="readable");e._closeRequested=true;if(e._queue.length===0){V(t)}}function pe(e,t){var r=e._controlledReadableStream;y(e._closeRequested===false);y(r._state==="readable");if(q(r)===true&&Z(r)>0){Q(r,t,false)}else{var n=1;if(e._strategySize!==undefined){var a=e._strategySize;try{n=a(t)}catch(t){ge(e,t);throw t}}try{w(e,t,n)}catch(t){ge(e,t);throw t}}de(e);return undefined}function me(e,t){var r=e._controlledReadableStream;y(r._state==="readable");P(e);Y(r,t)}function ge(e,t){if(e._controlledReadableStream._state==="readable"){me(e,t)}}function be(e){var t=e._controlledReadableStream;var r=t._state;if(r==="errored"){return null}if(r==="closed"){return 0}return e._strategyHWM-e._queueTotalSize}var ye=function(){function e(t,r){a(this,e);this._associatedReadableByteStreamController=t;this._view=r}n(e,[{key:"respond",value:function e(t){if(Se(this)===false){throw rt("respond")}if(this._associatedReadableByteStreamController===undefined){throw new TypeError("This BYOB request has been invalidated")}ze(this._associatedReadableByteStreamController,t)}},{key:"respondWithNewView",value:function e(t){if(Se(this)===false){throw rt("respond")}if(this._associatedReadableByteStreamController===undefined){throw new TypeError("This BYOB request has been invalidated")}if(!ArrayBuffer.isView(t)){throw new TypeError("You can only respond with array buffer views")}Ge(this._associatedReadableByteStreamController,t)}},{key:"view",get:function e(){return this._view}}]);return e}();var _e=function(){function e(t,r,n){a(this,e);if(M(t)===false){throw new TypeError("ReadableByteStreamController can only be constructed with a ReadableStream instance given "+"a byte source")}if(t._readableStreamController!==undefined){throw new TypeError("ReadableByteStreamController instances can only be created by the ReadableStream constructor given a byte "+"source")}this._controlledReadableStream=t;this._underlyingByteSource=r;this._pullAgain=false;this._pulling=false;Pe(this);this._queue=this._queueTotalSize=undefined;P(this);this._closeRequested=false;this._started=false;this._strategyHWM=h(n);var i=r.autoAllocateChunkSize;if(i!==undefined){if(Number.isInteger(i)===false||i<=0){throw new RangeError("autoAllocateChunkSize must be a positive integer")}}this._autoAllocateChunkSize=i;this._pendingPullIntos=[];var s=this;var o=l(r,"start",[this]);Promise.resolve(o).then(function(){s._started=true;y(s._pulling===false);y(s._pullAgain===false);we(s)},function(e){if(t._state==="readable"){We(s,e)}}).catch(_)}n(e,[{key:"close",value:function e(){if(Ae(this)===false){throw nt("close")}if(this._closeRequested===true){throw new TypeError("The stream has already been closed; do not close it again!")}var t=this._controlledReadableStream._state;if(t!=="readable"){throw new TypeError("The stream (in "+t+" state) is not in the readable state and cannot be closed")}qe(this)}},{key:"enqueue",value:function e(t){if(Ae(this)===false){throw nt("enqueue")}if(this._closeRequested===true){throw new TypeError("stream is closed or draining")}var r=this._controlledReadableStream._state;if(r!=="readable"){throw new TypeError("The stream (in "+r+" state) is not in the readable state and cannot be enqueued to")}if(!ArrayBuffer.isView(t)){throw new TypeError("You can only enqueue array buffer views when using a ReadableByteStreamController")}Ue(this,t)}},{key:"error",value:function e(t){if(Ae(this)===false){throw nt("error")}var r=this._controlledReadableStream;if(r._state!=="readable"){throw new TypeError("The stream is "+r._state+" and so cannot be errored")}We(this,t)}},{key:"__cancelSteps",value:function e(t){if(this._pendingPullIntos.length>0){var r=this._pendingPullIntos[0];r.bytesFilled=0}P(this);return c(this._underlyingByteSource,"cancel",[t])}},{key:"__pullSteps",value:function e(){var t=this._controlledReadableStream;y(ee(t)===true);if(this._queueTotalSize>0){y(Z(t)===0);var r=this._queue.shift();this._queueTotalSize-=r.byteLength;Ee(this);var n=void 0;try{n=new Uint8Array(r.buffer,r.byteOffset,r.byteLength)}catch(e){return Promise.reject(e)}return Promise.resolve(o(n,false))}var a=this._autoAllocateChunkSize;if(a!==undefined){var i=void 0;try{i=new ArrayBuffer(a)}catch(e){return Promise.reject(e)}var s={buffer:i,byteOffset:0,byteLength:a,bytesFilled:0,elementSize:1,ctor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(s)}var u=H(t);we(this);return u}},{key:"byobRequest",get:function e(){if(Ae(this)===false){throw nt("byobRequest")}if(this._byobRequest===undefined&&this._pendingPullIntos.length>0){var t=this._pendingPullIntos[0];var r=new Uint8Array(t.buffer,t.byteOffset+t.bytesFilled,t.byteLength-t.bytesFilled);this._byobRequest=new ye(this,r)}return this._byobRequest}},{key:"desiredSize",get:function e(){if(Ae(this)===false){throw nt("desiredSize")}return Be(this)}}]);return e}();function Ae(e){if(!g(e)){return false}if(!Object.prototype.hasOwnProperty.call(e,"_underlyingByteSource")){return false}return true}function Se(e){if(!g(e)){return false}if(!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")){return false}return true}function we(e){var t=Ne(e);if(t===false){return undefined}if(e._pulling===true){e._pullAgain=true;return undefined}y(e._pullAgain===false);e._pulling=true;var r=c(e._underlyingByteSource,"pull",[e]);r.then(function(){e._pulling=false;if(e._pullAgain===true){e._pullAgain=false;we(e)}},function(t){if(e._controlledReadableStream._state==="readable"){We(e,t)}}).catch(_);return undefined}function Pe(e){Oe(e);e._pendingPullIntos=[]}function ke(e,t){y(e._state!=="errored","state must not be errored");var r=false;if(e._state==="closed"){y(t.bytesFilled===0);r=true}var n=Ce(t);if(t.readerType==="default"){Q(e,n,r)}else{y(t.readerType==="byob");J(e,n,r)}}function Ce(e){var t=e.bytesFilled;var r=e.elementSize;y(t<=e.byteLength);y(t%r===0);return new e.ctor(e.buffer,e.byteOffset,t/r)}function Re(e,t,r,n){e._queue.push({buffer:t,byteOffset:r,byteLength:n});e._queueTotalSize+=n}function xe(e,t){var r=t.elementSize;var n=t.bytesFilled-t.bytesFilled%r;var a=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled);var i=t.bytesFilled+a;var o=i-i%r;var u=a;var l=false;if(o>n){u=o-t.bytesFilled;l=true}var c=e._queue;while(u>0){var f=c[0];var d=Math.min(u,f.byteLength);var h=t.byteOffset+t.bytesFilled;s(t.buffer,h,f.buffer,f.byteOffset,d);if(f.byteLength===d){c.shift()}else{f.byteOffset+=d;f.byteLength-=d}e._queueTotalSize-=d;Te(e,d,t);u-=d}if(l===false){y(e._queueTotalSize===0,"queue must be empty");y(t.bytesFilled>0);y(t.bytesFilled<t.elementSize)}return l}function Te(e,t,r){y(e._pendingPullIntos.length===0||e._pendingPullIntos[0]===r);Oe(e);r.bytesFilled+=t}function Ee(e){y(e._controlledReadableStream._state==="readable");if(e._queueTotalSize===0&&e._closeRequested===true){V(e._controlledReadableStream)}else{we(e)}}function Oe(e){if(e._byobRequest===undefined){return}e._byobRequest._associatedReadableByteStreamController=undefined;e._byobRequest._view=undefined;e._byobRequest=undefined}function Le(e){y(e._closeRequested===false);while(e._pendingPullIntos.length>0){if(e._queueTotalSize===0){return}var t=e._pendingPullIntos[0];if(xe(e,t)===true){Me(e);ke(e._controlledReadableStream,t)}}}function Ie(e,t){var r=e._controlledReadableStream;var n=1;if(t.constructor!==DataView){n=t.constructor.BYTES_PER_ELEMENT}var a=t.constructor;var i={buffer:t.buffer,byteOffset:t.byteOffset,byteLength:t.byteLength,bytesFilled:0,elementSize:n,ctor:a,readerType:"byob"};if(e._pendingPullIntos.length>0){i.buffer=f(i.buffer);e._pendingPullIntos.push(i);return G(r)}if(r._state==="closed"){var s=new t.constructor(i.buffer,i.byteOffset,0);return Promise.resolve(o(s,true))}if(e._queueTotalSize>0){if(xe(e,i)===true){var u=Ce(i);Ee(e);return Promise.resolve(o(u,false))}if(e._closeRequested===true){var l=new TypeError("Insufficient bytes to fill elements in the given buffer");We(e,l);return Promise.reject(l)}}i.buffer=f(i.buffer);e._pendingPullIntos.push(i);var c=G(r);we(e);return c}function je(e,t){t.buffer=f(t.buffer);y(t.bytesFilled===0,"bytesFilled must be 0");var r=e._controlledReadableStream;if($(r)===true){while(K(r)>0){var n=Me(e);ke(r,n)}}}function Fe(e,t,r){if(r.bytesFilled+t>r.byteLength){throw new RangeError("bytesWritten out of range")}Te(e,t,r);if(r.bytesFilled<r.elementSize){return}Me(e);var n=r.bytesFilled%r.elementSize;if(n>0){var a=r.byteOffset+r.bytesFilled;var i=r.buffer.slice(a-n,a);Re(e,i,0,i.byteLength)}r.buffer=f(r.buffer);r.bytesFilled-=n;ke(e._controlledReadableStream,r);Le(e)}function De(e,t){var r=e._pendingPullIntos[0];var n=e._controlledReadableStream;if(n._state==="closed"){if(t!==0){throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}je(e,r)}else{y(n._state==="readable");Fe(e,t,r)}}function Me(e){var t=e._pendingPullIntos.shift();Oe(e);return t}function Ne(e){var t=e._controlledReadableStream;if(t._state!=="readable"){return false}if(e._closeRequested===true){return false}if(e._started===false){return false}if(ee(t)===true&&Z(t)>0){return true}if($(t)===true&&K(t)>0){return true}if(Be(e)>0){return true}return false}function qe(e){var t=e._controlledReadableStream;y(e._closeRequested===false);y(t._state==="readable");if(e._queueTotalSize>0){e._closeRequested=true;return}if(e._pendingPullIntos.length>0){var r=e._pendingPullIntos[0];if(r.bytesFilled>0){var n=new TypeError("Insufficient bytes to fill elements in the given buffer");We(e,n);throw n}}V(t)}function Ue(e,t){var r=e._controlledReadableStream;y(e._closeRequested===false);y(r._state==="readable");var n=t.buffer;var a=t.byteOffset;var i=t.byteLength;var s=f(n);if(ee(r)===true){if(Z(r)===0){Re(e,s,a,i)}else{y(e._queue.length===0);var o=new Uint8Array(s,a,i);Q(r,o,false)}}else if($(r)===true){Re(e,s,a,i);Le(e)}else{y(q(r)===false,"stream must not be locked");Re(e,s,a,i)}}function We(e,t){var r=e._controlledReadableStream;y(r._state==="readable");Pe(e);P(e);Y(r,t)}function Be(e){var t=e._controlledReadableStream;var r=t._state;if(r==="errored"){return null}if(r==="closed"){return 0}return e._strategyHWM-e._queueTotalSize}function ze(e,t){t=Number(t);if(u(t)===false){throw new RangeError("bytesWritten must be a finite")}y(e._pendingPullIntos.length>0);De(e,t)}function Ge(e,t){y(e._pendingPullIntos.length>0);var r=e._pendingPullIntos[0];if(r.byteOffset+r.bytesFilled!==t.byteOffset){throw new RangeError("The region specified by view does not match byobRequest")}if(r.byteLength!==t.byteLength){throw new RangeError("The buffer of view has different capacity than byobRequest")}r.buffer=t.buffer;De(e,t.byteLength)}function He(e){return new TypeError("ReadableStream.prototype."+e+" can only be used on a ReadableStream")}function Xe(e){return new TypeError("Cannot "+e+" a stream using a released reader")}function Ve(e){return new TypeError("ReadableStreamDefaultReader.prototype."+e+" can only be used on a ReadableStreamDefaultReader")}function Ye(e){e._closedPromise=new Promise(function(t,r){e._closedPromise_resolve=t;e._closedPromise_reject=r})}function Je(e,t){e._closedPromise=Promise.reject(t);e._closedPromise_resolve=undefined;e._closedPromise_reject=undefined}function Qe(e){e._closedPromise=Promise.resolve(undefined);e._closedPromise_resolve=undefined;e._closedPromise_reject=undefined}function Ke(e,t){y(e._closedPromise_resolve!==undefined);y(e._closedPromise_reject!==undefined);e._closedPromise_reject(t);e._closedPromise_resolve=undefined;e._closedPromise_reject=undefined}function Ze(e,t){y(e._closedPromise_resolve===undefined);y(e._closedPromise_reject===undefined);e._closedPromise=Promise.reject(t)}function $e(e){y(e._closedPromise_resolve!==undefined);y(e._closedPromise_reject!==undefined);e._closedPromise_resolve(undefined);e._closedPromise_resolve=undefined;e._closedPromise_reject=undefined}function et(e){return new TypeError("ReadableStreamBYOBReader.prototype."+e+" can only be used on a ReadableStreamBYOBReader")}function tt(e){return new TypeError("ReadableStreamDefaultController.prototype."+e+" can only be used on a ReadableStreamDefaultController")}function rt(e){return new TypeError("ReadableStreamBYOBRequest.prototype."+e+" can only be used on a ReadableStreamBYOBRequest")}function nt(e){return new TypeError("ReadableByteStreamController.prototype."+e+" can only be used on a ReadableByteStreamController")}function at(e){try{Promise.prototype.then.call(e,undefined,function(){})}catch(e){}}},function(e,t,r){"use strict";var n=r(6);var a=r(4);var i=r(2);t.TransformStream=n.TransformStream;t.ReadableStream=a.ReadableStream;t.IsReadableStreamDisturbed=a.IsReadableStreamDisturbed;t.ReadableStreamDefaultControllerClose=a.ReadableStreamDefaultControllerClose;t.ReadableStreamDefaultControllerEnqueue=a.ReadableStreamDefaultControllerEnqueue;t.ReadableStreamDefaultControllerError=a.ReadableStreamDefaultControllerError;t.ReadableStreamDefaultControllerGetDesiredSize=a.ReadableStreamDefaultControllerGetDesiredSize;t.AcquireWritableStreamDefaultWriter=i.AcquireWritableStreamDefaultWriter;t.IsWritableStream=i.IsWritableStream;t.IsWritableStreamLocked=i.IsWritableStreamLocked;t.WritableStream=i.WritableStream;t.WritableStreamAbort=i.WritableStreamAbort;t.WritableStreamDefaultControllerError=i.WritableStreamDefaultControllerError;t.WritableStreamDefaultWriterCloseWithErrorPropagation=i.WritableStreamDefaultWriterCloseWithErrorPropagation;t.WritableStreamDefaultWriterRelease=i.WritableStreamDefaultWriterRelease;t.WritableStreamDefaultWriterWrite=i.WritableStreamDefaultWriterWrite},function(e,t,r){"use strict";var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(t,r,n){if(r)e(t.prototype,r);if(n)e(t,n);return t}}();function a(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var i=r(1),s=i.assert;var o=r(0),u=o.InvokeOrNoop,l=o.PromiseInvokeOrPerformFallback,c=o.PromiseInvokeOrNoop,f=o.typeIsObject;var d=r(4),h=d.ReadableStream,v=d.ReadableStreamDefaultControllerClose,p=d.ReadableStreamDefaultControllerEnqueue,m=d.ReadableStreamDefaultControllerError,g=d.ReadableStreamDefaultControllerGetDesiredSize;var b=r(2),y=b.WritableStream,_=b.WritableStreamDefaultControllerError;function A(e){if(e._errored===true){throw new TypeError("TransformStream is already errored")}if(e._readableClosed===true){throw new TypeError("Readable side is already closed")}P(e)}function S(e,t){if(e._errored===true){throw new TypeError("TransformStream is already errored")}if(e._readableClosed===true){throw new TypeError("Readable side is already closed")}var r=e._readableController;try{p(r,t)}catch(t){e._readableClosed=true;k(e,t);throw e._storedError}var n=g(r);var a=n<=0;if(a===true&&e._backpressure===false){x(e,true)}}function w(e,t){if(e._errored===true){throw new TypeError("TransformStream is already errored")}C(e,t)}function P(e){s(e._errored===false);s(e._readableClosed===false);try{v(e._readableController)}catch(e){s(false)}e._readableClosed=true}function k(e,t){if(e._errored===false){C(e,t)}}function C(e,t){s(e._errored===false);e._errored=true;e._storedError=t;if(e._writableDone===false){_(e._writableController,t)}if(e._readableClosed===false){m(e._readableController,t)}}function R(e){s(e._backpressureChangePromise!==undefined,"_backpressureChangePromise should have been initialized");if(e._backpressure===false){return Promise.resolve()}s(e._backpressure===true,"_backpressure should have been initialized");return e._backpressureChangePromise}function x(e,t){s(e._backpressure!==t,"TransformStreamSetBackpressure() should be called only when backpressure is changed");if(e._backpressureChangePromise!==undefined){e._backpressureChangePromise_resolve(t)}e._backpressureChangePromise=new Promise(function(t){e._backpressureChangePromise_resolve=t});e._backpressureChangePromise.then(function(e){s(e!==t,"_backpressureChangePromise should be fulfilled only when backpressure is changed")});e._backpressure=t}function T(e,t){var r=t._controlledTransformStream;S(r,e);return Promise.resolve()}function E(e,t){s(e._errored===false);s(e._transforming===false);s(e._backpressure===false);e._transforming=true;var r=e._transformer;var n=e._transformStreamController;var a=l(r,"transform",[t,n],T,[t,n]);return a.then(function(){e._transforming=false;return R(e)},function(t){k(e,t);return Promise.reject(t)})}function O(e){if(!f(e)){return false}if(!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")){return false}return true}function L(e){if(!f(e)){return false}if(!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")){return false}return true}var I=function(){function e(t,r){a(this,e);this._transformStream=t;this._startPromise=r}n(e,[{key:"start",value:function e(t){var r=this._transformStream;r._writableController=t;return this._startPromise.then(function(){return R(r)})}},{key:"write",value:function e(t){var r=this._transformStream;return E(r,t)}},{key:"abort",value:function e(){var t=this._transformStream;t._writableDone=true;C(t,new TypeError("Writable side aborted"))}},{key:"close",value:function e(){var t=this._transformStream;s(t._transforming===false);t._writableDone=true;var r=c(t._transformer,"flush",[t._transformStreamController]);return r.then(function(){if(t._errored===true){return Promise.reject(t._storedError)}if(t._readableClosed===false){P(t)}return Promise.resolve()}).catch(function(e){k(t,e);return Promise.reject(t._storedError)})}}]);return e}();var j=function(){function e(t,r){a(this,e);this._transformStream=t;this._startPromise=r}n(e,[{key:"start",value:function e(t){var r=this._transformStream;r._readableController=t;return this._startPromise.then(function(){s(r._backpressureChangePromise!==undefined,"_backpressureChangePromise should have been initialized");if(r._backpressure===true){return Promise.resolve()}s(r._backpressure===false,"_backpressure should have been initialized");return r._backpressureChangePromise})}},{key:"pull",value:function e(){var t=this._transformStream;s(t._backpressure===true,"pull() should be never called while _backpressure is false");s(t._backpressureChangePromise!==undefined,"_backpressureChangePromise should have been initialized");x(t,false);return t._backpressureChangePromise}},{key:"cancel",value:function e(){var t=this._transformStream;t._readableClosed=true;C(t,new TypeError("Readable side canceled"))}}]);return e}();var F=function(){function e(t){a(this,e);if(L(t)===false){throw new TypeError("TransformStreamDefaultController can only be "+"constructed with a TransformStream instance")}if(t._transformStreamController!==undefined){throw new TypeError("TransformStreamDefaultController instances can "+"only be created by the TransformStream constructor")}this._controlledTransformStream=t}n(e,[{key:"enqueue",value:function e(t){if(O(this)===false){throw M("enqueue")}S(this._controlledTransformStream,t)}},{key:"close",value:function e(){if(O(this)===false){throw M("close")}A(this._controlledTransformStream)}},{key:"error",value:function e(t){if(O(this)===false){throw M("error")}w(this._controlledTransformStream,t)}},{key:"desiredSize",get:function e(){if(O(this)===false){throw M("desiredSize")}var t=this._controlledTransformStream;var r=t._readableController;return g(r)}}]);return e}();var D=function(){function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};a(this,e);this._transformer=t;var r=t.readableStrategy,n=t.writableStrategy;this._transforming=false;this._errored=false;this._storedError=undefined;this._writableController=undefined;this._readableController=undefined;this._transformStreamController=undefined;this._writableDone=false;this._readableClosed=false;this._backpressure=undefined;this._backpressureChangePromise=undefined;this._backpressureChangePromise_resolve=undefined;this._transformStreamController=new F(this);var i=void 0;var o=new Promise(function(e){i=e});var l=new j(this,o);this._readable=new h(l,r);var c=new I(this,o);this._writable=new y(c,n);s(this._writableController!==undefined);s(this._readableController!==undefined);var f=g(this._readableController);x(this,f<=0);var d=this;var v=u(t,"start",[d._transformStreamController]);i(v);o.catch(function(e){if(d._errored===false){d._errored=true;d._storedError=e}})}n(e,[{key:"readable",get:function e(){if(L(this)===false){throw N("readable")}return this._readable}},{key:"writable",get:function e(){if(L(this)===false){throw N("writable")}return this._writable}}]);return e}();e.exports={TransformStream:D};function M(e){return new TypeError("TransformStreamDefaultController.prototype."+e+" can only be used on a TransformStreamDefaultController")}function N(e){return new TypeError("TransformStream.prototype."+e+" can only be used on a TransformStream")}},function(e,t,r){e.exports=r(5)}]))},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.PDFJS=t.globalScope=undefined;var n=r(69);var a=r(15);var i=r(0);var s=r(72);var o=r(20);var u=d(o);var l=r(71);var c=r(73);var f=r(74);function d(e){return e&&e.__esModule?e:{default:e}}if(!u.default.PDFJS){u.default.PDFJS={}}var h=u.default.PDFJS;{h.version="1.10.100";h.build="ea29ec83"}h.pdfBug=false;if(h.verbosity!==undefined){(0,i.setVerbosityLevel)(h.verbosity)}delete h.verbosity;Object.defineProperty(h,"verbosity",{get:function e(){return(0,i.getVerbosityLevel)()},set:function e(t){(0,i.setVerbosityLevel)(t)},enumerable:true,configurable:true});h.VERBOSITY_LEVELS=i.VERBOSITY_LEVELS;h.OPS=i.OPS;h.UNSUPPORTED_FEATURES=i.UNSUPPORTED_FEATURES;h.isValidUrl=a.isValidUrl;h.shadow=i.shadow;h.createBlob=i.createBlob;h.createObjectURL=function e(t,r){return(0,i.createObjectURL)(t,r,h.disableCreateObjectURL)};Object.defineProperty(h,"isLittleEndian",{configurable:true,get:function e(){return(0,i.shadow)(h,"isLittleEndian",(0,i.isLittleEndian)())}});h.removeNullCharacters=i.removeNullCharacters;h.PasswordResponses=i.PasswordResponses;h.PasswordException=i.PasswordException;h.UnknownErrorException=i.UnknownErrorException;h.InvalidPDFException=i.InvalidPDFException;h.MissingPDFException=i.MissingPDFException;h.UnexpectedResponseException=i.UnexpectedResponseException;h.Util=i.Util;h.PageViewport=i.PageViewport;h.createPromiseCapability=i.createPromiseCapability;h.maxImageSize=h.maxImageSize===undefined?-1:h.maxImageSize;h.cMapUrl=h.cMapUrl===undefined?null:h.cMapUrl;h.cMapPacked=h.cMapPacked===undefined?false:h.cMapPacked;h.disableFontFace=h.disableFontFace===undefined?false:h.disableFontFace;h.imageResourcesPath=h.imageResourcesPath===undefined?"":h.imageResourcesPath;h.disableWorker=h.disableWorker===undefined?false:h.disableWorker;h.workerSrc=h.workerSrc===undefined?null:h.workerSrc;h.workerPort=h.workerPort===undefined?null:h.workerPort;h.disableRange=h.disableRange===undefined?false:h.disableRange;h.disableStream=h.disableStream===undefined?false:h.disableStream;h.disableAutoFetch=h.disableAutoFetch===undefined?false:h.disableAutoFetch;h.pdfBug=h.pdfBug===undefined?false:h.pdfBug;h.postMessageTransfers=h.postMessageTransfers===undefined?true:h.postMessageTransfers;h.disableCreateObjectURL=h.disableCreateObjectURL===undefined?false:h.disableCreateObjectURL;h.disableWebGL=h.disableWebGL===undefined?true:h.disableWebGL;h.externalLinkTarget=h.externalLinkTarget===undefined?a.LinkTarget.NONE:h.externalLinkTarget;h.externalLinkRel=h.externalLinkRel===undefined?a.DEFAULT_LINK_REL:h.externalLinkRel;h.isEvalSupported=h.isEvalSupported===undefined?true:h.isEvalSupported;h.pdfjsNext=h.pdfjsNext===undefined?false:h.pdfjsNext;{var v=h.openExternalLinksInNewWindow;delete h.openExternalLinksInNewWindow;Object.defineProperty(h,"openExternalLinksInNewWindow",{get:function e(){return h.externalLinkTarget===a.LinkTarget.BLANK},set:function e(t){if(t){(0,i.deprecated)("PDFJS.openExternalLinksInNewWindow, please use "+'"PDFJS.externalLinkTarget = PDFJS.LinkTarget.BLANK" instead.')}if(h.externalLinkTarget!==a.LinkTarget.NONE){(0,i.warn)("PDFJS.externalLinkTarget is already initialized");return}h.externalLinkTarget=t?a.LinkTarget.BLANK:a.LinkTarget.NONE},enumerable:true,configurable:true});if(v){h.openExternalLinksInNewWindow=v}}h.getDocument=n.getDocument;h.LoopbackPort=n.LoopbackPort;h.PDFDataRangeTransport=n.PDFDataRangeTransport;h.PDFWorker=n.PDFWorker;h.hasCanvasTypedArrays=true;h.CustomStyle=a.CustomStyle;h.LinkTarget=a.LinkTarget;h.addLinkAttributes=a.addLinkAttributes;h.getFilenameFromUrl=a.getFilenameFromUrl;h.isExternalLinkTargetSet=a.isExternalLinkTargetSet;h.AnnotationLayer=s.AnnotationLayer;h.renderTextLayer=c.renderTextLayer;h.Metadata=l.Metadata;h.SVGGraphics=f.SVGGraphics;h.UnsupportedManager=n._UnsupportedManager;t.globalScope=u.default;t.PDFJS=h},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.FontLoader=t.FontFaceObject=undefined;var n=r(0);function a(e){this.docId=e;this.styleElement=null;this.nativeFontFaces=[];this.loadTestFontId=0;this.loadingContext={requests:[],nextRequestId:0}}a.prototype={insertRule:function e(t){var r=this.styleElement;if(!r){r=this.styleElement=document.createElement("style");r.id="PDFJS_FONT_STYLE_TAG_"+this.docId;document.documentElement.getElementsByTagName("head")[0].appendChild(r)}var n=r.sheet;n.insertRule(t,n.cssRules.length)},clear:function e(){if(this.styleElement){this.styleElement.remove();this.styleElement=null}this.nativeFontFaces.forEach(function(e){document.fonts.delete(e)});this.nativeFontFaces.length=0}};{var i=function e(){return atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQ"+"AABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwA"+"AAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbm"+"FtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAA"+"AADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6A"+"ABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAA"+"MQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAA"+"AAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAA"+"AAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQ"+"AAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMA"+"AQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAA"+"EAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAA"+"AQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAA"+"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"+"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"+"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"+"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAA"+"AAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgc"+"A/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWF"+"hYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQA"+"AAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAg"+"ABAAAAAAAAAAAD6AAAAAAAAA==")};Object.defineProperty(a.prototype,"loadTestFont",{get:function e(){return(0,n.shadow)(this,"loadTestFont",i())},configurable:true});a.prototype.addNativeFontFace=function e(t){this.nativeFontFaces.push(t);document.fonts.add(t)};a.prototype.bind=function e(t,r){var i=[];var s=[];var o=[];var u=function e(t){return t.loaded.catch(function(e){(0,n.warn)('Failed to load font "'+t.family+'": '+e)})};var l=a.isFontLoadingAPISupported&&!a.isSyncFontLoadingSupported;for(var c=0,f=t.length;c<f;c++){var d=t[c];if(d.attached||d.loading===false){continue}d.attached=true;if(l){var h=d.createNativeFontFace();if(h){this.addNativeFontFace(h);o.push(u(h))}}else{var v=d.createFontFaceRule();if(v){this.insertRule(v);i.push(v);s.push(d)}}}var p=this.queueLoadingCallback(r);if(l){Promise.all(o).then(function(){p.complete()})}else if(i.length>0&&!a.isSyncFontLoadingSupported){this.prepareFontLoadEvent(i,s,p)}else{p.complete()}};a.prototype.queueLoadingCallback=function e(t){function r(){(0,n.assert)(!s.end,"completeRequest() cannot be called twice");s.end=Date.now();while(a.requests.length>0&&a.requests[0].end){var e=a.requests.shift();setTimeout(e.callback,0)}}var a=this.loadingContext;var i="pdfjs-font-loading-"+a.nextRequestId++;var s={id:i,complete:r,callback:t,started:Date.now()};a.requests.push(s);return s};a.prototype.prepareFontLoadEvent=function e(t,r,a){function i(e,t){return e.charCodeAt(t)<<24|e.charCodeAt(t+1)<<16|e.charCodeAt(t+2)<<8|e.charCodeAt(t+3)&255}function s(e,t,r,n){var a=e.substr(0,t);var i=e.substr(t+r);return a+n+i}var o,u;var l=document.createElement("canvas");l.width=1;l.height=1;var c=l.getContext("2d");var f=0;function d(e,t){f++;if(f>30){(0,n.warn)("Load test font never loaded.");t();return}c.font="30px "+e;c.fillText(".",0,20);var r=c.getImageData(0,0,1,1);if(r.data[3]>0){t();return}setTimeout(d.bind(null,e,t))}var h="lt"+Date.now()+this.loadTestFontId++;var v=this.loadTestFont;var p=976;v=s(v,p,h.length,h);var m=16;var g=1482184792;var b=i(v,m);for(o=0,u=h.length-3;o<u;o+=4){b=b-g+i(h,o)|0}if(o<h.length){b=b-g+i(h+"XXX",o)|0}v=s(v,m,4,(0,n.string32)(b));var y="url(data:font/opentype;base64,"+btoa(v)+");";var _='@font-face { font-family:"'+h+'";src:'+y+"}";this.insertRule(_);var A=[];for(o=0,u=r.length;o<u;o++){A.push(r[o].loadedName)}A.push(h);var S=document.createElement("div");S.setAttribute("style","visibility: hidden;"+"width: 10px; height: 10px;"+"position: absolute; top: 0px; left: 0px;");for(o=0,u=A.length;o<u;++o){var w=document.createElement("span");w.textContent="Hi";w.style.fontFamily=A[o];S.appendChild(w)}document.body.appendChild(S);d(h,function(){document.body.removeChild(S);a.complete()})}}{a.isFontLoadingAPISupported=typeof document!=="undefined"&&!!document.fonts}{var s=function e(){if(typeof navigator==="undefined"){return true}var t=false;var r=/Mozilla\/5.0.*?rv:(\d+).*? Gecko/.exec(navigator.userAgent);if(r&&r[1]>=14){t=true}return t};Object.defineProperty(a,"isSyncFontLoadingSupported",{get:function e(){return(0,n.shadow)(a,"isSyncFontLoadingSupported",s())},enumerable:true,configurable:true})}var o={get value(){return(0,n.shadow)(this,"value",(0,n.isEvalSupported)())}};var u=function e(){function t(e,t){this.compiledGlyphs=Object.create(null);for(var r in e){this[r]=e[r]}this.options=t}t.prototype={createNativeFontFace:function e(){if(!this.data){return null}if(this.options.disableFontFace){this.disableFontFace=true;return null}var t=new FontFace(this.loadedName,this.data,{});if(this.options.fontRegistry){this.options.fontRegistry.registerFont(this)}return t},createFontFaceRule:function e(){if(!this.data){return null}if(this.options.disableFontFace){this.disableFontFace=true;return null}var t=(0,n.bytesToString)(new Uint8Array(this.data));var r=this.loadedName;var a="url(data:"+this.mimetype+";base64,"+btoa(t)+");";var i='@font-face { font-family:"'+r+'";src:'+a+"}";if(this.options.fontRegistry){this.options.fontRegistry.registerFont(this,a)}return i},getPathGenerator:function e(t,r){if(!(r in this.compiledGlyphs)){var n=t.get(this.loadedName+"_path_"+r);var a,i,s;if(this.options.isEvalSupported&&o.value){var u,l="";for(i=0,s=n.length;i<s;i++){a=n[i];if(a.args!==undefined){u=a.args.join(",")}else{u=""}l+="c."+a.cmd+"("+u+");\n"}this.compiledGlyphs[r]=new Function("c","size",l)}else{this.compiledGlyphs[r]=function(e,t){for(i=0,s=n.length;i<s;i++){a=n[i];if(a.cmd==="scale"){a.args=[t,-t]}e[a.cmd].apply(e,a.args)}}}}return this.compiledGlyphs[r]}};return t}();t.FontFaceObject=u;t.FontLoader=a},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.CanvasGraphics=undefined;var n=r(0);var a=r(121);var i=r(70);var s=16;var o=100;var u=4096;var l=.65;var c=true;var f=1e3;var d=16;var h={get value(){return(0,n.shadow)(h,"value",(0,n.isLittleEndian)())}};function v(e){if(!e.mozCurrentTransform){e._originalSave=e.save;e._originalRestore=e.restore;e._originalRotate=e.rotate;e._originalScale=e.scale;e._originalTranslate=e.translate;e._originalTransform=e.transform;e._originalSetTransform=e.setTransform;e._transformMatrix=e._transformMatrix||[1,0,0,1,0,0];e._transformStack=[];Object.defineProperty(e,"mozCurrentTransform",{get:function e(){return this._transformMatrix}});Object.defineProperty(e,"mozCurrentTransformInverse",{get:function e(){var t=this._transformMatrix;var r=t[0],n=t[1],a=t[2],i=t[3],s=t[4],o=t[5];var u=r*i-n*a;var l=n*a-r*i;return[i/u,n/l,a/l,r/u,(i*s-a*o)/l,(n*s-r*o)/u]}});e.save=function e(){var t=this._transformMatrix;this._transformStack.push(t);this._transformMatrix=t.slice(0,6);this._originalSave()};e.restore=function e(){var t=this._transformStack.pop();if(t){this._transformMatrix=t;this._originalRestore()}};e.translate=function e(t,r){var n=this._transformMatrix;n[4]=n[0]*t+n[2]*r+n[4];n[5]=n[1]*t+n[3]*r+n[5];this._originalTranslate(t,r)};e.scale=function e(t,r){var n=this._transformMatrix;n[0]=n[0]*t;n[1]=n[1]*t;n[2]=n[2]*r;n[3]=n[3]*r;this._originalScale(t,r)};e.transform=function t(r,n,a,i,s,o){var u=this._transformMatrix;this._transformMatrix=[u[0]*r+u[2]*n,u[1]*r+u[3]*n,u[0]*a+u[2]*i,u[1]*a+u[3]*i,u[0]*s+u[2]*o+u[4],u[1]*s+u[3]*o+u[5]];e._originalTransform(r,n,a,i,s,o)};e.setTransform=function t(r,n,a,i,s,o){this._transformMatrix=[r,n,a,i,s,o];e._originalSetTransform(r,n,a,i,s,o)};e.rotate=function e(t){var r=Math.cos(t);var n=Math.sin(t);var a=this._transformMatrix;this._transformMatrix=[a[0]*r+a[2]*n,a[1]*r+a[3]*n,a[0]*-n+a[2]*r,a[1]*-n+a[3]*r,a[4],a[5]];this._originalRotate(t)}}}var p=function e(){function t(e){this.canvasFactory=e;this.cache=Object.create(null)}t.prototype={getCanvas:function e(t,r,n,a){var i;if(this.cache[t]!==undefined){i=this.cache[t];this.canvasFactory.reset(i,r,n);i.context.setTransform(1,0,0,1,0,0)}else{i=this.canvasFactory.create(r,n);this.cache[t]=i}if(a){v(i.context)}return i},clear:function e(){for(var t in this.cache){var r=this.cache[t];this.canvasFactory.destroy(r);delete this.cache[t]}}};return t}();function m(e){var t=1e3;var r=e.width,n=e.height;var a,i,s,o=r+1;var u=new Uint8Array(o*(n+1));var l=new Uint8Array([0,2,4,0,1,0,5,4,8,10,0,8,0,2,1,0]);var c=r+7&~7,f=e.data;var d=new Uint8Array(c*n),h=0,v;for(a=0,v=f.length;a<v;a++){var p=128,m=f[a];while(p>0){d[h++]=m&p?0:255;p>>=1}}var g=0;h=0;if(d[h]!==0){u[0]=1;++g}for(i=1;i<r;i++){if(d[h]!==d[h+1]){u[i]=d[h]?2:1;++g}h++}if(d[h]!==0){u[i]=2;++g}for(a=1;a<n;a++){h=a*c;s=a*o;if(d[h-c]!==d[h]){u[s]=d[h]?1:8;++g}var b=(d[h]?4:0)+(d[h-c]?8:0);for(i=1;i<r;i++){b=(b>>2)+(d[h+1]?4:0)+(d[h-c+1]?8:0);if(l[b]){u[s+i]=l[b];++g}h++}if(d[h-c]!==d[h]){u[s+i]=d[h]?2:4;++g}if(g>t){return null}}h=c*(n-1);s=a*o;if(d[h]!==0){u[s]=8;++g}for(i=1;i<r;i++){if(d[h]!==d[h+1]){u[s+i]=d[h]?4:8;++g}h++}if(d[h]!==0){u[s+i]=4;++g}if(g>t){return null}var y=new Int32Array([0,o,-1,0,-o,0,0,0,1]);var _=[];for(a=0;g&&a<=n;a++){var A=a*o;var S=A+r;while(A<S&&!u[A]){A++}if(A===S){continue}var w=[A%o,a];var P=u[A],k=A,C;do{var R=y[P];do{A+=R}while(!u[A]);C=u[A];if(C!==5&&C!==10){P=C;u[A]=0}else{P=C&51*P>>4;u[A]&=P>>2|P<<2}w.push(A%o);w.push(A/o|0);--g}while(k!==A);_.push(w);--a}var x=function e(t){t.save();t.scale(1/r,-1/n);t.translate(0,-n);t.beginPath();for(var a=0,i=_.length;a<i;a++){var s=_[a];t.moveTo(s[0],s[1]);for(var o=2,u=s.length;o<u;o+=2){t.lineTo(s[o],s[o+1])}}t.fill();t.beginPath();t.restore()};return x}var g=function e(){function t(){this.alphaIsShape=false;this.fontSize=0;this.fontSizeScale=1;this.textMatrix=n.IDENTITY_MATRIX;this.textMatrixScale=1;this.fontMatrix=n.FONT_IDENTITY_MATRIX;this.leading=0;this.x=0;this.y=0;this.lineX=0;this.lineY=0;this.charSpacing=0;this.wordSpacing=0;this.textHScale=1;this.textRenderingMode=n.TextRenderingMode.FILL;this.textRise=0;this.fillColor="#000000";this.strokeColor="#000000";this.patternFill=false;this.fillAlpha=1;this.strokeAlpha=1;this.lineWidth=1;this.activeSMask=null;this.resumeSMaskCtx=null}t.prototype={clone:function e(){return Object.create(this)},setCurrentPoint:function e(t,r){this.x=t;this.y=r}};return t}();var b=function e(){var t=15;var r=10;function b(e,t,r,n,a){this.ctx=e;this.current=new g;this.stateStack=[];this.pendingClip=null;this.pendingEOFill=false;this.res=null;this.xobjs=null;this.commonObjs=t;this.objs=r;this.canvasFactory=n;this.imageLayer=a;this.groupStack=[];this.processingType3=null;this.baseTransform=null;this.baseTransformStack=[];this.groupLevel=0;this.smaskStack=[];this.smaskCounter=0;this.tempSMask=null;this.cachedCanvases=new p(this.canvasFactory);if(e){v(e)}this.cachedGetSinglePixelWidth=null}function y(e,t){if(typeof ImageData!=="undefined"&&t instanceof ImageData){e.putImageData(t,0,0);return}var r=t.height,a=t.width;var i=r%d;var s=(r-i)/d;var o=i===0?s:s+1;var u=e.createImageData(a,d);var l=0,c;var f=t.data;var v=u.data;var p,m,g,b;if(t.kind===n.ImageKind.GRAYSCALE_1BPP){var y=f.byteLength;var _=new Uint32Array(v.buffer,0,v.byteLength>>2);var A=_.length;var S=a+7>>3;var w=4294967295;var P=h.value?4278190080:255;for(p=0;p<o;p++){g=p<s?d:i;c=0;for(m=0;m<g;m++){var k=y-l;var C=0;var R=k>S?a:k*8-7;var x=R&~7;var T=0;var E=0;for(;C<x;C+=8){E=f[l++];_[c++]=E&128?w:P;_[c++]=E&64?w:P;_[c++]=E&32?w:P;_[c++]=E&16?w:P;_[c++]=E&8?w:P;_[c++]=E&4?w:P;_[c++]=E&2?w:P;_[c++]=E&1?w:P}for(;C<R;C++){if(T===0){E=f[l++];T=128}_[c++]=E&T?w:P;T>>=1}}while(c<A){_[c++]=0}e.putImageData(u,0,p*d)}}else if(t.kind===n.ImageKind.RGBA_32BPP){m=0;b=a*d*4;for(p=0;p<s;p++){v.set(f.subarray(l,l+b));l+=b;e.putImageData(u,0,m);m+=d}if(p<o){b=a*i*4;v.set(f.subarray(l,l+b));e.putImageData(u,0,m)}}else if(t.kind===n.ImageKind.RGB_24BPP){g=d;b=a*g;for(p=0;p<o;p++){if(p>=s){g=i;b=a*g}c=0;for(m=b;m--;){v[c++]=f[l++];v[c++]=f[l++];v[c++]=f[l++];v[c++]=255}e.putImageData(u,0,p*d)}}else{throw new Error("bad image kind: "+t.kind)}}function _(e,t){var r=t.height,n=t.width;var a=r%d;var i=(r-a)/d;var s=a===0?i:i+1;var o=e.createImageData(n,d);var u=0;var l=t.data;var c=o.data;for(var f=0;f<s;f++){var h=f<i?d:a;var v=3;for(var p=0;p<h;p++){var m=0;for(var g=0;g<n;g++){if(!m){var b=l[u++];m=128}c[v]=b&m?0:255;v+=4;m>>=1}}e.putImageData(o,0,f*d)}}function A(e,t){var r=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font"];for(var n=0,a=r.length;n<a;n++){var i=r[n];if(e[i]!==undefined){t[i]=e[i]}}if(e.setLineDash!==undefined){t.setLineDash(e.getLineDash());t.lineDashOffset=e.lineDashOffset}}function S(e){e.strokeStyle="#000000";e.fillStyle="#000000";e.fillRule="nonzero";e.globalAlpha=1;e.lineWidth=1;e.lineCap="butt";e.lineJoin="miter";e.miterLimit=10;e.globalCompositeOperation="source-over";e.font="10px sans-serif";if(e.setLineDash!==undefined){e.setLineDash([]);e.lineDashOffset=0}}function w(e,t,r,n){var a=e.length;for(var i=3;i<a;i+=4){var s=e[i];if(s===0){e[i-3]=t;e[i-2]=r;e[i-1]=n}else if(s<255){var o=255-s;e[i-3]=e[i-3]*s+t*o>>8;e[i-2]=e[i-2]*s+r*o>>8;e[i-1]=e[i-1]*s+n*o>>8}}}function P(e,t,r){var n=e.length;var a=1/255;for(var i=3;i<n;i+=4){var s=r?r[e[i]]:e[i];t[i]=t[i]*s*a|0}}function k(e,t,r){var n=e.length;for(var a=3;a<n;a+=4){var i=e[a-3]*77+e[a-2]*152+e[a-1]*28;t[a]=r?t[a]*r[i>>8]>>8:t[a]*i>>16}}function C(e,t,r,n,a,i,s){var o=!!i;var u=o?i[0]:0;var l=o?i[1]:0;var c=o?i[2]:0;var f;if(a==="Luminosity"){f=k}else{f=P}var d=1048576;var h=Math.min(n,Math.ceil(d/r));for(var v=0;v<n;v+=h){var p=Math.min(h,n-v);var m=e.getImageData(0,v,r,p);var g=t.getImageData(0,v,r,p);if(o){w(m.data,u,l,c)}f(m.data,g.data,s);e.putImageData(g,0,v)}}function R(e,t,r){var n=t.canvas;var a=t.context;e.setTransform(t.scaleX,0,0,t.scaleY,t.offsetX,t.offsetY);var s=t.backdrop||null;if(!t.transferMap&&i.WebGLUtils.isEnabled){var o=i.WebGLUtils.composeSMask(r.canvas,n,{subtype:t.subtype,backdrop:s});e.setTransform(1,0,0,1,0,0);e.drawImage(o,t.offsetX,t.offsetY);return}C(a,r,n.width,n.height,t.subtype,s,t.transferMap);e.drawImage(n,0,0)}var x=["butt","round","square"];var T=["miter","round","bevel"];var E={};var O={};b.prototype={beginDrawing:function e(t){var r=t.transform,n=t.viewport,a=t.transparency,i=t.background,s=i===undefined?null:i;var o=this.ctx.canvas.width;var u=this.ctx.canvas.height;this.ctx.save();this.ctx.fillStyle=s||"rgb(255, 255, 255)";this.ctx.fillRect(0,0,o,u);this.ctx.restore();if(a){var l=this.cachedCanvases.getCanvas("transparent",o,u,true);this.compositeCtx=this.ctx;this.transparentCanvas=l.canvas;this.ctx=l.context;this.ctx.save();this.ctx.transform.apply(this.ctx,this.compositeCtx.mozCurrentTransform)}this.ctx.save();S(this.ctx);if(r){this.ctx.transform.apply(this.ctx,r)}this.ctx.transform.apply(this.ctx,n.transform);this.baseTransform=this.ctx.mozCurrentTransform.slice();if(this.imageLayer){this.imageLayer.beginLayout()}},executeOperatorList:function e(a,i,s,o){var u=a.argsArray;var l=a.fnArray;var c=i||0;var f=u.length;if(f===c){return c}var d=f-c>r&&typeof s==="function";var h=d?Date.now()+t:0;var v=0;var p=this.commonObjs;var m=this.objs;var g;while(true){if(o!==undefined&&c===o.nextBreakPoint){o.breakIt(c,s);return c}g=l[c];if(g!==n.OPS.dependency){this[g].apply(this,u[c])}else{var b=u[c];for(var y=0,_=b.length;y<_;y++){var A=b[y];var S=A[0]==="g"&&A[1]==="_";var w=S?p:m;if(!w.isResolved(A)){w.get(A,s);return c}}}c++;if(c===f){return c}if(d&&++v>r){if(Date.now()>h){s();return c}v=0}}},endDrawing:function e(){if(this.current.activeSMask!==null){this.endSMaskGroup()}this.ctx.restore();if(this.transparentCanvas){this.ctx=this.compositeCtx;this.ctx.save();this.ctx.setTransform(1,0,0,1,0,0);this.ctx.drawImage(this.transparentCanvas,0,0);this.ctx.restore();this.transparentCanvas=null}this.cachedCanvases.clear();i.WebGLUtils.clear();if(this.imageLayer){this.imageLayer.endLayout()}},setLineWidth:function e(t){this.current.lineWidth=t;this.ctx.lineWidth=t},setLineCap:function e(t){this.ctx.lineCap=x[t]},setLineJoin:function e(t){this.ctx.lineJoin=T[t]},setMiterLimit:function e(t){this.ctx.miterLimit=t},setDash:function e(t,r){var n=this.ctx;if(n.setLineDash!==undefined){n.setLineDash(t);n.lineDashOffset=r}},setRenderingIntent:function e(t){},setFlatness:function e(t){},setGState:function e(t){for(var r=0,n=t.length;r<n;r++){var a=t[r];var i=a[0];var s=a[1];switch(i){case"LW":this.setLineWidth(s);break;case"LC":this.setLineCap(s);break;case"LJ":this.setLineJoin(s);break;case"ML":this.setMiterLimit(s);break;case"D":this.setDash(s[0],s[1]);break;case"RI":this.setRenderingIntent(s);break;case"FL":this.setFlatness(s);break;case"Font":this.setFont(s[0],s[1]);break;case"CA":this.current.strokeAlpha=a[1];break;case"ca":this.current.fillAlpha=a[1];this.ctx.globalAlpha=a[1];break;case"BM":this.ctx.globalCompositeOperation=s;break;case"SMask":if(this.current.activeSMask){if(this.stateStack.length>0&&this.stateStack[this.stateStack.length-1].activeSMask===this.current.activeSMask){this.suspendSMaskGroup()}else{this.endSMaskGroup()}}this.current.activeSMask=s?this.tempSMask:null;if(this.current.activeSMask){this.beginSMaskGroup()}this.tempSMask=null;break}}},beginSMaskGroup:function e(){var t=this.current.activeSMask;var r=t.canvas.width;var n=t.canvas.height;var a="smaskGroupAt"+this.groupLevel;var i=this.cachedCanvases.getCanvas(a,r,n,true);var s=this.ctx;var o=s.mozCurrentTransform;this.ctx.save();var u=i.context;u.scale(1/t.scaleX,1/t.scaleY);u.translate(-t.offsetX,-t.offsetY);u.transform.apply(u,o);t.startTransformInverse=u.mozCurrentTransformInverse;A(s,u);this.ctx=u;this.setGState([["BM","source-over"],["ca",1],["CA",1]]);this.groupStack.push(s);this.groupLevel++},suspendSMaskGroup:function e(){var t=this.ctx;this.groupLevel--;this.ctx=this.groupStack.pop();R(this.ctx,this.current.activeSMask,t);this.ctx.restore();this.ctx.save();A(t,this.ctx);this.current.resumeSMaskCtx=t;var r=n.Util.transform(this.current.activeSMask.startTransformInverse,t.mozCurrentTransform);this.ctx.transform.apply(this.ctx,r);t.save();t.setTransform(1,0,0,1,0,0);t.clearRect(0,0,t.canvas.width,t.canvas.height);t.restore()},resumeSMaskGroup:function e(){var t=this.current.resumeSMaskCtx;var r=this.ctx;this.ctx=t;this.groupStack.push(r);this.groupLevel++},endSMaskGroup:function e(){var t=this.ctx;this.groupLevel--;this.ctx=this.groupStack.pop();R(this.ctx,this.current.activeSMask,t);this.ctx.restore();A(t,this.ctx);var r=n.Util.transform(this.current.activeSMask.startTransformInverse,t.mozCurrentTransform);this.ctx.transform.apply(this.ctx,r)},save:function e(){this.ctx.save();var t=this.current;this.stateStack.push(t);this.current=t.clone();this.current.resumeSMaskCtx=null},restore:function e(){if(this.current.resumeSMaskCtx){this.resumeSMaskGroup()}if(this.current.activeSMask!==null&&(this.stateStack.length===0||this.stateStack[this.stateStack.length-1].activeSMask!==this.current.activeSMask)){this.endSMaskGroup()}if(this.stateStack.length!==0){this.current=this.stateStack.pop();this.ctx.restore();this.pendingClip=null;this.cachedGetSinglePixelWidth=null}},transform:function e(t,r,n,a,i,s){this.ctx.transform(t,r,n,a,i,s);this.cachedGetSinglePixelWidth=null},constructPath:function e(t,r){var a=this.ctx;var i=this.current;var s=i.x,o=i.y;for(var u=0,l=0,c=t.length;u<c;u++){switch(t[u]|0){case n.OPS.rectangle:s=r[l++];o=r[l++];var f=r[l++];var d=r[l++];if(f===0){f=this.getSinglePixelWidth()}if(d===0){d=this.getSinglePixelWidth()}var h=s+f;var v=o+d;this.ctx.moveTo(s,o);this.ctx.lineTo(h,o);this.ctx.lineTo(h,v);this.ctx.lineTo(s,v);this.ctx.lineTo(s,o);this.ctx.closePath();break;case n.OPS.moveTo:s=r[l++];o=r[l++];a.moveTo(s,o);break;case n.OPS.lineTo:s=r[l++];o=r[l++];a.lineTo(s,o);break;case n.OPS.curveTo:s=r[l+4];o=r[l+5];a.bezierCurveTo(r[l],r[l+1],r[l+2],r[l+3],s,o);l+=6;break;case n.OPS.curveTo2:a.bezierCurveTo(s,o,r[l],r[l+1],r[l+2],r[l+3]);s=r[l+2];o=r[l+3];l+=4;break;case n.OPS.curveTo3:s=r[l+2];o=r[l+3];a.bezierCurveTo(r[l],r[l+1],s,o,s,o);l+=4;break;case n.OPS.closePath:a.closePath();break}}i.setCurrentPoint(s,o)},closePath:function e(){this.ctx.closePath()},stroke:function e(t){t=typeof t!=="undefined"?t:true;var r=this.ctx;var n=this.current.strokeColor;r.lineWidth=Math.max(this.getSinglePixelWidth()*l,this.current.lineWidth);r.globalAlpha=this.current.strokeAlpha;if(n&&n.hasOwnProperty("type")&&n.type==="Pattern"){r.save();r.strokeStyle=n.getPattern(r,this);r.stroke();r.restore()}else{r.stroke()}if(t){this.consumePath()}r.globalAlpha=this.current.fillAlpha},closeStroke:function e(){this.closePath();this.stroke()},fill:function e(t){t=typeof t!=="undefined"?t:true;var r=this.ctx;var n=this.current.fillColor;var a=this.current.patternFill;var i=false;if(a){r.save();if(this.baseTransform){r.setTransform.apply(r,this.baseTransform)}r.fillStyle=n.getPattern(r,this);i=true}if(this.pendingEOFill){r.fill("evenodd");this.pendingEOFill=false}else{r.fill()}if(i){r.restore()}if(t){this.consumePath()}},eoFill:function e(){this.pendingEOFill=true;this.fill()},fillStroke:function e(){this.fill(false);this.stroke(false);this.consumePath()},eoFillStroke:function e(){this.pendingEOFill=true;this.fillStroke()},closeFillStroke:function e(){this.closePath();this.fillStroke()},closeEOFillStroke:function e(){this.pendingEOFill=true;this.closePath();this.fillStroke()},endPath:function e(){this.consumePath()},clip:function e(){this.pendingClip=E},eoClip:function e(){this.pendingClip=O},beginText:function e(){this.current.textMatrix=n.IDENTITY_MATRIX;this.current.textMatrixScale=1;this.current.x=this.current.lineX=0;this.current.y=this.current.lineY=0},endText:function e(){var t=this.pendingTextPaths;var r=this.ctx;if(t===undefined){r.beginPath();return}r.save();r.beginPath();for(var n=0;n<t.length;n++){var a=t[n];r.setTransform.apply(r,a.transform);r.translate(a.x,a.y);a.addToPath(r,a.fontSize)}r.restore();r.clip();r.beginPath();delete this.pendingTextPaths},setCharSpacing:function e(t){this.current.charSpacing=t},setWordSpacing:function e(t){this.current.wordSpacing=t},setHScale:function e(t){this.current.textHScale=t/100},setLeading:function e(t){this.current.leading=-t},setFont:function e(t,r){var a=this.commonObjs.get(t);var i=this.current;if(!a){throw new Error("Can't find font for "+t)}i.fontMatrix=a.fontMatrix?a.fontMatrix:n.FONT_IDENTITY_MATRIX;if(i.fontMatrix[0]===0||i.fontMatrix[3]===0){(0,n.warn)("Invalid font matrix for font "+t)}if(r<0){r=-r;i.fontDirection=-1}else{i.fontDirection=1}this.current.font=a;this.current.fontSize=r;if(a.isType3Font){return}var u=a.loadedName||"sans-serif";var l=a.black?"900":a.bold?"bold":"normal";var c=a.italic?"italic":"normal";var f='"'+u+'", '+a.fallbackName;var d=r<s?s:r>o?o:r;this.current.fontSizeScale=r/d;var h=c+" "+l+" "+d+"px "+f;this.ctx.font=h},setTextRenderingMode:function e(t){this.current.textRenderingMode=t},setTextRise:function e(t){this.current.textRise=t},moveText:function e(t,r){this.current.x=this.current.lineX+=t;this.current.y=this.current.lineY+=r},setLeadingMoveText:function e(t,r){this.setLeading(-r);this.moveText(t,r)},setTextMatrix:function e(t,r,n,a,i,s){this.current.textMatrix=[t,r,n,a,i,s];this.current.textMatrixScale=Math.sqrt(t*t+r*r);this.current.x=this.current.lineX=0;this.current.y=this.current.lineY=0},nextLine:function e(){this.moveText(0,this.current.leading)},paintChar:function e(t,r,a){var i=this.ctx;var s=this.current;var o=s.font;var u=s.textRenderingMode;var l=s.fontSize/s.fontSizeScale;var c=u&n.TextRenderingMode.FILL_STROKE_MASK;var f=!!(u&n.TextRenderingMode.ADD_TO_PATH_FLAG);var d;if(o.disableFontFace||f){d=o.getPathGenerator(this.commonObjs,t)}if(o.disableFontFace){i.save();i.translate(r,a);i.beginPath();d(i,l);if(c===n.TextRenderingMode.FILL||c===n.TextRenderingMode.FILL_STROKE){i.fill()}if(c===n.TextRenderingMode.STROKE||c===n.TextRenderingMode.FILL_STROKE){i.stroke()}i.restore()}else{if(c===n.TextRenderingMode.FILL||c===n.TextRenderingMode.FILL_STROKE){i.fillText(t,r,a)}if(c===n.TextRenderingMode.STROKE||c===n.TextRenderingMode.FILL_STROKE){i.strokeText(t,r,a)}}if(f){var h=this.pendingTextPaths||(this.pendingTextPaths=[]);h.push({transform:i.mozCurrentTransform,x:r,y:a,fontSize:l,addToPath:d})}},get isFontSubpixelAAEnabled(){var e=this.canvasFactory.create(10,10).context;e.scale(1.5,1);e.fillText("I",0,10);var t=e.getImageData(0,0,10,10).data;var r=false;for(var a=3;a<t.length;a+=4){if(t[a]>0&&t[a]<255){r=true;break}}return(0,n.shadow)(this,"isFontSubpixelAAEnabled",r)},showText:function e(t){var r=this.current;var a=r.font;if(a.isType3Font){return this.showType3Text(t)}var i=r.fontSize;if(i===0){return}var s=this.ctx;var o=r.fontSizeScale;var u=r.charSpacing;var c=r.wordSpacing;var f=r.fontDirection;var d=r.textHScale*f;var h=t.length;var v=a.vertical;var p=v?1:-1;var m=a.defaultVMetrics;var g=i*r.fontMatrix[0];var b=r.textRenderingMode===n.TextRenderingMode.FILL&&!a.disableFontFace;s.save();s.transform.apply(s,r.textMatrix);s.translate(r.x,r.y+r.textRise);if(r.patternFill){s.fillStyle=r.fillColor.getPattern(s,this)}if(f>0){s.scale(d,-1)}else{s.scale(d,1)}var y=r.lineWidth;var _=r.textMatrixScale;if(_===0||y===0){var A=r.textRenderingMode&n.TextRenderingMode.FILL_STROKE_MASK;if(A===n.TextRenderingMode.STROKE||A===n.TextRenderingMode.FILL_STROKE){this.cachedGetSinglePixelWidth=null;y=this.getSinglePixelWidth()*l}}else{y/=_}if(o!==1){s.scale(o,o);y/=o}s.lineWidth=y;var S=0,w;for(w=0;w<h;++w){var P=t[w];if((0,n.isNum)(P)){S+=p*P*i/1e3;continue}var k=false;var C=(P.isSpace?c:0)+u;var R=P.fontChar;var x=P.accent;var T,E,O,L;var I=P.width;if(v){var j,F,D;j=P.vmetric||m;F=P.vmetric?j[1]:I*.5;F=-F*g;D=j[2]*g;I=j?-j[0]:I;T=F/o;E=(S+D)/o}else{T=S/o;E=0}if(a.remeasure&&I>0){var M=s.measureText(R).width*1e3/i*o;if(I<M&&this.isFontSubpixelAAEnabled){var N=I/M;k=true;s.save();s.scale(N,1);T/=N}else if(I!==M){T+=(I-M)/2e3*i/o}}if(P.isInFont||a.missingFile){if(b&&!x){s.fillText(R,T,E)}else{this.paintChar(R,T,E);if(x){O=T+x.offset.x/o;L=E-x.offset.y/o;this.paintChar(x.fontChar,O,L)}}}var q=I*g+C*f;S+=q;if(k){s.restore()}}if(v){r.y-=S*d}else{r.x+=S*d}s.restore()},showType3Text:function e(t){var r=this.ctx;var a=this.current;var i=a.font;var s=a.fontSize;var o=a.fontDirection;var u=i.vertical?1:-1;var l=a.charSpacing;var c=a.wordSpacing;var f=a.textHScale*o;var d=a.fontMatrix||n.FONT_IDENTITY_MATRIX;var h=t.length;var v=a.textRenderingMode===n.TextRenderingMode.INVISIBLE;var p,m,g,b;if(v||s===0){return}this.cachedGetSinglePixelWidth=null;r.save();r.transform.apply(r,a.textMatrix);r.translate(a.x,a.y);r.scale(f,o);for(p=0;p<h;++p){m=t[p];if((0,n.isNum)(m)){b=u*m*s/1e3;this.ctx.translate(b,0);a.x+=b*f;continue}var y=(m.isSpace?c:0)+l;var _=i.charProcOperatorList[m.operatorListId];if(!_){(0,n.warn)('Type3 character "'+m.operatorListId+'" is not available.');continue}this.processingType3=m;this.save();r.scale(s,s);r.transform.apply(r,d);this.executeOperatorList(_);this.restore();var A=n.Util.applyTransform([m.width,0],d);g=A[0]*s+y;r.translate(g,0);a.x+=g*f}r.restore();this.processingType3=null},setCharWidth:function e(t,r){},setCharWidthAndBounds:function e(t,r,n,a,i,s){this.ctx.rect(n,a,i-n,s-a);this.clip();this.endPath()},getColorN_Pattern:function e(t){var r=this;var n;if(t[0]==="TilingPattern"){var i=t[1];var s=this.baseTransform||this.ctx.mozCurrentTransform.slice();var o={createCanvasGraphics:function e(t){return new b(t,r.commonObjs,r.objs,r.canvasFactory)}};n=new a.TilingPattern(t,i,this.ctx,o,s)}else{n=(0,a.getShadingPatternFromIR)(t)}return n},setStrokeColorN:function e(){this.current.strokeColor=this.getColorN_Pattern(arguments)},setFillColorN:function e(){this.current.fillColor=this.getColorN_Pattern(arguments);this.current.patternFill=true},setStrokeRGBColor:function e(t,r,a){var i=n.Util.makeCssRgb(t,r,a);this.ctx.strokeStyle=i;this.current.strokeColor=i},setFillRGBColor:function e(t,r,a){var i=n.Util.makeCssRgb(t,r,a);this.ctx.fillStyle=i;this.current.fillColor=i;this.current.patternFill=false},shadingFill:function e(t){var r=this.ctx;this.save();var i=(0,a.getShadingPatternFromIR)(t);r.fillStyle=i.getPattern(r,this,true);var s=r.mozCurrentTransformInverse;if(s){var o=r.canvas;var u=o.width;var l=o.height;var c=n.Util.applyTransform([0,0],s);var f=n.Util.applyTransform([0,l],s);var d=n.Util.applyTransform([u,0],s);var h=n.Util.applyTransform([u,l],s);var v=Math.min(c[0],f[0],d[0],h[0]);var p=Math.min(c[1],f[1],d[1],h[1]);var m=Math.max(c[0],f[0],d[0],h[0]);var g=Math.max(c[1],f[1],d[1],h[1]);this.ctx.fillRect(v,p,m-v,g-p)}else{this.ctx.fillRect(-1e10,-1e10,2e10,2e10)}this.restore()},beginInlineImage:function e(){throw new Error("Should not call beginInlineImage")},beginImageData:function e(){throw new Error("Should not call beginImageData")},paintFormXObjectBegin:function e(t,r){this.save();this.baseTransformStack.push(this.baseTransform);if(Array.isArray(t)&&t.length===6){this.transform.apply(this,t)}this.baseTransform=this.ctx.mozCurrentTransform;if(Array.isArray(r)&&r.length===4){var n=r[2]-r[0];var a=r[3]-r[1];this.ctx.rect(r[0],r[1],n,a);this.clip();this.endPath()}},paintFormXObjectEnd:function e(){this.restore();this.baseTransform=this.baseTransformStack.pop()},beginGroup:function e(t){this.save();var r=this.ctx;if(!t.isolated){(0,n.info)("TODO: Support non-isolated groups.")}if(t.knockout){(0,n.warn)("Knockout groups not supported.")}var a=r.mozCurrentTransform;if(t.matrix){r.transform.apply(r,t.matrix)}if(!t.bbox){throw new Error("Bounding box is required.")}var i=n.Util.getAxialAlignedBoundingBox(t.bbox,r.mozCurrentTransform);var s=[0,0,r.canvas.width,r.canvas.height];i=n.Util.intersect(i,s)||[0,0,0,0];var o=Math.floor(i[0]);var l=Math.floor(i[1]);var c=Math.max(Math.ceil(i[2])-o,1);var f=Math.max(Math.ceil(i[3])-l,1);var d=1,h=1;if(c>u){d=c/u;c=u}if(f>u){h=f/u;f=u}var v="groupAt"+this.groupLevel;if(t.smask){v+="_smask_"+this.smaskCounter++%2}var p=this.cachedCanvases.getCanvas(v,c,f,true);var m=p.context;m.scale(1/d,1/h);m.translate(-o,-l);m.transform.apply(m,a);if(t.smask){this.smaskStack.push({canvas:p.canvas,context:m,offsetX:o,offsetY:l,scaleX:d,scaleY:h,subtype:t.smask.subtype,backdrop:t.smask.backdrop,transferMap:t.smask.transferMap||null,startTransformInverse:null})}else{r.setTransform(1,0,0,1,0,0);r.translate(o,l);r.scale(d,h)}A(r,m);this.ctx=m;this.setGState([["BM","source-over"],["ca",1],["CA",1]]);this.groupStack.push(r);this.groupLevel++;this.current.activeSMask=null},endGroup:function e(t){this.groupLevel--;var r=this.ctx;this.ctx=this.groupStack.pop();if(this.ctx.imageSmoothingEnabled!==undefined){this.ctx.imageSmoothingEnabled=false}else{this.ctx.mozImageSmoothingEnabled=false}if(t.smask){this.tempSMask=this.smaskStack.pop()}else{this.ctx.drawImage(r.canvas,0,0)}this.restore()},beginAnnotations:function e(){this.save();if(this.baseTransform){this.ctx.setTransform.apply(this.ctx,this.baseTransform)}},endAnnotations:function e(){this.restore()},beginAnnotation:function e(t,r,n){this.save();S(this.ctx);this.current=new g;if(Array.isArray(t)&&t.length===4){var a=t[2]-t[0];var i=t[3]-t[1];this.ctx.rect(t[0],t[1],a,i);this.clip();this.endPath()}this.transform.apply(this,r);this.transform.apply(this,n)},endAnnotation:function e(){this.restore()},paintJpegXObject:function e(t,r,a){var i=this.objs.get(t);if(!i){(0,n.warn)("Dependent image isn't ready yet");return}this.save();var s=this.ctx;s.scale(1/r,-1/a);s.drawImage(i,0,0,i.width,i.height,0,-a,r,a);if(this.imageLayer){var o=s.mozCurrentTransformInverse;var u=this.getCanvasPosition(0,0);this.imageLayer.appendImage({objId:t,left:u[0],top:u[1],width:r/o[0],height:a/o[3]})}this.restore()},paintImageMaskXObject:function e(t){var r=this.ctx;var n=t.width,a=t.height;var i=this.current.fillColor;var s=this.current.patternFill;var o=this.processingType3;if(c&&o&&o.compiled===undefined){if(n<=f&&a<=f){o.compiled=m({data:t.data,width:n,height:a})}else{o.compiled=null}}if(o&&o.compiled){o.compiled(r);return}var u=this.cachedCanvases.getCanvas("maskCanvas",n,a);var l=u.context;l.save();_(l,t);l.globalCompositeOperation="source-in";l.fillStyle=s?i.getPattern(l,this):i;l.fillRect(0,0,n,a);l.restore();this.paintInlineImageXObject(u.canvas)},paintImageMaskXObjectRepeat:function e(t,r,n,a){var i=t.width;var s=t.height;var o=this.current.fillColor;var u=this.current.patternFill;var l=this.cachedCanvases.getCanvas("maskCanvas",i,s);var c=l.context;c.save();_(c,t);c.globalCompositeOperation="source-in";c.fillStyle=u?o.getPattern(c,this):o;c.fillRect(0,0,i,s);c.restore();var f=this.ctx;for(var d=0,h=a.length;d<h;d+=2){f.save();f.transform(r,0,0,n,a[d],a[d+1]);f.scale(1,-1);f.drawImage(l.canvas,0,0,i,s,0,-1,1,1);f.restore()}},paintImageMaskXObjectGroup:function e(t){var r=this.ctx;var n=this.current.fillColor;var a=this.current.patternFill;for(var i=0,s=t.length;i<s;i++){var o=t[i];var u=o.width,l=o.height;var c=this.cachedCanvases.getCanvas("maskCanvas",u,l);var f=c.context;f.save();_(f,o);f.globalCompositeOperation="source-in";f.fillStyle=a?n.getPattern(f,this):n;f.fillRect(0,0,u,l);f.restore();r.save();r.transform.apply(r,o.transform);r.scale(1,-1);r.drawImage(c.canvas,0,0,u,l,0,-1,1,1);r.restore()}},paintImageXObject:function e(t){var r=this.objs.get(t);if(!r){(0,n.warn)("Dependent image isn't ready yet");return}this.paintInlineImageXObject(r)},paintImageXObjectRepeat:function e(t,r,a,i){var s=this.objs.get(t);if(!s){(0,n.warn)("Dependent image isn't ready yet");return}var o=s.width;var u=s.height;var l=[];for(var c=0,f=i.length;c<f;c+=2){l.push({transform:[r,0,0,a,i[c],i[c+1]],x:0,y:0,w:o,h:u})}this.paintInlineImageXObjectGroup(s,l)},paintInlineImageXObject:function e(t){var r=t.width;var n=t.height;var a=this.ctx;this.save();a.scale(1/r,-1/n);var i=a.mozCurrentTransformInverse;var s=i[0],o=i[1];var u=Math.max(Math.sqrt(s*s+o*o),1);var l=i[2],c=i[3];var f=Math.max(Math.sqrt(l*l+c*c),1);var d,h;if(t instanceof HTMLElement||!t.data){d=t}else{h=this.cachedCanvases.getCanvas("inlineImage",r,n);var v=h.context;y(v,t);d=h.canvas}var p=r,m=n;var g="prescale1";while(u>2&&p>1||f>2&&m>1){var b=p,_=m;if(u>2&&p>1){b=Math.ceil(p/2);u/=p/b}if(f>2&&m>1){_=Math.ceil(m/2);f/=m/_}h=this.cachedCanvases.getCanvas(g,b,_);v=h.context;v.clearRect(0,0,b,_);v.drawImage(d,0,0,p,m,0,0,b,_);d=h.canvas;p=b;m=_;g=g==="prescale1"?"prescale2":"prescale1"}a.drawImage(d,0,0,p,m,0,-n,r,n);if(this.imageLayer){var A=this.getCanvasPosition(0,-n);this.imageLayer.appendImage({imgData:t,left:A[0],top:A[1],width:r/i[0],height:n/i[3]})}this.restore()},paintInlineImageXObjectGroup:function e(t,r){var n=this.ctx;var a=t.width;var i=t.height;var s=this.cachedCanvases.getCanvas("inlineImage",a,i);var o=s.context;y(o,t);for(var u=0,l=r.length;u<l;u++){var c=r[u];n.save();n.transform.apply(n,c.transform);n.scale(1,-1);n.drawImage(s.canvas,c.x,c.y,c.w,c.h,0,-1,1,1);if(this.imageLayer){var f=this.getCanvasPosition(c.x,c.y);this.imageLayer.appendImage({imgData:t,left:f[0],top:f[1],width:a,height:i})}n.restore()}},paintSolidColorImageMask:function e(){this.ctx.fillRect(0,0,1,1)},paintXObject:function e(){(0,n.warn)("Unsupported 'paintXObject' command.")},markPoint:function e(t){},markPointProps:function e(t,r){},beginMarkedContent:function e(t){},beginMarkedContentProps:function e(t,r){},endMarkedContent:function e(){},beginCompat:function e(){},endCompat:function e(){},consumePath:function e(){var t=this.ctx;if(this.pendingClip){if(this.pendingClip===O){t.clip("evenodd")}else{t.clip()}this.pendingClip=null}t.beginPath()},getSinglePixelWidth:function e(t){if(this.cachedGetSinglePixelWidth===null){this.ctx.save();var r=this.ctx.mozCurrentTransformInverse;this.ctx.restore();this.cachedGetSinglePixelWidth=Math.sqrt(Math.max(r[0]*r[0]+r[1]*r[1],r[2]*r[2]+r[3]*r[3]))}return this.cachedGetSinglePixelWidth},getCanvasPosition:function e(t,r){var n=this.ctx.mozCurrentTransform;return[n[0]*t+n[2]*r+n[4],n[1]*t+n[3]*r+n[5]]}};for(var L in n.OPS){b.prototype[n.OPS[L]]=b.prototype[L]}return b}();t.CanvasGraphics=b},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.TilingPattern=t.getShadingPatternFromIR=undefined;var n=r(0);var a=r(70);var i={};i.RadialAxial={fromIR:function e(t){var r=t[1];var n=t[2];var a=t[3];var i=t[4];var s=t[5];var o=t[6];return{type:"Pattern",getPattern:function e(t){var u;if(r==="axial"){u=t.createLinearGradient(a[0],a[1],i[0],i[1])}else if(r==="radial"){u=t.createRadialGradient(a[0],a[1],s,i[0],i[1],o)}for(var l=0,c=n.length;l<c;++l){var f=n[l];u.addColorStop(f[0],f[1])}return u}}}};var s=function e(){function t(e,t,r,n,a,i,s,o){var u=t.coords,l=t.colors;var c=e.data,f=e.width*4;var d;if(u[r+1]>u[n+1]){d=r;r=n;n=d;d=i;i=s;s=d}if(u[n+1]>u[a+1]){d=n;n=a;a=d;d=s;s=o;o=d}if(u[r+1]>u[n+1]){d=r;r=n;n=d;d=i;i=s;s=d}var h=(u[r]+t.offsetX)*t.scaleX;var v=(u[r+1]+t.offsetY)*t.scaleY;var p=(u[n]+t.offsetX)*t.scaleX;var m=(u[n+1]+t.offsetY)*t.scaleY;var g=(u[a]+t.offsetX)*t.scaleX;var b=(u[a+1]+t.offsetY)*t.scaleY;if(v>=b){return}var y=l[i],_=l[i+1],A=l[i+2];var S=l[s],w=l[s+1],P=l[s+2];var k=l[o],C=l[o+1],R=l[o+2];var x=Math.round(v),T=Math.round(b);var E,O,L,I;var j,F,D,M;var N;for(var q=x;q<=T;q++){if(q<m){N=q<v?0:v===m?1:(v-q)/(v-m);E=h-(h-p)*N;O=y-(y-S)*N;L=_-(_-w)*N;I=A-(A-P)*N}else{N=q>b?1:m===b?0:(m-q)/(m-b);E=p-(p-g)*N;O=S-(S-k)*N;L=w-(w-C)*N;I=P-(P-R)*N}N=q<v?0:q>b?1:(v-q)/(v-b);j=h-(h-g)*N;F=y-(y-k)*N;D=_-(_-C)*N;M=A-(A-R)*N;var U=Math.round(Math.min(E,j));var W=Math.round(Math.max(E,j));var B=f*q+U*4;for(var z=U;z<=W;z++){N=(E-z)/(E-j);N=N<0?0:N>1?1:N;c[B++]=O-(O-F)*N|0;c[B++]=L-(L-D)*N|0;c[B++]=I-(I-M)*N|0;c[B++]=255}}}function r(e,r,n){var a=r.coords;var i=r.colors;var s,o;switch(r.type){case"lattice":var u=r.verticesPerRow;var l=Math.floor(a.length/u)-1;var c=u-1;for(s=0;s<l;s++){var f=s*u;for(var d=0;d<c;d++,f++){t(e,n,a[f],a[f+1],a[f+u],i[f],i[f+1],i[f+u]);t(e,n,a[f+u+1],a[f+1],a[f+u],i[f+u+1],i[f+1],i[f+u])}}break;case"triangles":for(s=0,o=a.length;s<o;s+=3){t(e,n,a[s],a[s+1],a[s+2],i[s],i[s+1],i[s+2])}break;default:throw new Error("illegal figure")}}function n(e,t,n,i,s,o,u){var l=1.1;var c=3e3;var f=2;var d=Math.floor(e[0]);var h=Math.floor(e[1]);var v=Math.ceil(e[2])-d;var p=Math.ceil(e[3])-h;var m=Math.min(Math.ceil(Math.abs(v*t[0]*l)),c);var g=Math.min(Math.ceil(Math.abs(p*t[1]*l)),c);var b=v/m;var y=p/g;var _={coords:n,colors:i,offsetX:-d,offsetY:-h,scaleX:1/b,scaleY:1/y};var A=m+f*2;var S=g+f*2;var w,P,k,C;if(a.WebGLUtils.isEnabled){w=a.WebGLUtils.drawFigures(m,g,o,s,_);P=u.getCanvas("mesh",A,S,false);P.context.drawImage(w,f,f);w=P.canvas}else{P=u.getCanvas("mesh",A,S,false);var R=P.context;var x=R.createImageData(m,g);if(o){var T=x.data;for(k=0,C=T.length;k<C;k+=4){T[k]=o[0];T[k+1]=o[1];T[k+2]=o[2];T[k+3]=255}}for(k=0;k<s.length;k++){r(x,s[k],_)}R.putImageData(x,f,f);w=P.canvas}return{canvas:w,offsetX:d-f*b,offsetY:h-f*y,scaleX:b,scaleY:y}}return n}();i.Mesh={fromIR:function e(t){var r=t[2];var a=t[3];var i=t[4];var o=t[5];var u=t[6];var l=t[8];return{type:"Pattern",getPattern:function e(t,c,f){var d;if(f){d=n.Util.singularValueDecompose2dScale(t.mozCurrentTransform)}else{d=n.Util.singularValueDecompose2dScale(c.baseTransform);if(u){var h=n.Util.singularValueDecompose2dScale(u);d=[d[0]*h[0],d[1]*h[1]]}}var v=s(o,d,r,a,i,f?null:l,c.cachedCanvases);if(!f){t.setTransform.apply(t,c.baseTransform);if(u){t.transform.apply(t,u)}}t.translate(v.offsetX,v.offsetY);t.scale(v.scaleX,v.scaleY);return t.createPattern(v.canvas,"no-repeat")}}}};i.Dummy={fromIR:function e(){return{type:"Pattern",getPattern:function e(){return"hotpink"}}}};function o(e){var t=i[e[0]];if(!t){throw new Error("Unknown IR type: "+e[0])}return t.fromIR(e)}var u=function e(){var t={COLORED:1,UNCOLORED:2};var r=3e3;function a(e,t,r,n,a){this.operatorList=e[2];this.matrix=e[3]||[1,0,0,1,0,0];this.bbox=e[4];this.xstep=e[5];this.ystep=e[6];this.paintType=e[7];this.tilingType=e[8];this.color=t;this.canvasGraphicsFactory=n;this.baseTransform=a;this.type="Pattern";this.ctx=r}a.prototype={createPatternCanvas:function e(t){var a=this.operatorList;var i=this.bbox;var s=this.xstep;var o=this.ystep;var u=this.paintType;var l=this.tilingType;var c=this.color;var f=this.canvasGraphicsFactory;(0,n.info)("TilingType: "+l);var d=i[0],h=i[1],v=i[2],p=i[3];var m=[d,h];var g=[d+s,h+o];var b=g[0]-m[0];var y=g[1]-m[1];var _=n.Util.singularValueDecompose2dScale(this.matrix);var A=n.Util.singularValueDecompose2dScale(this.baseTransform);var S=[_[0]*A[0],_[1]*A[1]];b=Math.min(Math.ceil(Math.abs(b*S[0])),r);y=Math.min(Math.ceil(Math.abs(y*S[1])),r);var w=t.cachedCanvases.getCanvas("pattern",b,y,true);var P=w.context;var k=f.createCanvasGraphics(P);k.groupLevel=t.groupLevel;this.setFillAndStrokeStyleToContext(k,u,c);this.setScale(b,y,s,o);this.transformToScale(k);var C=[1,0,0,1,-m[0],-m[1]];k.transform.apply(k,C);this.clipBbox(k,i,d,h,v,p);k.executeOperatorList(a);return w.canvas},setScale:function e(t,r,n,a){this.scale=[t/n,r/a]},transformToScale:function e(t){var r=this.scale;var n=[r[0],0,0,r[1],0,0];t.transform.apply(t,n)},scaleToContext:function e(){var t=this.scale;this.ctx.scale(1/t[0],1/t[1])},clipBbox:function e(t,r,n,a,i,s){if(Array.isArray(r)&&r.length===4){var o=i-n;var u=s-a;t.ctx.rect(n,a,o,u);t.clip();t.endPath()}},setFillAndStrokeStyleToContext:function e(r,a,i){var s=r.ctx,o=r.current;switch(a){case t.COLORED:var u=this.ctx;s.fillStyle=u.fillStyle;s.strokeStyle=u.strokeStyle;o.fillColor=u.fillStyle;o.strokeColor=u.strokeStyle;break;case t.UNCOLORED:var l=n.Util.makeCssRgb(i[0],i[1],i[2]);s.fillStyle=l;s.strokeStyle=l;o.fillColor=l;o.strokeColor=l;break;default:throw new n.FormatError("Unsupported paint type: "+a)}},getPattern:function e(t,r){var n=this.createPatternCanvas(r);t=this.ctx;t.setTransform.apply(t,this.baseTransform);t.transform.apply(t,this.matrix);this.scaleToContext();return t.createPattern(n,"repeat")}};return a}();t.getShadingPatternFromIR=o;t.TilingPattern=u},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:true});var n=function(){function e(e,t){var r=[];var n=true;var a=false;var i=undefined;try{for(var s=e[Symbol.iterator](),o;!(n=(o=s.next()).done);n=true){r.push(o.value);if(t&&r.length===t)break}}catch(e){a=true;i=e}finally{try{if(!n&&s["return"])s["return"]()}finally{if(a)throw i}}return r}return function(t,r){if(Array.isArray(t)){return t}else if(Symbol.iterator in Object(t)){return e(t,r)}else{throw new TypeError("Invalid attempt to destructure non-iterable instance")}}}();var a=function e(t,r,n){if(t===null)t=Function.prototype;var a=Object.getOwnPropertyDescriptor(t,r);if(a===undefined){var i=Object.getPrototypeOf(t);if(i===null){return undefined}else{return e(i,r,n)}}else if("value"in a){return a.value}else{var s=a.get;if(s===undefined){return undefined}return s.call(n)}};var i=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(t,r,n){if(r)e(t.prototype,r);if(n)e(t,n);return t}}();function s(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function o(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}function u(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var l={NoError:0,EndOfDocument:-1,UnterminatedCdat:-2,UnterminatedXmlDeclaration:-3,UnterminatedDoctypeDeclaration:-4,UnterminatedComment:-5,MalformedElement:-6,OutOfMemory:-7,UnterminatedAttributeValue:-8,UnterminatedElement:-9,ElementNeverBegun:-10};function c(e,t){var r=e[t];return r===" "||r==="\n"||r==="\r"||r==="\t"}function f(e){for(var t=0,r=e.length;t<r;t++){if(!c(e,t)){return false}}return true}var d=function(){function e(){u(this,e)}i(e,[{key:"_resolveEntities",value:function e(t){return t.replace(/&([^;]+);/g,function(e,t){if(t.substring(0,2)==="#x"){return String.fromCharCode(parseInt(t.substring(2),16))}else if(t.substring(0,1)==="#"){return String.fromCharCode(parseInt(t.substring(1),10))}switch(t){case"lt":return"<";case"gt":return">";case"amp":return"&";case"quot":return'"'}return this.onResolveEntity(t)})}},{key:"_parseContent",value:function e(t,r){var n=r,a=void 0,i=[];function s(){while(n<t.length&&c(t,n)){++n}}while(n<t.length&&!c(t,n)&&t[n]!==">"&&t[n]!=="/"){++n}a=t.substring(r,n);s();while(n<t.length&&t[n]!==">"&&t[n]!=="/"&&t[n]!=="?"){s();var o="",u="";while(n<t.length&&!c(t,n)&&t[n]!=="="){o+=t[n];++n}s();if(t[n]!=="="){return null}++n;s();var l=t[n];if(l!=='"'&&l!=="'"){return null}var f=t.indexOf(l,++n);if(f<0){return null}u=t.substring(n,f);i.push({name:o,value:this._resolveEntities(u)});n=f+1;s()}return{name:a,attributes:i,parsed:n-r}}},{key:"_parseProcessingInstruction",value:function e(t,r){var n=r,a=void 0,i=void 0;function s(){while(n<t.length&&c(t,n)){++n}}while(n<t.length&&!c(t,n)&&t[n]!==">"&&t[n]!=="/"){++n}a=t.substring(r,n);s();var o=n;while(n<t.length&&(t[n]!=="?"||t[n+1]!==">")){++n}i=t.substring(o,n);return{name:a,value:i,parsed:n-r}}},{key:"parseXml",value:function e(t){var r=0;while(r<t.length){var n=t[r];var a=r;if(n==="<"){++a;var i=t[a];var s=void 0;switch(i){case"/":++a;s=t.indexOf(">",a);if(s<0){this.onError(l.UnterminatedElement);return}this.onEndElement(t.substring(a,s));a=s+1;break;case"?":++a;var o=this._parseProcessingInstruction(t,a);if(t.substring(a+o.parsed,a+o.parsed+2)!=="?>"){this.onError(l.UnterminatedXmlDeclaration);return}this.onPi(o.name,o.value);a+=o.parsed+2;break;case"!":if(t.substring(a+1,a+3)==="--"){s=t.indexOf("--\x3e",a+3);if(s<0){this.onError(l.UnterminatedComment);return}this.onComment(t.substring(a+3,s));a=s+3}else if(t.substring(a+1,a+8)==="[CDATA["){s=t.indexOf("]]>",a+8);if(s<0){this.onError(l.UnterminatedCdat);return}this.onCdata(t.substring(a+8,s));a=s+3}else if(t.substring(a+1,a+8)==="DOCTYPE"){var u=t.indexOf("[",a+8);var c=false;s=t.indexOf(">",a+8);if(s<0){this.onError(l.UnterminatedDoctypeDeclaration);return}if(u>0&&s>u){s=t.indexOf("]>",a+8);if(s<0){this.onError(l.UnterminatedDoctypeDeclaration);return}c=true}var f=t.substring(a+8,s+(c?1:0));this.onDoctype(f);a=s+(c?2:1)}else{this.onError(l.MalformedElement);return}break;default:var d=this._parseContent(t,a);if(d===null){this.onError(l.MalformedElement);return}var h=false;if(t.substring(a+d.parsed,a+d.parsed+2)==="/>"){h=true}else if(t.substring(a+d.parsed,a+d.parsed+1)!==">"){this.onError(l.UnterminatedElement);return}this.onBeginElement(d.name,d.attributes,h);a+=d.parsed+(h?2:1);break}}else{while(a<t.length&&t[a]!=="<"){a++}var v=t.substring(r,a);this.onText(this._resolveEntities(v))}r=a}}},{key:"onResolveEntity",value:function e(t){return"&"+t+";"}},{key:"onPi",value:function e(t,r){}},{key:"onComment",value:function e(t){}},{key:"onCdata",value:function e(t){}},{key:"onDoctype",value:function e(t){}},{key:"onText",value:function e(t){}},{key:"onBeginElement",value:function e(t,r,n){}},{key:"onEndElement",value:function e(t){}},{key:"onError",value:function e(t){}}]);return e}();var h=function(){function e(t,r){u(this,e);this.nodeName=t;this.nodeValue=r;Object.defineProperty(this,"parentNode",{value:null,writable:true})}i(e,[{key:"hasChildNodes",value:function e(){return this.childNodes&&this.childNodes.length>0}},{key:"firstChild",get:function e(){return this.childNodes[0]}},{key:"nextSibling",get:function e(){var t=this.parentNode.childNodes.indexOf(this);return this.parentNode.childNodes[t+1]}},{key:"textContent",get:function e(){if(!this.childNodes){return this.nodeValue||""}return this.childNodes.map(function(e){return e.textContent}).join("")}}]);return e}();var v=function(e){o(t,e);function t(){u(this,t);var e=s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));e._currentFragment=null;e._stack=null;e._errorCode=l.NoError;return e}i(t,[{key:"parseFromString",value:function e(t){this._currentFragment=[];this._stack=[];this._errorCode=l.NoError;this.parseXml(t);if(this._errorCode!==l.NoError){return undefined}var r=n(this._currentFragment,1),a=r[0];return{documentElement:a}}},{key:"onResolveEntity",value:function e(r){switch(r){case"apos":return"'"}return a(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"onResolveEntity",this).call(this,r)}},{key:"onText",value:function e(t){if(f(t)){return}var r=new h("#text",t);this._currentFragment.push(r)}},{key:"onCdata",value:function e(t){var r=new h("#text",t);this._currentFragment.push(r)}},{key:"onBeginElement",value:function e(t,r,n){var a=new h(t);a.childNodes=[];this._currentFragment.push(a);if(n){return}this._stack.push(this._currentFragment);this._currentFragment=a.childNodes}},{key:"onEndElement",value:function e(t){this._currentFragment=this._stack.pop();var r=this._currentFragment[this._currentFragment.length-1];for(var n=0,a=r.childNodes.length;n<a;n++){r.childNodes[n].parentNode=r}}},{key:"onError",value:function e(t){this._errorCode=t}}]);return t}(d);t.SimpleXMLParser=v},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.PDFDataTransportStream=undefined;var n=r(0);var a=function e(){function t(e,t){var r=this;(0,n.assert)(t);this._queuedChunks=[];var a=e.initialData;if(a&&a.length>0){var i=new Uint8Array(a).buffer;this._queuedChunks.push(i)}this._pdfDataRangeTransport=t;this._isRangeSupported=!e.disableRange;this._isStreamingSupported=!e.disableStream;this._contentLength=e.length;this._fullRequestReader=null;this._rangeReaders=[];this._pdfDataRangeTransport.addRangeListener(function(e,t){r._onReceiveData({begin:e,chunk:t})});this._pdfDataRangeTransport.addProgressListener(function(e){r._onProgress({loaded:e})});this._pdfDataRangeTransport.addProgressiveReadListener(function(e){r._onReceiveData({chunk:e})});this._pdfDataRangeTransport.transportReady()}t.prototype={_onReceiveData:function e(t){var r=new Uint8Array(t.chunk).buffer;if(t.begin===undefined){if(this._fullRequestReader){this._fullRequestReader._enqueue(r)}else{this._queuedChunks.push(r)}}else{var a=this._rangeReaders.some(function(e){if(e._begin!==t.begin){return false}e._enqueue(r);return true});(0,n.assert)(a)}},_onProgress:function e(t){if(this._rangeReaders.length>0){var r=this._rangeReaders[0];if(r.onProgress){r.onProgress({loaded:t.loaded})}}},_removeRangeReader:function e(t){var r=this._rangeReaders.indexOf(t);if(r>=0){this._rangeReaders.splice(r,1)}},getFullReader:function e(){(0,n.assert)(!this._fullRequestReader);var t=this._queuedChunks;this._queuedChunks=null;return new r(this,t)},getRangeReader:function e(t,r){var n=new a(this,t,r);this._pdfDataRangeTransport.requestDataRange(t,r);this._rangeReaders.push(n);return n},cancelAllRequests:function e(t){if(this._fullRequestReader){this._fullRequestReader.cancel(t)}var r=this._rangeReaders.slice(0);r.forEach(function(e){e.cancel(t)});this._pdfDataRangeTransport.abort()}};function r(e,t){this._stream=e;this._done=false;this._queuedChunks=t||[];this._requests=[];this._headersReady=Promise.resolve();e._fullRequestReader=this;this.onProgress=null}r.prototype={_enqueue:function e(t){if(this._done){return}if(this._requests.length>0){var r=this._requests.shift();r.resolve({value:t,done:false});return}this._queuedChunks.push(t)},get headersReady(){return this._headersReady},get isRangeSupported(){return this._stream._isRangeSupported},get isStreamingSupported(){return this._stream._isStreamingSupported},get contentLength(){return this._stream._contentLength},read:function e(){if(this._queuedChunks.length>0){var t=this._queuedChunks.shift();return Promise.resolve({value:t,done:false})}if(this._done){return Promise.resolve({value:undefined,done:true})}var r=(0,n.createPromiseCapability)();this._requests.push(r);return r.promise},cancel:function e(t){this._done=true;this._requests.forEach(function(e){e.resolve({value:undefined,done:true})});this._requests=[]}};function a(e,t,r){this._stream=e;this._begin=t;this._end=r;this._queuedChunk=null;this._requests=[];this._done=false;this.onProgress=null}a.prototype={_enqueue:function e(t){if(this._done){return}if(this._requests.length===0){this._queuedChunk=t}else{var r=this._requests.shift();r.resolve({value:t,done:false});this._requests.forEach(function(e){e.resolve({value:undefined,done:true})});this._requests=[]}this._done=true;this._stream._removeRangeReader(this)},get isStreamingSupported(){return false},read:function e(){if(this._queuedChunk){var t=this._queuedChunk;this._queuedChunk=null;return Promise.resolve({value:t,done:false})}if(this._done){return Promise.resolve({value:undefined,done:true})}var r=(0,n.createPromiseCapability)();this._requests.push(r);return r.promise},cancel:function e(t){this._done=true;this._requests.forEach(function(e){e.resolve({value:undefined,done:true})});this._requests=[];this._stream._removeRangeReader(this)}};return t}();t.PDFDataTransportStream=a},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.PDFNodeStream=undefined;var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(t,r,n){if(r)e(t.prototype,r);if(n)e(t,n);return t}}();var a=r(0);var i=r(44);function s(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function o(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}function u(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var l=require("fs");var c=require("http");var f=require("https");var d=require("url");var h=function(){function e(t){u(this,e);this.options=t;this.source=t.source;this.url=d.parse(this.source.url);this.isHttp=this.url.protocol==="http:"||this.url.protocol==="https:";this.isFsUrl=this.url.protocol==="file:"||!this.url.host;this.httpHeaders=this.isHttp&&this.source.httpHeaders||{};this._fullRequest=null;this._rangeRequestReaders=[]}n(e,[{key:"getFullReader",value:function e(){(0,a.assert)(!this._fullRequest);this._fullRequest=this.isFsUrl?new y(this):new g(this);return this._fullRequest}},{key:"getRangeReader",value:function e(t,r){var n=this.isFsUrl?new _(this,t,r):new b(this,t,r);this._rangeRequestReaders.push(n);return n}},{key:"cancelAllRequests",value:function e(t){if(this._fullRequest){this._fullRequest.cancel(t)}var r=this._rangeRequestReaders.slice(0);r.forEach(function(e){e.cancel(t)})}}]);return e}();var v=function(){function e(t){u(this,e);this._url=t.url;this._done=false;this._errored=false;this._reason=null;this.onProgress=null;this._contentLength=t.source.length;this._loaded=0;this._disableRange=t.options.disableRange||false;this._rangeChunkSize=t.source.rangeChunkSize;if(!this._rangeChunkSize&&!this._disableRange){this._disableRange=true}this._isStreamingSupported=!t.source.disableStream;this._isRangeSupported=!t.options.disableRange;this._readableStream=null;this._readCapability=(0,a.createPromiseCapability)();this._headersCapability=(0,a.createPromiseCapability)()}n(e,[{key:"read",value:function e(){var t=this;return this._readCapability.promise.then(function(){if(t._done){return Promise.resolve({value:undefined,done:true})}if(t._errored){return Promise.reject(t._reason)}var e=t._readableStream.read();if(e===null){t._readCapability=(0,a.createPromiseCapability)();return t.read()}t._loaded+=e.length;if(t.onProgress){t.onProgress({loaded:t._loaded,total:t._contentLength})}var r=new Uint8Array(e).buffer;return Promise.resolve({value:r,done:false})})}},{key:"cancel",value:function e(t){if(!this._readableStream){this._error(t);return}this._readableStream.destroy(t)}},{key:"_error",value:function e(t){this._errored=true;this._reason=t;this._readCapability.resolve()}},{key:"_setReadableStream",value:function e(t){var r=this;this._readableStream=t;t.on("readable",function(){r._readCapability.resolve()});t.on("end",function(){t.destroy();r._done=true;r._readCapability.resolve()});t.on("error",function(e){r._error(e)});if(!this._isStreamingSupported&&this._isRangeSupported){this._error(new a.AbortException("streaming is disabled"))}if(this._errored){this._readableStream.destroy(this._reason)}}},{key:"headersReady",get:function e(){return this._headersCapability.promise}},{key:"contentLength",get:function e(){return this._contentLength}},{key:"isRangeSupported",get:function e(){return this._isRangeSupported}},{key:"isStreamingSupported",get:function e(){return this._isStreamingSupported}}]);return e}();var p=function(){function e(t){u(this,e);this._url=t.url;this._done=false;this._errored=false;this._reason=null;this.onProgress=null;this._loaded=0;this._readableStream=null;this._readCapability=(0,a.createPromiseCapability)();this._isStreamingSupported=!t.source.disableStream}n(e,[{key:"read",value:function e(){var t=this;return this._readCapability.promise.then(function(){if(t._done){return Promise.resolve({value:undefined,done:true})}if(t._errored){return Promise.reject(t._reason)}var e=t._readableStream.read();if(e===null){t._readCapability=(0,a.createPromiseCapability)();return t.read()}t._loaded+=e.length;if(t.onProgress){t.onProgress({loaded:t._loaded})}var r=new Uint8Array(e).buffer;return Promise.resolve({value:r,done:false})})}},{key:"cancel",value:function e(t){if(!this._readableStream){this._error(t);return}this._readableStream.destroy(t)}},{key:"_error",value:function e(t){this._errored=true;this._reason=t;this._readCapability.resolve()}},{key:"_setReadableStream",value:function e(t){var r=this;this._readableStream=t;t.on("readable",function(){r._readCapability.resolve()});t.on("end",function(){t.destroy();r._done=true;r._readCapability.resolve()});t.on("error",function(e){r._error(e)});if(this._errored){this._readableStream.destroy(this._reason)}}},{key:"isStreamingSupported",get:function e(){return this._isStreamingSupported}}]);return e}();function m(e,t){return{protocol:e.protocol,auth:e.auth,host:e.hostname,port:e.port,path:e.path,method:"GET",headers:t}}var g=function(e){o(t,e);function t(e){u(this,t);var r=s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));var n=function t(n){r._headersCapability.resolve();r._setReadableStream(n);var a=(0,i.validateRangeRequestCapabilities)({getResponseHeader:function e(t){return r._readableStream.headers[t.toLowerCase()]},isHttp:e.isHttp,rangeChunkSize:r._rangeChunkSize,disableRange:r._disableRange}),s=a.allowRangeRequests,o=a.suggestedLength;if(s){r._isRangeSupported=true}r._contentLength=o};r._request=null;if(r._url.protocol==="http:"){r._request=c.request(m(r._url,e.httpHeaders),n)}else{r._request=f.request(m(r._url,e.httpHeaders),n)}r._request.on("error",function(e){r._errored=true;r._reason=e;r._headersCapability.reject(e)});r._request.end();return r}return t}(v);var b=function(e){o(t,e);function t(e,r,n){u(this,t);var a=s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));a._httpHeaders={};for(var i in e.httpHeaders){var o=e.httpHeaders[i];if(typeof o==="undefined"){continue}a._httpHeaders[i]=o}a._httpHeaders["Range"]="bytes="+r+"-"+(n-1);a._request=null;if(a._url.protocol==="http:"){a._request=c.request(m(a._url,a._httpHeaders),function(e){a._setReadableStream(e)})}else{a._request=f.request(m(a._url,a._httpHeaders),function(e){a._setReadableStream(e)})}a._request.on("error",function(e){a._errored=true;a._reason=e});a._request.end();return a}return t}(p);var y=function(e){o(t,e);function t(e){u(this,t);var r=s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));var n=decodeURI(r._url.path);l.lstat(n,function(e,t){if(e){r._errored=true;r._reason=e;r._headersCapability.reject(e);return}r._contentLength=t.size;r._setReadableStream(l.createReadStream(n));r._headersCapability.resolve()});return r}return t}(v);var _=function(e){o(t,e);function t(e,r,n){u(this,t);var a=s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));a._setReadableStream(l.createReadStream(decodeURI(a._url.path),{start:r,end:n-1}));return a}return t}(p);t.PDFNodeStream=h},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.PDFFetchStream=undefined;var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(t,r,n){if(r)e(t.prototype,r);if(n)e(t,n);return t}}();var a=r(0);var i=r(44);function s(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function o(e,t){return{method:"GET",headers:e,mode:"cors",credentials:t?"include":"same-origin",redirect:"follow"}}var u=function(){function e(t){s(this,e);this.options=t;this.source=t.source;this.isHttp=/^https?:/i.test(this.source.url);this.httpHeaders=this.isHttp&&this.source.httpHeaders||{};this._fullRequestReader=null;this._rangeRequestReaders=[]}n(e,[{key:"getFullReader",value:function e(){(0,a.assert)(!this._fullRequestReader);this._fullRequestReader=new l(this);return this._fullRequestReader}},{key:"getRangeReader",value:function e(t,r){var n=new c(this,t,r);this._rangeRequestReaders.push(n);return n}},{key:"cancelAllRequests",value:function e(t){if(this._fullRequestReader){this._fullRequestReader.cancel(t)}var r=this._rangeRequestReaders.slice(0);r.forEach(function(e){e.cancel(t)})}}]);return e}();var l=function(){function e(t){var r=this;s(this,e);this._stream=t;this._reader=null;this._loaded=0;this._withCredentials=t.source.withCredentials;this._contentLength=this._stream.source.length;this._headersCapability=(0,a.createPromiseCapability)();this._disableRange=this._stream.options.disableRange;this._rangeChunkSize=this._stream.source.rangeChunkSize;if(!this._rangeChunkSize&&!this._disableRange){this._disableRange=true}this._isRangeSupported=!this._stream.options.disableRange;this._isStreamingSupported=!this._stream.source.disableStream;this._headers=new Headers;for(var n in this._stream.httpHeaders){var u=this._stream.httpHeaders[n];if(typeof u==="undefined"){continue}this._headers.append(n,u)}var l=this._stream.source.url;fetch(l,o(this._headers,this._withCredentials)).then(function(e){if(!(0,i.validateResponseStatus)(e.status)){throw(0,i.createResponseStatusError)(e.status,l)}r._reader=e.body.getReader();r._headersCapability.resolve();var t=(0,i.validateRangeRequestCapabilities)({getResponseHeader:function t(r){return e.headers.get(r)},isHttp:r._stream.isHttp,rangeChunkSize:r._rangeChunkSize,disableRange:r._disableRange}),n=t.allowRangeRequests,s=t.suggestedLength;r._contentLength=s;r._isRangeSupported=n;if(!r._isStreamingSupported&&r._isRangeSupported){r.cancel(new a.AbortException("streaming is disabled"))}}).catch(this._headersCapability.reject);this.onProgress=null}n(e,[{key:"read",value:function e(){var t=this;return this._headersCapability.promise.then(function(){return t._reader.read().then(function(e){var r=e.value,n=e.done;if(n){return Promise.resolve({value:r,done:n})}t._loaded+=r.byteLength;if(t.onProgress){t.onProgress({loaded:t._loaded,total:t._contentLength})}var a=new Uint8Array(r).buffer;return Promise.resolve({value:a,done:false})})})}},{key:"cancel",value:function e(t){if(this._reader){this._reader.cancel(t)}}},{key:"headersReady",get:function e(){return this._headersCapability.promise}},{key:"contentLength",get:function e(){return this._contentLength}},{key:"isRangeSupported",get:function e(){return this._isRangeSupported}},{key:"isStreamingSupported",get:function e(){return this._isStreamingSupported}}]);return e}();var c=function(){function e(t,r,n){var u=this;s(this,e);this._stream=t;this._reader=null;this._loaded=0;this._withCredentials=t.source.withCredentials;this._readCapability=(0,a.createPromiseCapability)();this._isStreamingSupported=!t.source.disableStream;this._headers=new Headers;for(var l in this._stream.httpHeaders){var c=this._stream.httpHeaders[l];if(typeof c==="undefined"){continue}this._headers.append(l,c)}var f=r+"-"+(n-1);this._headers.append("Range","bytes="+f);var d=this._stream.source.url;fetch(d,o(this._headers,this._withCredentials)).then(function(e){if(!(0,i.validateResponseStatus)(e.status)){throw(0,i.createResponseStatusError)(e.status,d)}u._readCapability.resolve();u._reader=e.body.getReader()});this.onProgress=null}n(e,[{key:"read",value:function e(){var t=this;return this._readCapability.promise.then(function(){return t._reader.read().then(function(e){var r=e.value,n=e.done;if(n){return Promise.resolve({value:r,done:n})}t._loaded+=r.byteLength;if(t.onProgress){t.onProgress({loaded:t._loaded})}var a=new Uint8Array(r).buffer;return Promise.resolve({value:a,done:false})})})}},{key:"cancel",value:function e(t){if(this._reader){this._reader.cancel(t)}}},{key:"isStreamingSupported",get:function e(){return this._isStreamingSupported}}]);return e}();t.PDFFetchStream=u},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.NetworkManager=t.PDFNetworkStream=undefined;var n=r(0);var a=r(44);var i=r(20);var s=o(i);function o(e){return e&&e.__esModule?e:{default:e}}var u=200;var l=206;function c(e,t){this.url=e;t=t||{};this.isHttp=/^https?:/i.test(e);this.httpHeaders=this.isHttp&&t.httpHeaders||{};this.withCredentials=t.withCredentials||false;this.getXhr=t.getXhr||function e(){return new XMLHttpRequest};this.currXhrId=0;this.pendingRequests=Object.create(null);this.loadedRequests=Object.create(null)}function f(e){var t=e.response;if(typeof t!=="string"){return t}var r=(0,n.stringToBytes)(t);return r.buffer}var d=function e(){try{var t=new XMLHttpRequest;t.open("GET",s.default.location.href);t.responseType="moz-chunked-arraybuffer";return t.responseType==="moz-chunked-arraybuffer"}catch(e){return false}}();c.prototype={requestRange:function e(t,r,n){var a={begin:t,end:r};for(var i in n){a[i]=n[i]}return this.request(a)},requestFull:function e(t){return this.request(t)},request:function e(t){var r=this.getXhr();var n=this.currXhrId++;var a=this.pendingRequests[n]={xhr:r};r.open("GET",this.url);r.withCredentials=this.withCredentials;for(var i in this.httpHeaders){var s=this.httpHeaders[i];if(typeof s==="undefined"){continue}r.setRequestHeader(i,s)}if(this.isHttp&&"begin"in t&&"end"in t){var o=t.begin+"-"+(t.end-1);r.setRequestHeader("Range","bytes="+o);a.expectedStatus=206}else{a.expectedStatus=200}var u=d&&!!t.onProgressiveData;if(u){r.responseType="moz-chunked-arraybuffer";a.onProgressiveData=t.onProgressiveData;a.mozChunked=true}else{r.responseType="arraybuffer"}if(t.onError){r.onerror=function(e){t.onError(r.status)}}r.onreadystatechange=this.onStateChange.bind(this,n);r.onprogress=this.onProgress.bind(this,n);a.onHeadersReceived=t.onHeadersReceived;a.onDone=t.onDone;a.onError=t.onError;a.onProgress=t.onProgress;r.send(null);return n},onProgress:function e(t,r){var n=this.pendingRequests[t];if(!n){return}if(n.mozChunked){var a=f(n.xhr);n.onProgressiveData(a)}var i=n.onProgress;if(i){i(r)}},onStateChange:function e(t,r){var n=this.pendingRequests[t];if(!n){return}var a=n.xhr;if(a.readyState>=2&&n.onHeadersReceived){n.onHeadersReceived();delete n.onHeadersReceived}if(a.readyState!==4){return}if(!(t in this.pendingRequests)){return}delete this.pendingRequests[t];if(a.status===0&&this.isHttp){if(n.onError){n.onError(a.status)}return}var i=a.status||u;var s=i===u&&n.expectedStatus===l;if(!s&&i!==n.expectedStatus){if(n.onError){n.onError(a.status)}return}this.loadedRequests[t]=true;var o=f(a);if(i===l){var c=a.getResponseHeader("Content-Range");var d=/bytes (\d+)-(\d+)\/(\d+)/.exec(c);var h=parseInt(d[1],10);n.onDone({begin:h,chunk:o})}else if(n.onProgressiveData){n.onDone(null)}else if(o){n.onDone({begin:0,chunk:o})}else if(n.onError){n.onError(a.status)}},hasPendingRequests:function e(){for(var t in this.pendingRequests){return true}return false},getRequestXhr:function e(t){return this.pendingRequests[t].xhr},isStreamingRequest:function e(t){return!!this.pendingRequests[t].onProgressiveData},isPendingRequest:function e(t){return t in this.pendingRequests},isLoadedRequest:function e(t){return t in this.loadedRequests},abortAllRequests:function e(){for(var t in this.pendingRequests){this.abortRequest(t|0)}},abortRequest:function e(t){var r=this.pendingRequests[t].xhr;delete this.pendingRequests[t];r.abort()}};function h(e){this._options=e;var t=e.source;this._manager=new c(t.url,{httpHeaders:t.httpHeaders,withCredentials:t.withCredentials});this._rangeChunkSize=t.rangeChunkSize;this._fullRequestReader=null;this._rangeRequestReaders=[]}h.prototype={_onRangeRequestReaderClosed:function e(t){var r=this._rangeRequestReaders.indexOf(t);if(r>=0){this._rangeRequestReaders.splice(r,1)}},getFullReader:function e(){(0,n.assert)(!this._fullRequestReader);this._fullRequestReader=new v(this._manager,this._options);return this._fullRequestReader},getRangeReader:function e(t,r){var n=new p(this._manager,t,r);n.onClosed=this._onRangeRequestReaderClosed.bind(this);this._rangeRequestReaders.push(n);return n},cancelAllRequests:function e(t){if(this._fullRequestReader){this._fullRequestReader.cancel(t)}var r=this._rangeRequestReaders.slice(0);r.forEach(function(e){e.cancel(t)})}};function v(e,t){this._manager=e;var r=t.source;var a={onHeadersReceived:this._onHeadersReceived.bind(this),onProgressiveData:r.disableStream?null:this._onProgressiveData.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=r.url;this._fullRequestId=e.requestFull(a);this._headersReceivedCapability=(0,n.createPromiseCapability)();this._disableRange=t.disableRange||false;this._contentLength=r.length;this._rangeChunkSize=r.rangeChunkSize;if(!this._rangeChunkSize&&!this._disableRange){this._disableRange=true}this._isStreamingSupported=false;this._isRangeSupported=false;this._cachedChunks=[];this._requests=[];this._done=false;this._storedError=undefined;this.onProgress=null}v.prototype={_onHeadersReceived:function e(){var t=this._fullRequestId;var r=this._manager.getRequestXhr(t);var n=(0,a.validateRangeRequestCapabilities)({getResponseHeader:function e(t){return r.getResponseHeader(t)},isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange}),i=n.allowRangeRequests,s=n.suggestedLength;this._contentLength=s||this._contentLength;if(i){this._isRangeSupported=true}var o=this._manager;if(o.isStreamingRequest(t)){this._isStreamingSupported=true}else if(this._isRangeSupported){o.abortRequest(t)}this._headersReceivedCapability.resolve()},_onProgressiveData:function e(t){if(this._requests.length>0){var r=this._requests.shift();r.resolve({value:t,done:false})}else{this._cachedChunks.push(t)}},_onDone:function e(t){if(t){this._onProgressiveData(t.chunk)}this._done=true;if(this._cachedChunks.length>0){return}this._requests.forEach(function(e){e.resolve({value:undefined,done:true})});this._requests=[]},_onError:function e(t){var r=this._url;var n=(0,a.createResponseStatusError)(t,r);this._storedError=n;this._headersReceivedCapability.reject(n);this._requests.forEach(function(e){e.reject(n)});this._requests=[];this._cachedChunks=[]},_onProgress:function e(t){if(this.onProgress){this.onProgress({loaded:t.loaded,total:t.lengthComputable?t.total:this._contentLength})}},get isRangeSupported(){return this._isRangeSupported},get isStreamingSupported(){return this._isStreamingSupported},get contentLength(){return this._contentLength},get headersReady(){return this._headersReceivedCapability.promise},read:function e(){if(this._storedError){return Promise.reject(this._storedError)}if(this._cachedChunks.length>0){var t=this._cachedChunks.shift();return Promise.resolve({value:t,done:false})}if(this._done){return Promise.resolve({value:undefined,done:true})}var r=(0,n.createPromiseCapability)();this._requests.push(r);return r.promise},cancel:function e(t){this._done=true;this._headersReceivedCapability.reject(t);this._requests.forEach(function(e){e.resolve({value:undefined,done:true})});this._requests=[];if(this._manager.isPendingRequest(this._fullRequestId)){this._manager.abortRequest(this._fullRequestId)}this._fullRequestReader=null}};function p(e,t,r){this._manager=e;var n={onDone:this._onDone.bind(this),onProgress:this._onProgress.bind(this)};this._requestId=e.requestRange(t,r,n);this._requests=[];this._queuedChunk=null;this._done=false;this.onProgress=null;this.onClosed=null}p.prototype={_close:function e(){if(this.onClosed){this.onClosed(this)}},_onDone:function e(t){var r=t.chunk;if(this._requests.length>0){var n=this._requests.shift();n.resolve({value:r,done:false})}else{this._queuedChunk=r}this._done=true;this._requests.forEach(function(e){e.resolve({value:undefined,done:true})});this._requests=[];this._close()},_onProgress:function e(t){if(!this.isStreamingSupported&&this.onProgress){this.onProgress({loaded:t.loaded})}},get isStreamingSupported(){return false},read:function e(){if(this._queuedChunk!==null){var t=this._queuedChunk;this._queuedChunk=null;return Promise.resolve({value:t,done:false})}if(this._done){return Promise.resolve({value:undefined,done:true})}var r=(0,n.createPromiseCapability)();this._requests.push(r);return r.promise},cancel:function e(t){this._done=true;this._requests.forEach(function(e){e.resolve({value:undefined,done:true})});this._requests=[];if(this._manager.isPendingRequest(this._requestId)){this._manager.abortRequest(this._requestId)}this._close()}};t.PDFNetworkStream=h;t.NetworkManager=c}])});