import React from "react";
import { Firebase } from "../../config/Firebase";
import "./info.css"
import InventoryInfo from "./InventoryInfo";
export default class Info extends React.Component {

  constructor(props) {
    super(props);
    this.state = {

    }
    this.ViewCta = this.ViewCta.bind(this);
  }

  shouldComponentUpdate(nextProps, nextState) {
    if (nextProps.info != this.props.info) {

      var cameraEl = document.getElementById('cam1');
      const data = {
        actiontype: "infocard",
        lockmode: true,
        data: this.props.info,
        rotation: cameraEl.getAttribute("rotation")
      };
      this.props.SendCustomMessage(data)

    }
    return true
  }


  ViewCta(Link,authcheck) {
    var _D_Link = decodeURI(Link);
    if (_D_Link.includes("https://propvr.tech/embed")) {
      var _nodes = _D_Link.split("?cid=")
      var _Data = _nodes[1].split("&proid=");
      var _uid = _Data[0];
      var _project = _Data[1];

      if(authcheck){
        this.props.closeinfo();
        this.props.changeProject(_project)
      }else{
        if (_uid == Firebase.auth().currentUser.uid) {
        this.props.closeinfo();
        this.props.changeProject(_project)
      }
      else {
        // window.open(Link)
      }
      }
      
    } else {
      // window.open(Link)
    }
  }
  render() {
    if(this.props.info.InfoType=="Info"){
      return (

      <><div id="infocards-modal" className="center-info" style={{ position: 'absolute', width: '100%', height: '100%' }}>
        <div id="cards-info" style={{ display: 'block', position: 'relative', margin: "auto", top: "0px", left: "0px", height: 'fit-content', transform: 'scale(0.8)', zIndex: 999, maxWidth: "450px" }} className={this.props.info.isMobile ? "info-cards testinfo center-div-info" : "info-cards testinfo"}>

          <div class="info-card-item">
            <button style={{ position: 'absolute', right: '20px', zIndex: '99', background: "#fff", borderRadius: "16px", margin: "-22px -12px" }} onClick={this.props.closeinfo} type="button" className="close" data-dismiss="modal" aria-label="Close">


              <span aria-hidden="true">
                <svg width="24" height="24" viewBox="0 0 24 24">
                  <defs>
                    <path id="prefix__close" d="M7.414 6l4.293-4.293c.391-.391.391-1.023 0-1.414-.39-.391-1.023-.391-1.414 0L6 4.586 1.707.293C1.317-.098.684-.098.293.293c-.39.391-.39 1.023 0 1.414L4.586 6 .293 10.293c-.39.391-.39 1.023 0 1.414.195.195.451.293.707.293.256 0 .512-.098.707-.293L6 7.414l4.293 4.293c.195.195.451.293.707.293.256 0 .512-.098.707-.293.391-.391.391-1.023 0-1.414L7.414 6z" />
                  </defs>
                  <g fill="none" fillRule="evenodd" transform="translate(6 6)">
                    <use fill="#222B45" href="#prefix__close" />
                  </g>
                </svg></span>
            </button>
            {this.props.info.infoimgurl != undefined && this.props.info.infoimgurl.length ?
              <div style={{ backgroundImage: "url('" + this.props.info.infoimgurl + "')" }} className="info-card-image" >



              </div>
              :
              this.props.info.vidurl != undefined && this.props.info.vidurl.length ?

                <div className="info-card-image">
                  { }
                  <iframe className="ytembed" style={{ width: '100%', height: '100%' }} src={YoutubeURL(this.props.info.vidurl)} frameBorder={0} allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowFullScreen />
                </div>
                : this.props.info.info3durl != undefined && this.props.info.info3durl.length ?
                  <div className="info-card-image">
                    <model-viewer style={{ width: 'auto!important', height: 'inherit !important' }} className="arcard" src={this.props.info.info3durl} ios-src={this.props.info.info3diosurl} shadow-intensity={1} camera-controls interaction-prompt="auto" auto-rotate ar>
                      <button className="fullscreenbut" onclick="this.parentElement.requestFullscreen();" style={{ backgroundColor: 'white', backgroundImage: 'url(https://logomakr.com/media/clipart/tnp_landan/1408665.svg)', borderRadius: '4px', border: 'none', position: 'absolute', top: '16px', right: '16px', width: '35px', height: '35px' }}>
                      </button>
                    </model-viewer>


                  </div>


                  : ""

            }



            {this.props.info.Description != undefined && this.props.info.Description.length ?
              <div className="card-info"> <h2 className="card-title">{this.props.info.key}</h2><p className="card-intro">{this.props.info.Description}</p>
                <p style={{ margin: '0px', paddingTop: '10px' }}>
                  {this.props.info.buyurl != undefined && this.props.info.buyurl.length ?

                    <a style={{ float: 'right', backgroundColor: 'crimson' }} className="info-url" target="_blank" href={this.props.info.buyurl}>
                      Buy</a>

                    : ""}{this.props.info.knowurl != undefined && this.props.info.knowurl.length ?
                      <a style={{ float: 'right' }} className="info-url" target="_blank" href={this.props.info.knowurl}>Know More</a>

                      : ""
                  }</p>
              </div>

            : ""}
            {this.props.info.ctaLink != "" && this.props.info.ctaLink != undefined ?
              <>
                <p style={{ margin: "0px", paddingTop: "10px" }}><button onClick={() => { this.ViewCta(this.props.info.ctaLink,false) }} style={{ float: "right", color: "#fff !important" }} className="info-url">{this.props.info.ctaName}</button>
                </p></>
              : ""}
          </div>


        </div>


      </div>
      </>
    )
    }
    else if(this.props.info.InfoType=="InventoryInfo") {
      return (
        <InventoryInfo changeImage={this.props.changeImage}
        GetImageId={this.props.GetImageId} ViewCta={this.ViewCta} closeinfo={this.props.closeinfo} info={this.props.info}/>
        
      )
    }
    else{
      return ""
    }
    
  }
}

function YoutubeURL(url) {
  var vidurl = url;
  if (vidurl.search("watch") > 0) {
    vidurl = vidurl.replace('watch?v=', 'embed/');
  }
  else if (vidurl.search("youtu.be") > 0) {
    vidurl = vidurl.replace('youtu.be', 'youtube.com/embed/');
  }
  else {
    vidurl = vidurl + "?enablejsapi=1&version=3&playerapiid=ytplayer";
  }
  return vidurl;
}