import Firebase from 'firebase/compat/app';
import "firebase/compat/auth";
import "firebase/compat/database"
import "firebase/compat/storage"
import 'firebase/compat/firestore';
const config = {
  apiKey: process.env.REACT_APP_API_FIREBASE_API_KEY,
  authDomain: process.env.REACT_APP_API_FIREBASE_AUTH_DOMAIN,
  databaseURL: process.env.REACT_APP_API_FIREBASE_DATABASE_URL,
  projectId: process.env.REACT_APP_API_FIREBASE_PROJECT_ID,
  storageBucket: process.env.REACT_APP_API_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.REACT_APP_API_FIREBASE_MESSAGINGSENDER_ID,
  appId: process.env.REACT_APP_API_FIREBASE_APP_ID


};

export {Firebase};
const fire = Firebase.initializeApp(config);
export default fire;

