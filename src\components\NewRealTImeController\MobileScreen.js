// MobileControls.jsx - Updated
import React, { useState, useEffect, useRef } from 'react';
import {
    MicIcon, MicOffIcon, VideoIcon, VideoOffIcon,
    UsersIcon, ChatIcon, EndCallIcon, EyeIcon, EyeOffIcon,
    FullScreenIcon, ActiveChat, ActiveMember, ActiveDropdown,
    StopScreenShareIcon, ScreenShareIcon
} from '../../assets/SvgIcons';
import Timer from '../../Tools/ToolComponents/Timer';
import GuestTimer from '../../Tools/ToolComponents/GuestTimer';
import LarkXRSettingsModal from '../LarkXR/LarkXRSettingsModal';
import LarkXRHelpModal from '../LarkXR/LarkXRHelpModal';
import LarkXRNetworkModal from '../LarkXR/LarkXRNetworkModal';

const MobileControls = ({
    Audio, Video, Tab, ChatO<PERSON>, MembersOpen, UnreadCount, userscount,
    handleAudioControl, handleVideoControl,
    handleTabControl, handleEndSession, handleStopSharing, handleEndSessionWithModal, handleStopSharingWithModal,
    toggleFullscreen, handleInvertControls, handleScreenShare, ScreenShare,
    ShowControls, handleShareProject, switchingProject, SessionDetails, roomId,
    isGuest = false
}) => {
    const [showMenu, setShowMenu] = useState(false);
    const [activeModal, setActiveModal] = useState(null);
    const [modalProps, setModalProps] = useState({});
    const menuRef = useRef(null);

    const toggleMenu = () => {
        setShowMenu(!showMenu);
    };

    const handleOpenModal = (modalType, props = {}) => {
        console.log('Mobile: Opening modal:', modalType);
        setActiveModal(modalType);
        setModalProps(props);
    };

    const handleCloseModal = () => {
        console.log('Mobile: Closing modal');
        setActiveModal(null);
        setModalProps({});
    };

    // Close menu when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (menuRef.current && !menuRef.current.contains(event.target)) {
                setShowMenu(false);
            }
        };

        // Add event listener when menu is open
        if (showMenu) {
            document.addEventListener('mousedown', handleClickOutside);
        }

        // Clean up event listener
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [showMenu]);

    return (
        <div className="w-full z-40 bg-white py-2 px-2">
            {/* Top control bar for mobile */}
            <div className="flex justify-evenly items-center gap-2">
                {/* Timer component */}
{isGuest ? (
                    <GuestTimer
                        position={"top"}
                        Config={SessionDetails}
                        start={SessionDetails?.start_time || new Date()}
                        roomId={roomId}
                        trackInactivity={false}
                        trackMaxtimeout={false}
                    />
                ) : (
                    <Timer
                        position={"top"}
                        start={SessionDetails?.start || new Date()}
                        roomId={roomId}
                        trackInactivity={false}
                        trackMaxtimeout={true}
                        SessionDetails={SessionDetails}
                    />
                )}

                <button
                    onClick={handleAudioControl}
                    className="w-8 h-8 rounded-lg flex items-center justify-center bg-[#F3F4F6]"
                >
                    <div className="flex items-center justify-center w-5 h-5">
                        {Audio ? <MicIcon /> : <MicOffIcon />}
                    </div>
                </button>

                <button
                    onClick={handleVideoControl}
                    className="w-8 h-8 rounded-lg flex items-center justify-center bg-[#F3F4F6]"
                >
                    <div className="flex items-center justify-center w-5 h-5">
                        {Video ? <VideoIcon /> : <VideoOffIcon />}
                    </div>
                </button>

                {/* Screen Share Button - only for host, not guest */}
                {!isGuest && (
                    <button
                        onClick={handleScreenShare}
                        className={`w-8 h-8 rounded-lg flex items-center justify-center transition-all duration-200
                        ${ScreenShare ? 'bg-red-50 border-1 border-red-500 hover:bg-red-100' : 'bg-[#F3F4F6] hover:bg-[#E5E7EB] border-[none]'}`}
                    >
                        <div className="flex items-center justify-center w-5 h-5">
                            <ScreenShareIcon isActive={ScreenShare} />
                        </div>
                    </button>
                )}

                <button
                    onClick={() => handleTabControl("CHAT")}
                    className={`w-8 h-8 rounded-lg flex items-center justify-center relative
              ${ChatOpen ? 'bg-white border-1 border-[#1C64F2]' : 'bg-[#F3F4F6] border-[none]'}`}
                >
                    <div className="flex items-center justify-center w-5 h-5">
                        {ChatOpen ? <ActiveChat /> : <ChatIcon />}
                    </div>
                    {UnreadCount > 0 && (
                        <span className="absolute top-0 right-0 bg-[#ff4444] text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                            {UnreadCount}
                        </span>
                    )}
                </button>

                <button
                    onClick={() => handleTabControl("MEMBERS")}
                    className={`w-8 h-8 rounded-lg flex items-center justify-center relative
              ${MembersOpen ? 'bg-white border-1 border-[#1C64F2]' : 'bg-[#F3F4F6] border-[none]'}`}
                >
                    <div className="flex items-center justify-center w-5 h-5">
                        {MembersOpen ? <ActiveMember /> : <UsersIcon />}
                    </div>
                    {userscount > 0 && (
                        <span className="absolute top-0 right-0 bg-[#ff4444] text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                            {userscount}
                        </span>
                    )}
                </button>

                {/* <button
            onClick={handleEndSession}
            className="w-8 h-8 rounded-lg relative flex items-center justify-center bg-[#FDF2F2] hover:bg-[#ff6666] hover:text-white"
          >
            <EndCallIcon />
          </button> */}

                <div className="relative" ref={menuRef}>
                    <button
                        onClick={toggleMenu}
                        className={`w-8 h-8 rounded-lg flex items-center justify-center ${showMenu ? 'bg-white border-1 border-[#1C64F2]' : 'bg-[#F3F4F6] hover:bg-[#E5E7EB] border-[none]'}`}
                    >
                        <div className="flex items-center justify-center w-5 h-5">
                            {showMenu ? <ActiveDropdown /> : (
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                                </svg>
                            )}
                        </div>
                    </button>

                    {showMenu && (
                        <div className="absolute top-10 right-0 bg-white rounded-lg shadow-lg px-2 w-40 z-[99999999]">

                            <button
                                onClick={toggleFullscreen}
                                className="flex items-center gap-2 w-full text-left p-2 hover:bg-gray-100 rounded-md text-[14px]"
                            >
                                <FullScreenIcon />
                                <span>Full Screen</span>
                            </button>

                            {/* Hide Controls option - only show for non-default project types */}
                            {SessionDetails?.type !== 'default' && (
                                <button
                                    onClick={() => {
                                        handleInvertControls();
                                        setShowMenu(false);
                                    }}
                                    className="flex items-center gap-2 w-full text-left p-2 hover:bg-gray-100 rounded-md text-[14px]"
                                >
                                    <EyeIcon />
                                    <span>Hide Controls</span>
                                </button>
                            )}

                            {/* LarkXR Controls - only show for LarkXR sessions */}
                            {SessionDetails?.type === 'lark' && (
                                <>
                                    {/* Restart App */}
                                    {/* <button
                                        onClick={() => {
                                            if (window.confirm('Please confirm whether to restart the application')) {
                                                console.log('Restarting LarkXR application...');
                                                // Handle restart functionality
                                            }
                                            setShowMenu(false);
                                        }}
                                        className="flex items-center gap-2 w-full text-left p-2 hover:bg-gray-100 text-[14px] rounded-md"
                                    >
                                         <svg width="14" height="14" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
                                        <span>Restart App</span>
                                    </button> */}

                                    {/* Settings */}
                                    {/* <button
                                        onClick={() => {
                                            handleOpenModal('settings', { larksr: null, isConnected: true });
                                            setShowMenu(false);
                                        }}
                                        className="flex items-center gap-2 w-full text-left p-2 hover:bg-gray-100 text-[14px] rounded-md"
                                    >
                                        <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
                                        <span>Settings</span>
                                    </button> */}

                                    {/* Network */}
                                    <button
                                        onClick={() => {
                                            handleOpenModal('network', { larksr: null, isConnected: true });
                                            setShowMenu(false);
                                        }}
                                        className="flex items-center gap-2 w-full text-left p-2 hover:bg-gray-100 text-[14px] rounded-md"
                                    >
                                        <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M7 0.5C5.61553 0.5 4.26215 0.910543 3.11101 1.67971C1.95987 2.44888 1.06266 3.54213 0.532846 4.82121C0.00303299 6.1003 -0.13559 7.50776 0.134506 8.86563C0.404603 10.2235 1.07129 11.4708 2.05026 12.4497C3.02922 13.4287 4.2765 14.0954 5.63437 14.3655C6.99224 14.6356 8.3997 14.497 9.67879 13.9672C10.9579 13.4373 12.0511 12.5401 12.8203 11.389C13.5895 10.2378 14 8.88447 14 7.5C13.998 5.64411 13.2598 3.86482 11.9475 2.5525C10.6352 1.24019 8.85589 0.502038 7 0.5ZM6.65 3.3C6.85767 3.3 7.06068 3.36158 7.23335 3.47696C7.40602 3.59233 7.5406 3.75632 7.62008 3.94818C7.69955 4.14004 7.72034 4.35116 7.67983 4.55484C7.63931 4.75852 7.53931 4.94562 7.39246 5.09246C7.24562 5.23931 7.05853 5.33931 6.85485 5.37982C6.65117 5.42034 6.44005 5.39954 6.24818 5.32007C6.05632 5.2406 5.89233 5.10602 5.77696 4.93335C5.66158 4.76068 5.6 4.55767 5.6 4.35C5.6 4.07152 5.71063 3.80445 5.90754 3.60754C6.10445 3.41062 6.37152 3.3 6.65 3.3ZM8.4 11H5.6C5.41435 11 5.2363 10.9262 5.10503 10.795C4.97375 10.6637 4.9 10.4856 4.9 10.3C4.9 10.1143 4.97375 9.9363 5.10503 9.80502C5.2363 9.67375 5.41435 9.6 5.6 9.6H6.3V7.5H5.6C5.41435 7.5 5.2363 7.42625 5.10503 7.29497C4.97375 7.1637 4.9 6.98565 4.9 6.8C4.9 6.61435 4.97375 6.4363 5.10503 6.30502C5.2363 6.17375 5.41435 6.1 5.6 6.1H7C7.18565 6.1 7.3637 6.17375 7.49498 6.30502C7.62625 6.4363 7.7 6.61435 7.7 6.8V9.6H8.4C8.58565 9.6 8.7637 9.67375 8.89498 9.80502C9.02625 9.9363 9.1 10.1143 9.1 10.3C9.1 10.4856 9.02625 10.6637 8.89498 10.795C8.7637 10.9262 8.58565 11 8.4 11Z" fill="#6B7280"/>
        </svg>
                                        <span>info</span>
                                    </button>

                                    {/* Help */}
                                    <button
                                        onClick={() => {
                                            handleOpenModal('help', { larksr: null, isConnected: true });
                                            setShowMenu(false);
                                        }}
                                        className="flex items-center gap-2 w-full text-left p-2 hover:bg-gray-100 text-[14px] rounded-md"
                                    >
                                        <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M7 0C5.61553 0 4.26215 0.410543 3.11101 1.17971C1.95987 1.94888 1.06266 3.04213 0.532846 4.32121C0.00303299 5.6003 -0.13559 7.00776 0.134506 8.36563C0.404603 9.7235 1.07129 10.9708 2.05026 11.9497C3.02922 12.9287 4.2765 13.5954 5.63437 13.8655C6.99224 14.1356 8.3997 13.997 9.67879 13.4672C10.9579 12.9373 12.0511 12.0401 12.8203 10.889C13.5895 9.73784 14 8.38447 14 7C13.998 5.14411 13.2598 3.36482 11.9475 2.0525C10.6352 0.74019 8.85589 0.00203812 7 0ZM7 11.2C6.86156 11.2 6.72622 11.1589 6.6111 11.082C6.49599 11.0051 6.40627 10.8958 6.35329 10.7679C6.3003 10.64 6.28644 10.4992 6.31345 10.3634C6.34046 10.2276 6.40713 10.1029 6.50503 10.005C6.60292 9.90713 6.72765 9.84046 6.86344 9.81345C6.99923 9.78644 7.13997 9.8003 7.26788 9.85328C7.39579 9.90626 7.50511 9.99598 7.58203 10.1111C7.65895 10.2262 7.7 10.3616 7.7 10.5C7.7 10.6856 7.62625 10.8637 7.49498 10.995C7.3637 11.1262 7.18565 11.2 7 11.2ZM7.7 8.0262V8.4C7.7 8.58565 7.62625 8.7637 7.49498 8.89497C7.3637 9.02625 7.18565 9.1 7 9.1C6.81435 9.1 6.6363 9.02625 6.50503 8.89497C6.37375 8.7637 6.3 8.58565 6.3 8.4V7.4074C6.3 7.31314 6.31903 7.21985 6.35596 7.13312C6.39288 7.0464 6.44695 6.96802 6.5149 6.9027C6.58234 6.83697 6.66255 6.78579 6.75057 6.75232C6.83858 6.71885 6.93253 6.7038 7.0266 6.7081C7.16193 6.713 7.29686 6.69051 7.42328 6.64198C7.54971 6.59344 7.66502 6.51986 7.7623 6.42565C7.85958 6.33145 7.93682 6.21855 7.98939 6.09375C8.04195 5.96895 8.06876 5.83482 8.0682 5.6994C8.07897 5.42092 7.97867 5.14957 7.78937 4.94504C7.60007 4.74052 7.33728 4.61957 7.0588 4.6088C6.78032 4.59803 6.50898 4.69833 6.30445 4.88763C6.09992 5.07693 5.97897 5.33972 5.9682 5.6182C5.96599 5.71044 5.9453 5.8013 5.90737 5.88541C5.86943 5.96951 5.81502 6.04516 5.74734 6.10788C5.67967 6.17059 5.6001 6.2191 5.51336 6.25054C5.42661 6.28197 5.33444 6.2957 5.2423 6.2909C5.05698 6.28361 4.88212 6.20308 4.75612 6.06698C4.63013 5.93087 4.5633 5.75033 4.5703 5.565C4.59018 5.11486 4.7338 4.67892 4.98537 4.30512C5.23695 3.93131 5.58674 3.63413 5.99627 3.44624C6.4058 3.25835 6.85922 3.18703 7.30666 3.24013C7.75409 3.29324 8.17822 3.4687 8.5324 3.74722C8.88658 4.02575 9.15709 4.39655 9.31418 4.81886C9.47128 5.24116 9.50887 5.69861 9.42284 6.14089C9.3368 6.58318 9.13046 6.99317 8.82651 7.32579C8.52255 7.6584 8.13276 7.90076 7.7 8.0262Z" fill="#6B7280"/>
                                                </svg>
                                        <span>Help</span>
                                    </button>
                                </>
                            )}

                            {/* Stop Sharing option - only shown when a project is being shared AND not a guest */}
                            {!isGuest && SessionDetails?.project_id &&
                             SessionDetails.project_id !== 'undefined' &&
                             SessionDetails.project_id !== null &&
                             SessionDetails.type !== 'default' && (
                                <button
                                    onClick={() => {
                                        handleStopSharingWithModal();
                                        setShowMenu(false);
                                    }}
                                    className="flex items-center gap-2 w-full text-left p-2 hover:bg-gray-100 rounded-md text-[14px]"
                                >
                                        <StopScreenShareIcon />
                                    <span>Stop Sharing</span>
                                </button>
                            )}

                            {/* End Session button - always shown */}
                            <button
                                onClick={() => {
                                    handleEndSessionWithModal();
                                    setShowMenu(false);
                                }}
                                className="flex items-center gap-2 w-full text-left p-2 hover:bg-gray-100 rounded-md text-[14px] text-red-500"
                            >
                                <EndCallIcon />
                                <span>End Session</span>
                            </button>
                        </div>
                    )}
                </div>

            </div>

            {/* Share Project Button or Project Name at bottom */}
            {!isGuest && (
                <div className="fixed bottom-7 left-0 right-0 px-4 z-10">
                    {/* Check if a project is being shared */}
                    {SessionDetails?.project_id &&
                     SessionDetails.project_id !== 'undefined' &&
                     SessionDetails.project_id !== null &&
                     SessionDetails.type !== 'default' ? (
                        <button
                            onClick={handleShareProject}
                            className="bg-white w-full p-3 rounded-lg shadow-md flex items-center justify-between"
                        >
                            <div className="flex items-center">
                                <div className="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center mr-3">
                                    <svg width="16" height="16" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M9.35883 0.603879C9.20615 0.670162 9.07018 0.763677 8.95586 0.881838L4.04027 5.96277C3.55965 6.45955 3.57268 7.2495 4.06943 7.73016C4.56618 8.21082 5.35607 8.19779 5.83669 7.701L8.65526 4.78763L8.83233 15.5195C8.84374 16.2107 9.41288 16.7602 10.1028 16.7488C10.7927 16.7374 11.3434 16.1695 11.332 15.4783L11.1549 4.74639L14.0681 7.56519C14.5648 8.04585 15.3547 8.03281 15.8353 7.53603C16.075 7.28827 16.1922 6.96627 16.1869 6.64629C16.1817 6.3263 16.0539 6.00834 15.8062 5.76863L10.7256 0.852637C10.6087 0.738292 10.4685 0.649334 10.3137 0.588124C10.0067 0.466914 9.66171 0.472606 9.35883 0.603879Z" fill="#1C64F2" />
                                        <path d="M17.5399 12.8753L17.6018 16.6251C17.6131 17.315 17.0612 17.8843 16.3726 17.8956L3.87426 18.1019C3.1856 18.1132 2.61519 17.5625 2.6038 16.8725L2.54193 13.1228C2.53053 12.4316 1.96139 11.8821 1.27148 11.8935C0.581575 11.9048 0.0308697 12.4728 0.0422745 13.164L0.104145 16.9138C0.138256 18.9812 1.84829 20.6358 3.91551 20.6017L16.4138 20.3955C18.481 20.3614 20.1355 18.6512 20.1014 16.5838L20.0396 12.8341C20.0281 12.1428 19.459 11.5934 18.7691 11.6048C18.0792 11.6161 17.5285 12.1841 17.5399 12.8753Z" fill="#1C64F2" />
                                    </svg>
                                </div>
                                <span className="text-gray-700 font-medium truncate max-w-[200px]">
                                    {SessionDetails?.projectName || "Project"}
                                </span>
                            </div>
                            <svg width="12" height="8" viewBox="0 0 12 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M1 1.5L6 6.5L11 1.5" stroke="#1C64F2" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                        </button>
                    ) : (
                        <button
                            onClick={handleShareProject}
                            disabled={switchingProject}
                            className={`bg-[#1C64F2] hover:bg-blue-600 text-white font-medium text-[14px] h-[41px] py-3 px-4 rounded-lg w-full flex items-center justify-center ${switchingProject ? 'opacity-75' : ''}`}
                        >
                            {switchingProject ? (
                                <>
                                    <span className="mr-2 animate-spin inline-block h-4 w-4 rounded-full border-1 border-white border-t-transparent"></span>
                                    Switching...
                                </>
                            ) : (
                                <>
                                    <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg" className="mr-2">
                                        <path d="M9.35883 0.603879C9.20615 0.670162 9.07018 0.763677 8.95586 0.881838L4.04027 5.96277C3.55965 6.45955 3.57268 7.2495 4.06943 7.73016C4.56618 8.21082 5.35607 8.19779 5.83669 7.701L8.65526 4.78763L8.83233 15.5195C8.84374 16.2107 9.41288 16.7602 10.1028 16.7488C10.7927 16.7374 11.3434 16.1695 11.332 15.4783L11.1549 4.74639L14.0681 7.56519C14.5648 8.04585 15.3547 8.03281 15.8353 7.53603C16.075 7.28827 16.1922 6.96627 16.1869 6.64629C16.1817 6.3263 16.0539 6.00834 15.8062 5.76863L10.7256 0.852637C10.6087 0.738292 10.4685 0.649334 10.3137 0.588124C10.0067 0.466914 9.66171 0.472606 9.35883 0.603879Z" fill="white" />
                                        <path d="M17.5399 12.8753L17.6018 16.6251C17.6131 17.315 17.0612 17.8843 16.3726 17.8956L3.87426 18.1019C3.1856 18.1132 2.61519 17.5625 2.6038 16.8725L2.54193 13.1228C2.53053 12.4316 1.96139 11.8821 1.27148 11.8935C0.581575 11.9048 0.0308697 12.4728 0.0422745 13.164L0.104145 16.9138C0.138256 18.9812 1.84829 20.6358 3.91551 20.6017L16.4138 20.3955C18.481 20.3614 20.1355 18.6512 20.1014 16.5838L20.0396 12.8341C20.0281 12.1428 19.459 11.5934 18.7691 11.6048C18.0792 11.6161 17.5285 12.1841 17.5399 12.8753Z" fill="white" />
                                    </svg>
                                    Share Project
                                </>
                            )}
                        </button>
                    )}
                </div>
            )}



            {/* LarkXR Modals */}
            {activeModal === 'settings' && (
                <LarkXRSettingsModal
                    larksr={modalProps.larksr}
                    isConnected={modalProps.isConnected}
                    onClose={handleCloseModal}
                />
            )}

            {activeModal === 'network' && (
                <LarkXRNetworkModal
                    larksr={modalProps.larksr}
                    isConnected={modalProps.isConnected}
                    onClose={handleCloseModal}
                />
            )}

            {activeModal === 'help' && (
                <LarkXRHelpModal onClose={handleCloseModal} />
            )}
        </div>
    );
};

export default MobileControls;