import React, { Component } from "react";
import * as Sessions from '../../../Actions/Sessions'
import { connect } from "react-redux";
import Fire from "../../../config/Firebase";
import LoaderOverlay from "../../../components/LoaderOverlay";

class AleGuest extends Component {
  constructor(props) {
    super(props);
    this.state = {
      ale_link: "",
      loading: true,
      error: null,
      iframeLoaded: false,
      lastRouteUpdate: null
    }
    this.initialSrc = null; // Track initial src to prevent iframe reloads
  }

  componentDidMount() {
    console.log("ALE Guest - Component mounted, roomId:", this.props.roomId);

    // Set up custom message subscription for non-route messages
    this.props.SubscribeToCustom((msg) => {
      try {
        const data = JSON.parse(msg);
        
        // Don't forward route changes (they're handled by Firebase)
        if (data.actiontype !== "routechange") {
          const iframe = document.getElementById('showcase_frame');
          if (iframe?.contentWindow) {
            iframe.contentWindow.postMessage({ ...data, simulate: true }, "*");
          }
        }
      } catch (error) {
        console.error("ALE Guest - Error processing custom message:", error);
      }
    });

    // Set loading timeout
    this.loadingTimeout = setTimeout(() => {
      if (this.state.loading) {
        console.error("ALE Guest - Loading timeout reached");
        this.setState({
          error: "Loading timeout: Please wait for the host to start the experience.",
          loading: false,
        });
      }
    }, 30000);

    this.setupConfigListener();
  }

  componentWillUnmount() {
    if (this.loadingTimeout) {
      clearTimeout(this.loadingTimeout);
    }
    if (this.configUnsubscribe) {
      this.configUnsubscribe();
    }
  }

  setupConfigListener = () => {
    console.log("ALE Guest - Setting up config listener...");

    this.configUnsubscribe = Fire.firestore()
      .collection('sessions')
      .doc(this.props.roomId)
      .collection("config")
      .doc("data")
      .onSnapshot(
        { includeMetadataChanges: true },
        (doc) => {
          console.log("ALE Guest - Config updated:", doc.exists, doc.data());

          if (!doc.exists) {
            console.log("ALE Guest - Config document doesn't exist, waiting...");
            return;
          }

          const configData = doc.data();

          // Handle project switching
          if (configData?.projectSwitching) {
            console.log("ALE Guest - Project switching detected");
            this.initialSrc = null; // Reset initial src for project switch
            this.setState({
              ale_link: "",
              loading: true,
              error: null,
              iframeLoaded: false,
              lastRouteUpdate: null
            });

            // Reset timeout for project switch
            if (this.loadingTimeout) {
              clearTimeout(this.loadingTimeout);
            }
            this.loadingTimeout = setTimeout(() => {
              if (this.state.loading) {
                this.setState({
                  error: "Project switch timeout: Please wait for the host.",
                  loading: false,
                });
              }
            }, 30000);

            return;
          }

          // Handle ALE link updates
          if (configData?.ale_link) {
            console.log("ALE Guest - Processing ALE link:", configData.ale_link);

            // Add guest parameter
            const guestParam = configData.ale_link.includes("?") ? "&salestoolGuest=true" : "?salestoolGuest=true";
            const ale_link_with_guest = configData.ale_link + guestParam;

            console.log("ALE Guest - Final guest URL:", ale_link_with_guest);

            // Check if this is the initial load (no existing link)
            if (!this.state.ale_link) {
              console.log("ALE Guest - Initial load, setting iframe src");
              this.setState({
                ale_link: ale_link_with_guest,
                loading: false,
                error: null,
                iframeLoaded: false
              });

              // Clear loading timeout
              if (this.loadingTimeout) {
                clearTimeout(this.loadingTimeout);
                this.loadingTimeout = null;
              }
              return;
            }

            // For any subsequent updates, always use postMessage (never reload iframe)
            if (this.state.ale_link && this.state.iframeLoaded) {
              console.log("ALE Guest - Route update detected, using postMessage");
              
              // Send route change to iframe without reloading
              const iframe = document.getElementById('showcase_frame');
              if (iframe?.contentWindow) {
                const routeMessage = {
                  actiontype: "routechange",
                  url: ale_link_with_guest,
                  simulate: true,
                  timestamp: Date.now()
                };
                
                console.log("ALE Guest - Sending route change to iframe:", routeMessage);
                iframe.contentWindow.postMessage(routeMessage, '*');
              }

              // Update state without causing iframe reload
              this.setState({
                ale_link: ale_link_with_guest,
                lastRouteUpdate: ale_link_with_guest,
                loading: false,
                error: null
              });

              return;
            }

            // Fallback for edge cases - but this should rarely happen
            console.log("ALE Guest - Fallback case, iframe not loaded yet");
            this.setState({
              ale_link: ale_link_with_guest,
              loading: false,
              error: null
            });

          } else if (configData?.ale_link === null) {
            // Host cleared the link
            console.log("ALE Guest - Host cleared ALE link");
            this.initialSrc = null; // Reset initial src when host clears link
            this.setState({
              ale_link: "",
              loading: true,
              error: null,
              iframeLoaded: false,
              lastRouteUpdate: null
            });
          }
        },
        (error) => {
          console.error("ALE Guest - Firebase listener error:", error);
          this.setState({
            error: "Connection error: " + error.message,
            loading: false,
          });
        }
      );
  }

  handleIframeLoad = () => {
    console.log("ALE Guest - Iframe loaded successfully");
    this.setState({ iframeLoaded: true });
  }

  handleIframeError = () => {
    console.error("ALE Guest - Iframe failed to load");
    this.setState({ 
      iframeLoaded: false,
      error: "Failed to load the virtual experience"
    });
  }

  render() {
    const { ale_link, loading, error } = this.state;

    console.log("ALE Guest - Rendering:", { hasLink: !!ale_link, loading, error });

    if (loading) {
      const isProjectSwitch = !!this.previousAleLink && !ale_link;
      
      return (
        <LoaderOverlay
          title={isProjectSwitch ? "Switching Project" : "Preparing Virtual Experience"}
          message={isProjectSwitch ? 
            "The host is switching projects. Please wait..." : 
            "Waiting for the host to start the experience..."
          }
        />
      );
    }

    if (error) {
      return (
        <LoaderOverlay
          title="Unable to Load Experience"
          message={error}
        />
      );
    }

    // Track previous link for project switch detection
    if (ale_link && ale_link !== this.previousAleLink) {
      this.previousAleLink = ale_link;
    }

    // Use initialSrc to prevent iframe reloads - only set src once!
    if (!this.initialSrc && ale_link) {
      this.initialSrc = ale_link;
    }

    return (
      <iframe
        id="showcase_frame"
        title="ALE Virtual Experience"
        src={this.initialSrc || ale_link}
        style={{ 
          width: "100%", 
          height: "100%", 
          position: "absolute",
          pointerEvents: "none"
        }}
        onLoad={this.handleIframeLoad}
        onError={this.handleIframeError}
      />
    );
  }
}

const mapStateToProps = state => {
  return {
    configDetails: state.Sessions.configData,
  }
}

const mapDispatchToProps = {
  ...Sessions
}

export default connect(mapStateToProps, mapDispatchToProps)(AleGuest);