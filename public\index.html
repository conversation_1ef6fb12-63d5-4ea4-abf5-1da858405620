<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <meta name="theme-color" content="#000000">
  <meta http-equiv='cache-control' content='no-cache'>

  <link rel="stylesheet" href="https://cdn.propvr.tech/css/font-awesome.min.css" />

  <link rel="icon" id="favicon" href="./favicon.ico" type="image/x-icon">
  <link rel="stylesheet" type="text/css" href="https://fonts.googleapis.com/css?family=Open+Sans" />
  <script src="https://cdn.propvr.tech/js/jquery-3.3.1.min.js" crossorigin="anonymous"></script>
  <script defer type="module" src="https://unpkg.com/@google/model-viewer/dist/model-viewer.min.js"></script>
  <script defer nomodule src="https://unpkg.com/@google/model-viewer/dist/model-viewer-legacy.js"></script>
  <script src="https://cdn.propvr.tech/js/popper.min.js" crossorigin="anonymous"></script>
  <script src="https://cdn.propvr.tech/js/bootstrap-material-design.min.js" crossorigin="anonymous"></script>
  <script src="https://apis.google.com/js/api.js" crossorigin="anonymous"></script>
  <script src="https://cdn.propvr.tech/js/selectpicker.min.js" crossorigin="anonymous"></script>
  <script src="https://cdn.propvr.tech/js/material-kit.min.js?v=2.0.7" crossorigin="anonymous"></script>
  <script src="https://static.matterport.com/showcase-sdk/latest.js"></script>
  <script src="https://cdn.propvr.tech/js/salestoolaframe.min.js"></script>
  <script src="https://cdn.jsdelivr.net/gh/donmccurdy/aframe-extras@v6.1.1/dist/aframe-extras.min.js"></script>
  <script src=https://cdn.propvr.tech/js/aframe-stereo.min.js></script>


  <script src="https://cdn.propvr.tech/js/aframe-look-at-component.min.js"></script>
  <script src="https://cdn.propvr.tech/js/aframe-gif-shader.min.js"></script>
  <script src="https://cdn.propvr.tech/exterior/js/htmlembedcomp.min.js"></script>
  <script src=https://cdn.propvr.tech/js/aframe-chromakey-material.min.js></script>
  <script src=https://cdn.propvr.tech/js/aframe-chromakey-material.min.js></script>
  <script src="https://cdn.tailwindcss.com"></script>
  <title>
    Propvr
  </title>
</head>
<style id="3dcards">
  /*     :root{
          --navcolor:#fff;
        } */

  @font-face {
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/opensans/v18/mem8YaGs126MiZpBA-UFWJ0bbck.woff2) format('woff2');
    unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
  }

  /* cyrillic */
  @font-face {
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/opensans/v18/mem8YaGs126MiZpBA-UFUZ0bbck.woff2) format('woff2');
    unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
  }

  /* greek-ext */
  @font-face {
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/opensans/v18/mem8YaGs126MiZpBA-UFWZ0bbck.woff2) format('woff2');
    unicode-range: U+1F00-1FFF;
  }

  /* greek */
  @font-face {
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/opensans/v18/mem8YaGs126MiZpBA-UFVp0bbck.woff2) format('woff2');
    unicode-range: U+0370-03FF;
  }

  /* vietnamese */
  @font-face {
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/opensans/v18/mem8YaGs126MiZpBA-UFWp0bbck.woff2) format('woff2');
    unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB;
  }

  /* latin-ext */
  @font-face {
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/opensans/v18/mem8YaGs126MiZpBA-UFW50bbck.woff2) format('woff2');
    unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
  }

  /* latin */
  @font-face {
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/opensans/v18/mem8YaGs126MiZpBA-UFVZ0b.woff2) format('woff2');
    unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  }




  .main_banner {
    /*         animation-duration: 1s;
           animation-fill-mode: both; */
    display: flex !important;

  }

  .stick {
    width: 2px;
    height: 150px;
    background: #fff;
  }

  .des_banner {
    min-width: 105px;
    font-size: 16px;
  }

  .des_banner,
  .min_banner {
    padding: 5px 10px 5px 10px;
    font-family: 'Open Sans';
    text-align: left;
    white-space: nowrap;
    filter: brightness(0.7) contrast(1.5);
    text-transform: capitalize;
    width: max-content;
  }


  .min_banner {
    margin-top: 3px;
    min-width: 90px;
    font-size: 13px
  }

  .Salestool_Embed {
    background-color: #000000a8;
    color: white;
    font-size: 40px;
    filter: contrast(1.6);
    display: flex;
    flex-direction: column !important;
    width: 70px;
    height: fit-content;

    /*     height: 100px; */
    flex-flow: nowrap;
  }

  .Salestool_Embed .RTC {
    display: flex;
    flex-direction: row;
    text-align: center;
    height: 64px;
  }

  .Salestool_Embed .RTC .offcontrol {
    flex: auto;
    padding: 10px;
    background: rgb(255 10 75);
    margin: 10px;
    border-radius: 4px;
    height: 24px;
    width: 24px;
    display: flex;
    justify-content: center;
  }

  .Salestool_Embed .RTC .oncontrol {
    flex: auto;
    padding: 10px;
    background: #fff;
    margin: 10px;
    border-radius: 4px;
    height: 24px;
    width: 24px;
    display: flex;
    justify-content: center;
  }

  .Salestool_Embed>.RTC>.offcontrol>svg>path,
  g {
    /* fill:#fff; */
  }

  .Salestool_Embed>.RTC>.offcontrol>svg>g {}

  .Salestool_Embed>.RTC>.oncontrol>svg>g {
    fill: #2979FF;
  }

  .EmbedNameDiv {
    background-color: #2979FF;
    color: #fff;
    border-radius: 20px;
    font-family: 'Open Sans';
    font-size: 20px;
    max-width: 300px;
    overflow: hidden;
    white-space: pre;
    width: fit-content;
    height: fit-content;


  }

  .EmbedName {
    margin: 4px 14px;
  }
  #log {
    width: 100%;
    height: 300px;
    overflow-y: scroll;
    background-color: transparent;
    font-family: monospace;
    padding: 10px;
    position: fixed;
    max-width:40%;
    top: 0;
    display:none;
    left: 0;
}

#log p {
    margin: 0;
    padding: 0;
}

</style>

<body>
  <img id="Reseller_Whitelogo" src="https://cdn.propvr.tech/images/logo/logo_propvr_verticalwhite.png" width="0" height="0" style="display:none"> <img id="ResellerBgImage" src="https://storagecdn.propvr.tech/assets/loginBG.jpg" width="0" height="0" style="display:none"> <img id="ResellerHeader" src="Create, Collaborate and Share" width="0" height="0" style="display:none"> <img id="Reseller_logo" src="https://cdn.propvr.tech/images/logo/logo_propvr_vertical.png" width="0" height="0" style="display:none"><img id="ResellerText" src="Prop VR allows you to create, collaborate and share virtual tour experience of your properties with your customers. Take your customers for a virtual-guided tour and increase sales." width="0" height="0" style="display:none">


  <div id="root" sstyle="--main-color:#3366ff;--main-color-hover:#598BFF;--main-white-image:&quot;url(&quot;https://cdn.propvr.tech/images/logo/logo_propvr_verticalwhite.png&quot;)&quot;;--main-image:&quot;url(&quot;https://cdn.propvr.tech/images/logo/logo_propvr_vertical.png&quot;)&quot;">
    <div style="width: 100%; height: 100%;">
      <div style="margin: auto; width: 50px; position: relative; height: 50px; top: calc(50% - 80px);">
        <div class="lds-ring">
          <div></div>
          <div></div>
          <div></div>
          <div></div>
        </div>
      </div>
    </div>
  </div>
  <audio muted src="https://storagecdn.propvr.tech/sounds%2Fpresentmode.mp3" id="JoiningAudio">

  </audio>
  <div id="log"></div>

  <script>
    // if (window.location.protocol == "http:") {
    //   window.location.protocol = "https:"
    // }

  </script>
</body>

</html>