import React from "react";

// import PDFViewer from 'mgr-pdf-viewer-react'

class Pdfviewer extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      url: '/salestool/pdf/pdf.html?data=' + this.props.data + '&type=host',
      screenHeight: window.innerHeight,
      isLandscape: window.innerWidth > window.innerHeight,
    }
    this.ExamplePDFViewer = this.ExamplePDFViewer.bind(this);
    this.writeHTML = React.createRef();
    this.accessiframe = this.accessiframe.bind(this);
    this.next = this.next.bind(this);
    this.prev = this.prev.bind(this);

  }


  updateScreenHeight = () => {
    this.setState({
      screenHeight: window.innerHeight
    });
  };

  handleOrientationChange = () => {
    const isLandscape = window.innerWidth > window.innerHeight;
    this.setState({ isLandscape });
  };
  componentDidMount() {
    // Add an event listener to update the screen height on window resize
    window.addEventListener('resize', this.updateScreenHeight);
    window.addEventListener('resize', this.handleOrientationChange);
  }

  componentWillUnmount() {
    // Remove the event listener when the component is unmounted to avoid memory leaks
    window.removeEventListener('resize', this.updateScreenHeight);
    window.removeEventListener('resize', this.handleOrientationChange);
  }
  componentDidMount() {
    window.scrollTo(0, 0);
  }
  accessiframe(event) {

    var thumbslider = setInterval(() => {
      let doc = document.getElementById('iframe').contentWindow;
      if (doc != null && this.props.pdf) {

        var a = this.writeHTML.current.contentWindow;
        if (a.document.getElementsByClassName('df-ui-next')[0]) {
          a.document.getElementsByClassName('df-ui-next')[0].addEventListener("click", this.next);
          a.document.getElementsByClassName('df-ui-next')[1].addEventListener("click", this.next);
        }

        if (a.document.getElementsByClassName('df-ui-prev')[0]) {
          a.document.getElementsByClassName('df-ui-prev')[0].addEventListener("click", this.prev);
          a.document.getElementsByClassName('df-ui-prev')[1].addEventListener("click", this.prev);
          clearInterval(thumbslider);
        }

      }

    }, 1000)

  }

  next(event) {

    this.props.senddata({ actiontype: "pdf_next", roomid: this.props.room, data: "next" });
  }

  prev(event) {
    this.props.senddata({ actiontype: "pdf_prev", roomid: this.props.room, data: "prev" });
  }

  componentDidUpdate(nextProps) {
    if (this.props.data !== nextProps.data) {

      this.setState({
        url: '/salestool/pdf/pdf.html?data=' + this.props.data + '&type=host'
      })
      this.accessiframe();
      // this.writeHTML();

    }
  }

  ExamplePDFViewer() {
    // const style={
    //   height:'300px'
    // }

    const iframe = {
      height: "100%",
      width: "100%",
      border: "none",
      background: "#fff"
    }

    return (
      <iframe id="iframe" ref={this.writeHTML}  className="border-b-[inherit] w-full h-full rounded" src={this.state.url}></iframe>
      // <PDFViewer
      //     document={{
      //         url: this.props.data,
      //         page:2
      //     }}
      //     css="pdf_height"
      //     navigation={{
      //       css: {
      //         previousPageBtn:'pdf_navigation_host',  
      //         nextPageBtn:'pdf_navigation_host', 
      //     }

      //     }}
      // />
    )
  }










  render() {

    const modal = {
      width: "100%",
      height: "100%",
      maxWidth: "100%",
      margin: "0",
      padding: "0"
    }

    const modal_content = {
      height: "95%",
      width: "95%",
      borderRadius: "10px!important"
    }

    const modal_body = {
      height: "100%",
      width: "100%",
      border: "none"
    }


    return (
      <div style={{ display: this.props.pdf === true ? 'block' : 'none' }} className="bg-[black]/80 bg-opacity-80 fixed z-[10501] overflow-hidden w-full h-screen inset-0 w-full h-full top-0" id="pdf_modal" tabIndex="-1" role="dialog">
        <div className="w-full h-full max-w-full m-0 p-0 flex justify-center items-center" role="document">
          <div className={` ${!this.state.isLandscape?"h-full w-full rounded":"h-full w-full "} flex flex-col justify-center  flex flex-col w-full pointer-events-auto bg-transparent backdrop-blur-2xl bg-clip-padding m-auto rounded-none sm:rounded-md  border-[none] inset-0 `}>
            <div className="w-full h-full sm:p-3" id="df_manual_book">
              <div className="flex flex-column h-full">
              <div className="h-[15%] sm:h-[10%] flex-auto px-3 sm:px-0 w-full flex justify-between items-center">
                <div className="w-fit sm:px-4 py-2 sm:bg-[#525252] text-sm text-white rounded-sm font-medium leading-normal">
                  pdf.pdf
                </div>
              <button onClick={() => this.props.open_close_pdf('pdf_modal', false)} type="button" className="bg-[#525252] backdrop-blur-2xl float-right shadow w-8 h-8 rounded-full flex justify-center items-center" data-dismiss="modal" aria-label="Close">
                {/* <span style={{ marginLeft: '-50px' }} aria-hidden="true"> */}
                  <svg className="w-6 h-6" viewBox="0 0 32 32" fill="white" xmlns="http://www.w3.org/2000/svg">
                    <path d="M17.191 16L23.7534 9.43756C24.0823 9.10869 24.0823 8.5755 23.7534 8.24666C23.4245 7.91778 22.8913 7.91778 22.5625 8.24666L16 14.8091L9.43756 8.24666C9.10869 7.91778 8.5755 7.91778 8.24666 8.24666C7.91781 8.57553 7.91778 9.10872 8.24666 9.43756L14.8091 16L8.24666 22.5624C7.91778 22.8913 7.91778 23.4245 8.24666 23.7534C8.57553 24.0822 9.10872 24.0822 9.43756 23.7534L16 17.1909L22.5624 23.7534C22.8913 24.0822 23.4245 24.0822 23.7534 23.7534C24.0822 23.4245 24.0822 22.8913 23.7534 22.5624L17.191 16Z" fill="white" />
                  </svg>
                {/* </span> */}
              </button>
              </div>
              <div className="w-full h-full pt-1 flex-auto">
              {this.ExamplePDFViewer()}
              </div>
              </div>
              {/* <ExamplePDFViewer></ExamplePDFViewer> */}
            </div>
          </div>
        </div>
      </div>
    )
  }
}
export default Pdfviewer;
