import React from "react";
import { connect } from "react-redux";
import 'jquery';

class Share extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      url: '',
      tooltip: false,
      shortUrl: ''
    }
    this.handlecopy = this.handlecopy.bind(this);
    this.onClick = this.onClick.bind(this);
    this.handleShare = this.handleShare.bind(this);
  }

  onClick() {
    this.setState({
      tooltip: false
    });
    this.props.open_close('share', false);

    // Call onDismiss if provided
    if (this.props.onDismiss) {
      this.props.onDismiss();
    }
  }

  handlecopy() {
    navigator.clipboard.writeText(this.state.shortUrl || encodeURI(this.state.url));
    this.setState({
      tooltip: true
    });
  }

  handleShare() {
    if (navigator.share) {
      navigator.share({
        title: 'Join my meeting',
        text: 'Click the link to join my meeting',
        url: this.state.url,
      })
      .then(() => console.log('Successful share'))
      .catch((error) => console.log('Error sharing', error));
    } else {
      this.handlecopy();
    }
  }

  componentDidMount() {
    window.scrollTo(0, 0);

    const fullUrl = window.location.protocol + '//' + window.location.hostname + (window.location.port.length ? ":" + window.location.port : "") + "/salestool/joinroom/" + this.props.roomId;


    this.setState({
      url: fullUrl,
    });
  }

  render() {
    return (
      <div
        style={{ display: this.props.share === true ? 'flex' : 'none' }}
        className="bg-black/50 fixed z-[10501] inset-0 flex items-center justify-center p-1"
        id="share_modal"
        tabIndex="-1"
        role="dialog"
      >
        <div className="w-[300px] max-w-sm bg-white rounded-2xl shadow-xl overflow-hidden">
          <div className="p-2 md:p-3 flex flex-col">
            <p className="text-[#6B7280] text-[14px] font-medium text-center ">Meeting Link</p>

            <div className="flex items-center bg-gray-100 rounded-lg p-3 mb-4">
              <svg className="w-4 h-4 text-gray-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
              </svg>

              <span className="mx-2 text-[#111928] text-[12px] text-base font-normal truncate flex-grow">
                {this.state.url}
              </span>

              <button
                onClick={this.handlecopy}
                className="flex-shrink-0 text-gray-500 hover:text-gray-700"
                title="Copy link to clipboard"
              >
                <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M3.00024 4.2002H3.33325V4.7998H2.49927V5.2998L2.50024 14.9004V15.4004H10.1663V14.5996H10.8303L10.8293 14.8867C10.8212 15.1822 10.7481 15.4104 10.6057 15.5859L10.5403 15.6582C10.3197 15.8772 10.021 16 9.69458 16H2.94458C2.32848 15.9998 1.83347 15.503 1.83325 14.9004V5.2998C1.83336 4.71267 2.33786 4.2002 3.00024 4.2002ZM9.52368 1H13.0549C13.6712 1.00001 14.166 1.49688 14.1663 2.09961V11.7002C14.1662 12.3031 13.6712 12.7997 13.0559 12.7998H6.27856C5.66224 12.7998 5.16637 12.303 5.16626 11.7002V5.16309C5.16629 4.66065 5.36924 4.18661 5.74536 3.82617L8.10181 1.56348H8.10278C8.47899 1.20182 8.98235 1 9.52368 1ZM12.9915 1.5791L10.4973 1.59473L10.0002 1.59766V4.5C10.0002 5.08722 9.49571 5.59961 8.83325 5.59961H5.83325V12.2002H13.5579L13.5549 11.6973L13.5002 2.09668C13.4999 2.03162 13.488 1.9693 13.467 1.91211C13.4565 1.88349 13.4443 1.85654 13.4309 1.83301C13.4244 1.82152 13.4172 1.81093 13.4104 1.80078C13.4069 1.79561 13.4032 1.78998 13.3997 1.78516C13.3979 1.78275 13.3956 1.78064 13.3938 1.77832C13.393 1.7772 13.3927 1.77552 13.3918 1.77441L13.3899 1.77344V1.77246L13.3889 1.77148L13.2375 1.57812L12.9915 1.5791ZM8.48657 2.07031L6.33228 4.13965L5.43579 5H9.33325V1.25781L8.48657 2.07031Z" fill="#6B7280" stroke="#6B7280"/>
                  </svg>
              </button>
            </div>

            <button
              onClick={this.handleShare}
              className="w-full bg-[#1C64F2] hover:bg-blue-600 text-[14px] text-white font-medium py-2 px-3 rounded-lg mb-2 transition duration-200"
            >
              Share the link
            </button>

            <button
              onClick={this.onClick}
              className="w-full text-[#1C64F2] text-[14px] font-medium py-2 px-3 rounded-lg transition duration-200 hover:bg-blue-100"
            >
              Dismiss
            </button>

            {this.state.tooltip && (
              <div className="mt-1 text-xs text-green-600 font-medium text-center">
                Link copied to clipboard
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }
}

const mapStateToProps = state => {
  return {
    Config: state.Call.config,
  }
}

export default connect(mapStateToProps, null)(Share);