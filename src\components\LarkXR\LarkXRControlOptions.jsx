// src/components/LarkXR/LarkXRControlOptions.jsx
import React, { useState } from 'react';
import { createPortal } from 'react-dom';
import LarkXRSettingsModal from './LarkXRSettingsModal';
import LarkXRHelpModal from './LarkXRHelpModal';
import LarkXRNetworkModal from './LarkXRNetworkModal';

const LarkXRControlOptions = ({
  larksr,
  isConnected,
  onRestartApp,
  showInDropdown = false,
  onClose,
  onOpenModal // Function to handle modal opening at parent level
}) => {
  // Use local modal state as fallback when onOpenModal is not provided
  const [activeModal, setActiveModal] = useState(null);

  const handleRestartApp = () => {
    if (window.confirm('Please confirm whether to restart the application')) {
      console.log('Restarting LarkXR application...');
      if (larksr && larksr.restartCloudApp) {
        larksr.restartCloudApp();
      }
      if (onRestartApp) {
        onRestartApp();
      }
    }
    if (onClose) onClose();
  };

  const handleNetworkInfo = () => {
    console.log('Opening Network modal');
    if (onOpenModal) {
      // Use parent modal management
      onOpenModal('network', { larksr, isConnected });
      if (onClose) onClose();
    } else {
      // Use local modal management as fallback
      setActiveModal('network');
    }
  };

  const handleSettings = () => {
    console.log('Opening Settings modal');
    if (onOpenModal) {
      // Use parent modal management
      onOpenModal('settings', { larksr, isConnected });
      if (onClose) onClose();
    } else {
      // Use local modal management as fallback
      setActiveModal('settings');
    }
  };

  const handleHelp = () => {
    console.log('Opening Help modal');
    if (onOpenModal) {
      // Use parent modal management
      onOpenModal('help', { larksr, isConnected });
      if (onClose) onClose();
    } else {
      // Use local modal management as fallback
      setActiveModal('help');
    }
  };

  const closeModal = () => {
    console.log('Closing modal');
    setActiveModal(null);
  };

  const controlOptions = [
    // {
    //   id: 'restart',
    //   label: 'Restart App',
    //   icon: (
    //     <svg width="14" height="14" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    //       <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
    //     </svg>
    //   ),
    //   onClick: handleRestartApp,
    //   description: 'Restart the cloud application'
    // },
    {
      id: 'network',
      label: 'info',
      icon: (
        <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M7 0.5C5.61553 0.5 4.26215 0.910543 3.11101 1.67971C1.95987 2.44888 1.06266 3.54213 0.532846 4.82121C0.00303299 6.1003 -0.13559 7.50776 0.134506 8.86563C0.404603 10.2235 1.07129 11.4708 2.05026 12.4497C3.02922 13.4287 4.2765 14.0954 5.63437 14.3655C6.99224 14.6356 8.3997 14.497 9.67879 13.9672C10.9579 13.4373 12.0511 12.5401 12.8203 11.389C13.5895 10.2378 14 8.88447 14 7.5C13.998 5.64411 13.2598 3.86482 11.9475 2.5525C10.6352 1.24019 8.85589 0.502038 7 0.5ZM6.65 3.3C6.85767 3.3 7.06068 3.36158 7.23335 3.47696C7.40602 3.59233 7.5406 3.75632 7.62008 3.94818C7.69955 4.14004 7.72034 4.35116 7.67983 4.55484C7.63931 4.75852 7.53931 4.94562 7.39246 5.09246C7.24562 5.23931 7.05853 5.33931 6.85485 5.37982C6.65117 5.42034 6.44005 5.39954 6.24818 5.32007C6.05632 5.2406 5.89233 5.10602 5.77696 4.93335C5.66158 4.76068 5.6 4.55767 5.6 4.35C5.6 4.07152 5.71063 3.80445 5.90754 3.60754C6.10445 3.41062 6.37152 3.3 6.65 3.3ZM8.4 11H5.6C5.41435 11 5.2363 10.9262 5.10503 10.795C4.97375 10.6637 4.9 10.4856 4.9 10.3C4.9 10.1143 4.97375 9.9363 5.10503 9.80502C5.2363 9.67375 5.41435 9.6 5.6 9.6H6.3V7.5H5.6C5.41435 7.5 5.2363 7.42625 5.10503 7.29497C4.97375 7.1637 4.9 6.98565 4.9 6.8C4.9 6.61435 4.97375 6.4363 5.10503 6.30502C5.2363 6.17375 5.41435 6.1 5.6 6.1H7C7.18565 6.1 7.3637 6.17375 7.49498 6.30502C7.62625 6.4363 7.7 6.61435 7.7 6.8V9.6H8.4C8.58565 9.6 8.7637 9.67375 8.89498 9.80502C9.02625 9.9363 9.1 10.1143 9.1 10.3C9.1 10.4856 9.02625 10.6637 8.89498 10.795C8.7637 10.9262 8.58565 11 8.4 11Z" fill="#6B7280"/>
        </svg>
      ),
      onClick: handleNetworkInfo,
      description: 'View network status and diagnostics'
    },
    // {
    //   id: 'settings',
    //   label: 'Settings',
    //   icon: (
    //     <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    //       <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
    //       <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
    //     </svg>
    //   ),
    //   onClick: handleSettings,
    //   description: 'Configure LarkXR settings'
    // },
    {
      id: 'help',
      label: 'Help',
      icon: (
        <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M7 0C5.61553 0 4.26215 0.410543 3.11101 1.17971C1.95987 1.94888 1.06266 3.04213 0.532846 4.32121C0.00303299 5.6003 -0.13559 7.00776 0.134506 8.36563C0.404603 9.7235 1.07129 10.9708 2.05026 11.9497C3.02922 12.9287 4.2765 13.5954 5.63437 13.8655C6.99224 14.1356 8.3997 13.997 9.67879 13.4672C10.9579 12.9373 12.0511 12.0401 12.8203 10.889C13.5895 9.73784 14 8.38447 14 7C13.998 5.14411 13.2598 3.36482 11.9475 2.0525C10.6352 0.74019 8.85589 0.00203812 7 0ZM7 11.2C6.86156 11.2 6.72622 11.1589 6.6111 11.082C6.49599 11.0051 6.40627 10.8958 6.35329 10.7679C6.3003 10.64 6.28644 10.4992 6.31345 10.3634C6.34046 10.2276 6.40713 10.1029 6.50503 10.005C6.60292 9.90713 6.72765 9.84046 6.86344 9.81345C6.99923 9.78644 7.13997 9.8003 7.26788 9.85328C7.39579 9.90626 7.50511 9.99598 7.58203 10.1111C7.65895 10.2262 7.7 10.3616 7.7 10.5C7.7 10.6856 7.62625 10.8637 7.49498 10.995C7.3637 11.1262 7.18565 11.2 7 11.2ZM7.7 8.0262V8.4C7.7 8.58565 7.62625 8.7637 7.49498 8.89497C7.3637 9.02625 7.18565 9.1 7 9.1C6.81435 9.1 6.6363 9.02625 6.50503 8.89497C6.37375 8.7637 6.3 8.58565 6.3 8.4V7.4074C6.3 7.31314 6.31903 7.21985 6.35596 7.13312C6.39288 7.0464 6.44695 6.96802 6.5149 6.9027C6.58234 6.83697 6.66255 6.78579 6.75057 6.75232C6.83858 6.71885 6.93253 6.7038 7.0266 6.7081C7.16193 6.713 7.29686 6.69051 7.42328 6.64198C7.54971 6.59344 7.66502 6.51986 7.7623 6.42565C7.85958 6.33145 7.93682 6.21855 7.98939 6.09375C8.04195 5.96895 8.06876 5.83482 8.0682 5.6994C8.07897 5.42092 7.97867 5.14957 7.78937 4.94504C7.60007 4.74052 7.33728 4.61957 7.0588 4.6088C6.78032 4.59803 6.50898 4.69833 6.30445 4.88763C6.09992 5.07693 5.97897 5.33972 5.9682 5.6182C5.96599 5.71044 5.9453 5.8013 5.90737 5.88541C5.86943 5.96951 5.81502 6.04516 5.74734 6.10788C5.67967 6.17059 5.6001 6.2191 5.51336 6.25054C5.42661 6.28197 5.33444 6.2957 5.2423 6.2909C5.05698 6.28361 4.88212 6.20308 4.75612 6.06698C4.63013 5.93087 4.5633 5.75033 4.5703 5.565C4.59018 5.11486 4.7338 4.67892 4.98537 4.30512C5.23695 3.93131 5.58674 3.63413 5.99627 3.44624C6.4058 3.25835 6.85922 3.18703 7.30666 3.24013C7.75409 3.29324 8.17822 3.4687 8.5324 3.74722C8.88658 4.02575 9.15709 4.39655 9.31418 4.81886C9.47128 5.24116 9.50887 5.69861 9.42284 6.14089C9.3368 6.58318 9.13046 6.99317 8.82651 7.32579C8.52255 7.6584 8.13276 7.90076 7.7 8.0262Z" fill="#6B7280"/>
        </svg>
      ),
      onClick: handleHelp,
      description: 'Get help and support information'
    }
  ];

  // Render control options and fallback modals
  return (
    <>
      {showInDropdown ? (
        // Dropdown items
        controlOptions.map((option) => (
          <button
            key={option.id}
            onClick={option.onClick}
            disabled={!isConnected && option.id === 'restart'}
            className={`flex items-center gap-2 w-full text-left p-2 hover:bg-gray-100 rounded-md text-[14px] transition-colors ${
              !isConnected && option.id === 'restart' ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            title={option.description}
          >
            {option.icon}
            <span>{option.label}</span>
          </button>
        ))
      ) : (
        // Grid layout for bottom sheet
        <div className="grid grid-cols-2 gap-4">
          {controlOptions.map((option) => (
            <button
              key={option.id}
              onClick={option.onClick}
              disabled={!isConnected && option.id === 'restart'}
              className={`flex flex-col items-center gap-2 p-4 rounded-lg border transition-colors ${
                !isConnected && option.id === 'restart'
                  ? 'border-gray-200 bg-gray-50 opacity-50 cursor-not-allowed'
                  : 'border-gray-200 hover:bg-gray-50 hover:border-blue-300'
              }`}
            >
              <div className={`${!isConnected && option.id === 'restart' ? 'text-gray-400' : 'text-gray-600'}`}>
                {option.icon}
              </div>
              <span className={`text-sm font-medium ${!isConnected && option.id === 'restart' ? 'text-gray-400' : 'text-gray-900'}`}>
                {option.label}
              </span>
              <span className="text-xs text-gray-500 text-center">
                {option.description}
              </span>
            </button>
          ))}
        </div>
      )}

      {/* Fallback modals - only render when onOpenModal is not provided */}
      {!onOpenModal && activeModal === 'settings' && createPortal(
        <LarkXRSettingsModal
          larksr={larksr}
          isConnected={isConnected}
          onClose={closeModal}
        />,
        document.body
      )}
      {!onOpenModal && activeModal === 'help' && createPortal(
        <LarkXRHelpModal onClose={closeModal} />,
        document.body
      )}
      {!onOpenModal && activeModal === 'network' && createPortal(
        <LarkXRNetworkModal
          larksr={larksr}
          isConnected={isConnected}
          onClose={closeModal}
        />,
        document.body
      )}
    </>
  );
};

export default LarkXRControlOptions;
