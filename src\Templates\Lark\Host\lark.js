import React from "react"
import Fire from "../../../config/Firebase.jsx";
import * as ExceptionActions from "../../../Actions/Exception"
import { connect } from "react-redux"
import * as Sessions from '../../../Actions/Sessions';
import axios from "axios";

class Lark extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            hasShownEndingToast: false,
            isAssigningInstance: false,
            iframeLoaded: false
        };
        this.MapPixelInstance = this.MapPixelInstance.bind(this);
        this.handleIframeMessage = this.handleIframeMessage.bind(this);
    }

    // Handle messages from the iframe
    handleIframeMessage(event) {
        console.log("Host received message from iframe:", event.data);

        // Check if the message is the success message from the iframe
        if (event.data && event.data.type === 'SUCCESS') {
            console.log("Host received SUCCESS message from iframe");
            this.setState({ iframeLoaded: true });

            // Update Firestore to indicate host iframe is loaded
            if (this.props.configDetails && this.props.configDetails.authCode) {
                Fire.firestore().collection("sessions").doc(this.props.roomId).collection("config").doc("data").update({
                    hostIframeLoaded: true
                }).then(() => {
                    console.log("Updated Firestore with hostIframeLoaded: true");
                }).catch(error => {
                    console.error("Failed to update hostIframeLoaded status:", error);
                });
            }
        }
    }

    async MapPixelInstance() {
        if (this.state.isAssigningInstance) {
            console.log("Instance assignment already in progress. Skipping.");
            return;
        }

        if (this.props.configDetails && this.props.configDetails.pixel_streaming_link) {
            console.log("Instance already assigned. Skipping AssignInstance call.");
            return;
        }

        if (this.props.ProjectDetails.projectSettings &&
            this.props.ProjectDetails.projectSettings.pixelstreaming &&
            this.props.ProjectDetails.projectSettings.pixelstreaming.application_id) {

            this.setState({ isAssigningInstance: true, iframeLoaded: false });
            const authcode = await axios.get('https://ps.propvr.io:8181/util/getAuthCode');
            console.log(authcode.data.message)
            return Fire.firestore().collection("sessions").doc(this.props.roomId).collection("config").doc("data").update({
                pixel_streaming_link: `https://larkxr-274706608007.us-central1.run.app/webclient/?appliId=${this.props.ProjectDetails.projectSettings.pixelstreaming.application_id}&codeRate=10000&frameRate=60&language=en-US`,
                authCode: authcode.data.message,
                hostIframeLoaded: false // Set to false when getting a new auth code
            }).then(() => {
                this.setState({ error: null, isAssigningInstance: false });
                // this.props.CreateToast({
                //     // message: "Pixel Streaming Instance Assigned",
                //     // postmessage: "You can now start streaming."
                // });
            }).catch(error => {
                console.error("Firestore update error:", error);
                this.setState({ error: "Failed to update session data", isAssigningInstance: false });
            });
        }
    }

    componentDidUpdate(prevProps) {
        if (this.props.isNearingEnd && !this.state.hasShownEndingToast) {
            this.props.CreateToast({
                message: "Session Ending Soon",
                postmessage: "The session will end in less than 5 minutes."
            });
            this.setState({ hasShownEndingToast: true });
        }
    }

    async componentDidMount() {
        console.log(this.props.ProjectDetails)

        window.addEventListener('message', this.handleIframeMessage);

        Fire.firestore().collection('sessions').doc(this.props.roomId).collection("config").doc("data").onSnapshot({ includeMetadataChanges: true }, (doc) => {
            const config_data = doc.data();
            console.log("config", config_data)
            if (!doc.exists) {
                Fire.firestore().collection('sessions').doc(this.props.roomId).collection("config").doc("data").set({
                    roomId: this.props.roomId,
                    hostIframeLoaded: false
                })
            }
            this.MapPixelInstance();

            this.props.SetConfigData(config_data)

            console.log("host qwertytrew",`https://larkxr-274706608007.us-central1.run.app/webclient/?appliId=${this.props.ProjectDetails.projectSettings.pixelstreaming.application_id}&codeRate=10000&frameRate=60&language=en-US`)
        })
    }

    componentWillUnmount() {
        window.removeEventListener('message', this.handleIframeMessage);
    }

    focusIframe() {
        var iframe = document.getElementById('showcase-iframe');
        iframe.contentWindow.focus();
    }

    render() {
        if (this.props.configDetails && this.props.configDetails.pixel_streaming_link) {
            return (
                <iframe
                    onMouseEnter={() => { this.focusIframe() }}
                    onClick={() => { this.focusIframe() }}
                    style={{ width: "100%", height: '100%', position: "absolute", cursor: "pointer", border: "none" }}
                    src={this.props.configDetails.pixel_streaming_link + `&userType=1&nickname=host&authCode=${this.props.configDetails.authCode}`}
                    allow="fullscreen; vr"
                    id="showcase-iframe">
                </iframe>
            )
        }
        return (
          <p>Loading...</p>
        )
    }
}

const mapStateToProps = state => {
    return {
        configDetails: state.Sessions.configData,
        ProjectDetails: state.Sessions.projectDetails,
    }
}

const mapDispatchToProps = {
    ...ExceptionActions,
    ...Sessions
}

export default connect(mapStateToProps, mapDispatchToProps)(Lark);