import React from "react";
import "./info.css"
import InventoryInfo from "./InventoryInfo";

export default class Info extends React.Component{
    
    constructor(props){
        super(props);
        this.state={
           
            isMobile:window.screen.width<768
        }
    }

    render(){
      if(this.props.info.InfoType=="Info"){

        return(
            
          <><div id="infocards-modal" className="center-info" style={{position: 'absolute',  width: '100%', height: '100%'}}>
          <div id="cards-info" style={{display: 'block', position: 'relative',margin:"auto", top: "0px", left:"0px", height: 'fit-content', transform: 'scale(0.8)', zIndex: 999,maxWidth:"450px"}} className={this.props.info.isMobile?"info-cards testinfo center-div-info":"info-cards testinfo"}>
          
          <div class="info-card-item">
          
                 {this.props.info.infoimgurl!=undefined && this.props.info.infoimgurl.length?
          <div style={{backgroundImage:"url('"+this.props.info.infoimgurl+"')"}} className="info-card-image" >
              
              
              
              </div>
         :
        this.props.info.vidurl!=undefined && this.props.info.vidurl.length ?
        <div className="info-card-image">
          <iframe className="ytembed" style={{width: '100%', height: '100%'}} src={YoutubeURL(this.props.info.vidurl)} frameBorder={0} allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowFullScreen />
        </div>
:this.props.info.info3durl!=undefined && this.props.info.info3durl.length?
<div className="info-card-image">
  <model-viewer style={{width: 'auto!important', height: 'inherit !important'}} className="arcard" src={this.props.info.info3durl} ios-src={this.props.info.info3diosurl}   shadow-intensity={1} camera-controls interaction-prompt="auto" auto-rotate ar>
    <button className="fullscreenbut" onclick="this.parentElement.requestFullscreen();" style={{backgroundColor: 'white', backgroundImage: 'url(https://logomakr.com/media/clipart/tnp_landan/1408665.svg)', borderRadius: '4px', border: 'none', position: 'absolute', top: '16px', right: '16px', width: '35px', height: '35px'}}>
    </button>
  </model-viewer>

  
</div>


:""
        
        }
        
    
    { this.props.info.Description!=undefined && this.props.info.Description.length ?
                  <div className="card-info"> <h2 className="card-title">{this.props.info.key}</h2><p className="card-intro">{this.props.info.Description}</p>
<p style={{margin: '0px', paddingTop: '10px'}}>
    { this.props.info.buyurl!=undefined && this.props.info.buyurl.length ?
    
        <a style={{float: 'right', backgroundColor: 'crimson'}} className="info-url" target="_blank" href={this.props.info.buyurl}>
            Buy</a>

    :""}{this.props.info.knowurl!=undefined && this.props.info.knowurl.length ? 
      <a style={{float: 'right'}} className="info-url" target="_blank" href={this.props.info.knowurl}>Know More</a>

      :""
    }</p>
    </div>
    



    :""}
    </div>
 
    
    </div>


          </div>
        </>
       )
    }
    else if(this.props.info.InfoType=="InventoryInfo") {
      return (
        <InventoryInfo info={this.props.info}/>
        
      )
    }
    else{
      return ""
    }
  }
}


 function YoutubeURL(url){

  var vidurl=url;
    if(vidurl.search("watch")>0){
      vidurl=vidurl.replace('watch?v=','embed/');
    }
    else if(vidurl.search("youtu.be")>0){
       vidurl=vidurl.replace('youtu.be','youtube.com/embed/');
    }
    else{
      vidurl=vidurl+"?enablejsapi=1&version=3&playerapiid=ytplayer";
    }
    return vidurl;
}