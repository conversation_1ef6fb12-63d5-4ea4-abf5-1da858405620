// Screen size utility functions

// Breakpoints matching Tailwind CSS defaults
export const SCREEN_SM = 640;  // sm
export const SCREEN_MD = 768;  // md
export const SCREEN_LG = 1024; // lg
export const SCREEN_XL = 1280; // xl
export const SCREEN_2XL = 1536; // 2xl

/**
 * Check if the current screen is mobile size (< sm)
 * @returns {boolean}
 */
export const isMobileScreen = () => {
  return window.innerWidth < SCREEN_SM;
};

/**
 * Check if the current screen is tablet size (>= sm and < md)
 * @returns {boolean}
 */
export const isTabletScreen = () => {
  return window.innerWidth >= SCREEN_SM && window.innerWidth < SCREEN_MD;
};

/**
 * Check if the current screen is desktop size (>= md)
 * @returns {boolean}
 */
export const isDesktopScreen = () => {
  return window.innerWidth >= SCREEN_MD;
};

/**
 * Get the current screen size category
 * @returns {'mobile'|'tablet'|'desktop'}
 */
export const getScreenSize = () => {
  if (isMobileScreen()) return 'mobile';
  if (isTabletScreen()) return 'tablet';
  return 'desktop';
};

/**
 * Add a resize event listener that calls the callback with the current screen size
 * @param {Function} callback - Function to call when screen size changes
 * @returns {Function} - Function to remove the event listener
 */
export const addScreenResizeListener = (callback) => {
  const handleResize = () => {
    callback(getScreenSize());
  };
  
  window.addEventListener('resize', handleResize);
  
  // Return a function to remove the event listener
  return () => {
    window.removeEventListener('resize', handleResize);
  };
};