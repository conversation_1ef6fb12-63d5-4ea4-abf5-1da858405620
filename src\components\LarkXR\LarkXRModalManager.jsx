// src/components/LarkXR/LarkXRModalManager.jsx
import React, { createContext, useContext, useState } from 'react';
import LarkXRSettingsModal from './LarkXRSettingsModal';
import LarkXRHelpModal from './LarkXRHelpModal';
import LarkXRNetworkModal from './LarkXRNetworkModal';

const LarkXRModalContext = createContext();

export const useLarkXRModal = () => {
  const context = useContext(LarkXRModalContext);
  if (!context) {
    throw new Error('useLarkXRModal must be used within a LarkXRModalProvider');
  }
  return context;
};

export const LarkXRModalProvider = ({ children }) => {
  const [activeModal, setActiveModal] = useState(null);
  const [modalProps, setModalProps] = useState({});

  const openModal = (modalType, props = {}) => {
    console.log('Opening modal:', modalType, props);
    setActiveModal(modalType);
    setModalProps(props);
  };

  const closeModal = () => {
    console.log('Closing modal');
    setActiveModal(null);
    setModalProps({});
  };

  const value = {
    activeModal,
    openModal,
    closeModal
  };

  return (
    <LarkXRModalContext.Provider value={value}>
      {children}
      
      {/* Render modals */}
      {activeModal === 'settings' && (
        <LarkXRSettingsModal
          larksr={modalProps.larksr}
          isConnected={modalProps.isConnected}
          onClose={closeModal}
        />
      )}
      
      {activeModal === 'help' && (
        <LarkXRHelpModal onClose={closeModal} />
      )}
      
      {activeModal === 'network' && (
        <LarkXRNetworkModal
          larksr={modalProps.larksr}
          isConnected={modalProps.isConnected}
          onClose={closeModal}
        />
      )}
    </LarkXRModalContext.Provider>
  );
};

export default LarkXRModalProvider;
