import React from "react"
import Fire from "../../../config/Firebase.jsx";
import * as ExceptionActions from "../../../Actions/Exception"
import { connect } from "react-redux"
import * as Sessions from '../../../Actions/Sessions';
import { PostRequestWithHeaders } from "../../../Tools/helpers/api.js";
import LoaderOverlay from "../../../components/LoaderOverlay.js";

class MP extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            hasShownEndingToast: false,
            isAssigningInstance: false
        };
        this.MapPixelInstance = this.MapPixelInstance.bind(this);
    }

    MapPixelInstance() {
        if (this.state.isAssigningInstance) {
            console.log("Instance assignment already in progress. Skipping.");
            return;
        }

        if (this.props.configDetails && this.props.configDetails.pixel_streaming_link) {
            console.log("Instance already assigned. Skipping AssignInstance call.");
            return;
        }

        if (this.props.ProjectDetails.projectSettings && 
            this.props.ProjectDetails.projectSettings.pixelstreaming && 
            this.props.ProjectDetails.projectSettings.pixelstreaming.pixel_streaming_endpoint) {
            
            this.setState({ isAssigningInstance: true });

            PostRequestWithHeaders({
                url: `${process.env.REACT_APP_API_BACKEND_API_URL}session/AssignInstance`,
                body: {
                    project_id: this.props.ProjectDetails._id,
                    session_id: this.props.roomId
                }
            }).then((response) => {
                if (response.status === 200 && response.data && response.data.pixel_streaming_link) {
                    return Fire.firestore().collection("sessions").doc(this.props.roomId).collection("config").doc("data").update({
                        pixel_streaming_link: response.data.pixel_streaming_link,
                    }).then(() => {
                        this.props.SetConfigData(response.data);
                        this.setState({ error: null, isAssigningInstance: false });
                        this.props.CreateToast({
                            message: "Pixel Streaming Instance Assigned",
                            // postmessage: "You can now start streaming."
                        });
                    }).catch(error => {
                        console.error("Firestore update error:", error);
                        this.setState({ error: "Failed to update session data", isAssigningInstance: false });
                    });
                } else if (response.status === 202) {
                    this.props.CreateToast({
                        message: "Pixel Streaming Instance In Queue",
                        postmessage: "Please wait while we assign an instance to you."
                    });
                    this.setState({ isAssigningInstance: false });
                    return setTimeout(() => {
                        return this.MapPixelInstance();
                    }, 30000);
                } else if (response.status === 404) {
                    this.setState({ error: response.error, isAssigningInstance: false });
                } else {
                    console.error("Unexpected response:", response);
                    this.setState({ error: "Unexpected response from server", isAssigningInstance: false });
                }
            }).catch(error => {
                console.error("AssignInstance error:", error);
                this.setState({ error: "Failed to assign pixel streaming instance", isAssigningInstance: false });
            });
        }
    }

    componentDidUpdate(prevProps) {
        if (this.props.isNearingEnd && !this.state.hasShownEndingToast) {
          this.props.CreateToast({
            message: "Session Ending Soon",
            postmessage: "The session will end in less than 5 minutes."
          });
          this.setState({ hasShownEndingToast: true });
        }
    }

    async componentDidMount() {
        console.log(this.props.ProjectDetails)
        Fire.firestore().collection('sessions').doc(this.props.roomId).collection("config").doc("data").onSnapshot({includeMetadataChanges: true},(doc)=>{
            const config_data = doc.data();
            console.log("config",config_data)
            if(!doc.exists){
                Fire.firestore().collection('sessions').doc(this.props.roomId).collection("config").doc("data").set({roomId:this.props.roomId})
                this.MapPixelInstance()
                return
            }
            if(!config_data.pixel_streaming_link){
               this.MapPixelInstance()
               return
            }

            this.props.SetConfigData(config_data)
        })
    }

    focusIframe() {
        var iframe = document.getElementById('showcase-iframe');
        iframe.contentWindow.focus();
    }

    render() {
        if(this.props.configDetails && this.props.configDetails.pixel_streaming_link){
            return (
                <iframe
                    onMouseEnter={() => { this.focusIframe() }}
                    onClick={() => { this.focusIframe() }}
                    style={{ width: "100%", height: '100%', position: "absolute", cursor: "pointer" }}
                    src={this.props.configDetails.pixel_streaming_link}
                    frameBorder="0"
                    allow="fullscreen; vr"
                    id="showcase-iframe">
                </iframe>
            )
        }
        return (
            <LoaderOverlay
                title="Pixel Streaming Instance In Queue"
                message="Please wait while instance is getting assigned..."
            />
        )
    }
}

const mapStateToProps = state => {
    return {
      configDetails: state.Sessions.configData,
      ProjectDetails: state.Sessions.projectDetails,
    }
}

const mapDispatchToProps = {
    ...ExceptionActions,
    ...Sessions
}

export default connect(mapStateToProps, mapDispatchToProps)(MP);