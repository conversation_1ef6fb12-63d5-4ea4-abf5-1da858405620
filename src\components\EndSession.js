import React, { useEffect, useState } from "react";
import { getCookie } from "../Tools/helpers/domhelper";
import { PostRequestWithHeaders } from "../Tools/helpers/api";
import Fire from "../config/Firebase";

const EndSession = () => {
  const [orgThumbnail, setOrgThumbnail] = useState("");
  const [imageLoaded, setImageLoaded] = useState(false);

  useEffect(() => {
    window.scrollTo(0, 0);
  
    const unsubscribe = Fire.auth().onAuthStateChanged(async (user) => {
      if (user) {
        const uid = user.uid;
        const accessToken = getCookie("accessToken");
  
        console.log("User ID and Access Token:", uid, accessToken);
  
        if (uid && accessToken) {
          try {
            const response = await PostRequestWithHeaders({
              url: process.env.REACT_APP_API_BACKEND_API_URL + 'user/GetUserDetails',
              body: { uid }
            });
  
            const orgWithThumbnail = response.organization_id?.find(org => org.thumbnail);
            setOrgThumbnail(orgWithThumbnail?.thumbnail || "");
          } catch (error) {
            console.error("Error fetching user details:", error);
          }
        }
      }
    });
  
    return () => unsubscribe(); // Cleanup on unmount
  }, []);
  
  return (
    <div className="flex flex-col justify-center items-center h-screen bg-white relative space-y-2">
      {/* Logo at the top - only show if image is loaded */}
      {orgThumbnail && (
        <div className="absolute top-4 left-4 md:top-8 md:left-8">
          <img
            alt="Logo"
            className={`h-8 md:h-10 w-auto transition-opacity duration-300 ${imageLoaded ? 'opacity-100' : 'opacity-0'}`}
            src={orgThumbnail}
            onLoad={() => setImageLoaded(true)}
            onError={() => setImageLoaded(false)}
          />
        </div>
      )}

      <div className="w-full max-w-lg text-center px-4 -mt-10">
        {/* SVG Placeholder */}
        <div className="mb-2">
          <div className="w-96 h-64 mx-auto">
            <svg className="w-full h-full mx-auto" viewBox="0 0 224 101" fill="none" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMidYMid meet">
              <g clip-path="url(#clip0_2968_3232)">
                <path d="M202.339 40.6768H195.582V64.0663H202.339V40.6768Z" fill="#F2F2F2" />
                <path d="M206.801 59.6668L190.366 40.1157L157.378 40.4173L137.367 59.9085L137.772 60.1684H137.627V100.71H206.756V60.1684L206.801 59.6668Z" fill="#F2F2F2" />
                <path d="M190.384 40.1567L169.854 64.2269V100.71H206.757V59.648L190.384 40.1567Z" fill="#E6E6E6" />
                <path d="M192.203 80.5347H184.666V87.1962H192.203V80.5347Z" fill="#3F3D56" />
                <path d="M192.203 69.0039H184.666V75.5598H192.203V69.0039Z" fill="#3F3D56" />
                <path d="M192.203 80.5347H184.666V87.1962H192.203V80.5347Z" fill="white" />
                <path d="M192.203 69.0039H184.666V75.5598H192.203V69.0039Z" fill="white" />
                <path d="M158.709 78.7671H151.173V85.4286H158.709V78.7671Z" fill="white" />
                <path d="M158.709 67.2363H151.173V73.7922H158.709V67.2363Z" fill="white" />
                <path d="M149.017 40.6768H142.26V64.0663H149.017V40.6768Z" fill="#F2F2F2" />
                <path d="M153.479 59.6668L137.045 40.1157L104.057 40.4173L84.0459 59.9085L84.4503 60.1684H84.3058V100.71H153.435V60.1684L153.479 59.6668Z" fill="#F2F2F2" />
                <path d="M137.062 40.1567L116.531 64.2269V100.71H153.435V59.648L137.062 40.1567Z" fill="#E6E6E6" />
                <path d="M138.881 80.5347H131.345V87.1962H138.881V80.5347Z" fill="#3F3D56" />
                <path d="M138.881 69.0039H131.345V75.5598H138.881V69.0039Z" fill="#3F3D56" />
                <path d="M138.881 80.5347H131.345V87.1962H138.881V80.5347Z" fill="white" />
                <path d="M138.881 69.0039H131.345V75.5598H138.881V69.0039Z" fill="white" />
                <path d="M105.387 78.7671H97.8506V85.4286H105.387V78.7671Z" fill="white" />
                <path d="M105.387 67.2363H97.8506V73.7922H105.387V67.2363Z" fill="white" />
                <path d="M94.4186 20.4912H85.3896V51.7453H94.4186V20.4912Z" fill="#F2F2F2" />
                <path d="M100.381 45.8659L78.4213 19.7412L34.3412 20.144L7.60156 46.1892L8.14173 46.5364H7.94883V100.71H100.322V46.5364L100.381 45.8659Z" fill="#F2F2F2" />
                <path d="M78.444 19.7964L51.0098 51.9601V100.71H100.322V45.8415L78.444 19.7964Z" fill="#E6E6E6" />
                <path d="M80.8745 73.751H70.8037V82.6524H80.8745V73.751Z" fill="#3F3D56" />
                <path d="M80.8745 58.3433H70.8037V67.1035H80.8745V58.3433Z" fill="#3F3D56" />
                <path d="M80.8745 73.751H70.8037V82.6524H80.8745V73.751Z" fill="white" />
                <path d="M80.8745 58.3433H70.8037V67.1035H80.8745V58.3433Z" fill="white" />
                <path d="M36.1196 71.3887H26.0488V80.2901H36.1196V71.3887Z" fill="white" />
                <path d="M36.1196 55.9814H26.0488V64.7417H36.1196V55.9814Z" fill="white" />
                <path d="M216.184 100.51H0.21875V100.958H216.184V100.51Z" fill="#3F3D56" />
                <path d="M34.2441 92.2712C37.8447 92.2712 40.7636 86.3156 40.7636 78.9689C40.7636 71.6222 37.8447 65.6665 34.2441 65.6665C30.6435 65.6665 27.7246 71.6222 27.7246 78.9689C27.7246 86.3156 30.6435 92.2712 34.2441 92.2712Z" fill="#3F3D56" />
                <path d="M34.847 100.743C32.2359 85.0769 34.8207 69.4672 34.8471 69.3115L35.3551 69.3976C35.3288 69.5525 32.7588 85.0801 35.3552 100.659L34.847 100.743Z" fill="#E1EFFE" />
                <path d="M39.9842 76.1089L34.0889 79.2622L34.3319 79.7165L40.2272 76.5632L39.9842 76.1089Z" fill="#E1EFFE" />
                <path d="M28.1103 77.6878L27.8672 78.1421L33.7618 81.2972L34.005 80.843L28.1103 77.6878Z" fill="#E1EFFE" />
                <path d="M18.5804 84.4717C25.6228 84.4717 31.3317 72.8231 31.3317 58.4539C31.3317 44.0846 25.6228 32.436 18.5804 32.436C11.5381 32.436 5.8291 44.0846 5.8291 58.4539C5.8291 72.8231 11.5381 84.4717 18.5804 84.4717Z" fill="#E1EFFE" />
                <path d="M20.0009 101.001C14.9007 70.3998 19.9494 39.9104 20.001 39.606L20.5089 39.692C20.4575 39.9956 15.4235 70.403 20.5091 100.916L20.0009 101.001Z" fill="#3F3D56" />
                <path d="M29.9225 53.0759L18.3916 59.2437L18.6346 59.698L30.1655 53.5303L29.9225 53.0759Z" fill="#3F3D56" />
                <path d="M6.46579 56.1663L6.22266 56.6206L17.7519 62.7915L17.995 62.3372L6.46579 56.1663Z" fill="#3F3D56" />
                <path d="M48.7802 78.3977C58.4171 78.3977 66.2294 62.4575 66.2294 42.7943C66.2294 23.1311 58.4171 7.19092 48.7802 7.19092C39.1433 7.19092 31.3311 23.1311 31.3311 42.7943C31.3311 62.4575 39.1433 78.3977 48.7802 78.3977Z" fill="#E1EFFE" />
                <path d="M50.8176 101C43.841 59.141 50.7472 17.4344 50.8177 17.0181L51.3256 17.1041C51.2552 17.5197 44.3638 59.1442 51.3258 100.916L50.8176 101Z" fill="#3F3D56" />
                <path d="M64.3465 35.519L48.5674 43.959L48.8104 44.4133L64.5895 35.9733L64.3465 35.519Z" fill="#3F3D56" />
                <path d="M32.1582 39.7474L31.915 40.2017L47.6919 48.6458L47.9351 48.1916L32.1582 39.7474Z" fill="#3F3D56" />
                <path d="M195.366 92.2712C198.967 92.2712 201.886 86.3156 201.886 78.9689C201.886 71.6222 198.967 65.6665 195.366 65.6665C191.766 65.6665 188.847 71.6222 188.847 78.9689C188.847 86.3156 191.766 92.2712 195.366 92.2712Z" fill="#3F3D56" />
                <path d="M194.764 100.743C197.375 85.0769 194.79 69.4672 194.764 69.3115L194.256 69.3976C194.282 69.5525 196.852 85.0801 194.256 100.659L194.764 100.743Z" fill="#E1EFFE" />
                <path d="M189.626 76.1082L189.383 76.5625L195.278 79.7158L195.521 79.2615L189.626 76.1082Z" fill="#E1EFFE" />
                <path d="M201.5 77.6881L195.605 80.8433L195.849 81.2975L201.743 78.1424L201.5 77.6881Z" fill="#E1EFFE" />
                <path d="M211.031 84.4717C218.073 84.4717 223.782 72.8231 223.782 58.4539C223.782 44.0846 218.073 32.436 211.031 32.436C203.988 32.436 198.279 44.0846 198.279 58.4539C198.279 72.8231 203.988 84.4717 211.031 84.4717Z" fill="#E1EFFE" />
                <path d="M209.61 101.001C214.71 70.3998 209.661 39.9104 209.61 39.606L209.102 39.692C209.153 39.9956 214.187 70.403 209.102 100.916L209.61 101.001Z" fill="#3F3D56" />
                <path d="M199.687 53.076L199.444 53.5303L210.975 59.698L211.218 59.2437L199.687 53.076Z" fill="#3F3D56" />
                <path d="M223.144 56.1655L211.615 62.3364L211.858 62.7907L223.388 56.6198L223.144 56.1655Z" fill="#3F3D56" />
                <path d="M180.83 78.3977C190.467 78.3977 198.279 62.4575 198.279 42.7943C198.279 23.1311 190.467 7.19092 180.83 7.19092C171.193 7.19092 163.381 23.1311 163.381 42.7943C163.381 62.4575 171.193 78.3977 180.83 78.3977Z" fill="#E1EFFE" />
                <path d="M178.792 101C185.769 59.141 178.863 17.4344 178.792 17.0181L178.284 17.1041C178.355 17.5197 185.246 59.1442 178.284 100.916L178.792 101Z" fill="#3F3D56" />
                <path d="M165.264 35.5188L165.021 35.9731L180.8 44.4131L181.043 43.9588L165.264 35.5188Z" fill="#3F3D56" />
                <path d="M197.453 39.7468L181.676 48.1909L181.919 48.6452L197.696 40.201L197.453 39.7468Z" fill="#3F3D56" />
                <path d="M132.315 22.7327L134.378 21.0832C132.776 20.9064 132.117 21.7802 131.848 22.4719C130.596 21.9521 129.233 22.6333 129.233 22.6333L133.36 24.1314C133.152 23.5753 132.789 23.0901 132.315 22.7327Z" fill="#3F3D56" />
                <path d="M81.9052 8.17024L83.9676 6.52066C82.3654 6.34389 81.7071 7.21771 81.4376 7.90935C80.1859 7.38958 78.8232 8.07077 78.8232 8.07077L82.9498 9.56887C82.7417 9.01282 82.3793 8.52765 81.9052 8.17024Z" fill="#3F3D56" />
                <path d="M164.129 1.67268L166.191 0.0231014C164.589 -0.153668 163.931 0.720156 163.661 1.4118C162.41 0.892018 161.047 1.57321 161.047 1.57321L165.173 3.07131C164.965 2.51526 164.603 2.03009 164.129 1.67268Z" fill="#3F3D56" />
                <path d="M168.293 97.9183C167.745 97.9182 167.207 98.0627 166.732 98.3372C166.25 97.9013 165.652 97.6147 165.01 97.5122C164.368 97.4097 163.711 97.4957 163.117 97.7598C162.523 98.0238 162.019 98.4546 161.665 98.9997C161.311 99.5449 161.123 100.181 161.124 100.831H171.419C171.363 100.04 171.009 99.3001 170.429 98.7597C169.849 98.2193 169.086 97.9186 168.293 97.9183Z" fill="#3F3D56" />
                <path d="M63.6654 97.9183C63.1172 97.9182 62.5786 98.0627 62.104 98.3372C61.622 97.9013 61.0238 97.6147 60.3821 97.5122C59.7403 97.4097 59.0826 97.4957 58.4888 97.7598C57.895 98.0238 57.3907 98.4546 57.0369 98.9997C56.6832 99.5449 56.4953 100.181 56.4961 100.831H66.7907C66.7346 100.04 66.381 99.3001 65.8011 98.7597C65.2212 98.2193 64.4581 97.9186 63.6654 97.9183Z" fill="#3F3D56" />
                <path d="M117.211 97.9183C116.663 97.9182 116.124 98.0627 115.65 98.3372C115.168 97.9013 114.57 97.6147 113.928 97.5122C113.286 97.4097 112.629 97.4957 112.035 97.7598C111.441 98.0238 110.937 98.4546 110.583 98.9997C110.229 99.5449 110.041 100.181 110.042 100.831H120.337C120.281 100.04 119.927 99.3001 119.347 98.7597C118.767 98.2193 118.004 97.9186 117.211 97.9183Z" fill="#3F3D56" />
                <path d="M105.913 90.5645H76.2119V91.5225H80.844V100.624H81.802V91.5225H99.8434V100.624H100.802V91.5225H105.913V90.5645Z" fill="#3F3D56" />
                <path d="M105.971 87.6973H76.2705V88.6554H105.971V87.6973Z" fill="#3F3D56" />
                <path d="M105.971 85.3022H76.2705V86.2603H105.971V85.3022Z" fill="#3F3D56" />
                <path d="M105.971 82.9072H76.2705V83.8653H105.971V82.9072Z" fill="#3F3D56" />
                <path d="M152.29 90.5645H122.589V91.5225H127.221V100.624H128.179V91.5225H146.22V100.624H147.179V91.5225H152.29V90.5645Z" fill="#3F3D56" />
                <path d="M152.348 87.6973H122.647V88.6554H152.348V87.6973Z" fill="#3F3D56" />
                <path d="M152.348 85.3022H122.647V86.2603H152.348V85.3022Z" fill="#3F3D56" />
                <path d="M152.348 82.9072H122.647V83.8653H152.348V82.9072Z" fill="#3F3D56" />
              </g>
              <defs>
                <clipPath id="clip0_2968_3232">
                  <rect width="223.563" height="101" fill="white" transform="translate(0.21875)" />
                </clipPath>
              </defs>
            </svg>
          </div>
          {/* Meeting ended message */}
          <h2 className="text-2xl font-semibold text-gray-800 mb-1">
            Your meeting has ended.
          </h2>

          {/* Thank you message */}
          <p className="text-lg text-gray-600 mt-1">
            Thanks for participating.
          </p>
        </div>

      </div>
    </div>
  );
};

export default EndSession;
