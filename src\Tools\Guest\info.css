
.info-cards {
	display: flex;
	padding: 1rem;
	margin-bottom: 2rem;
	width: 400px
}

@media (min-width:40rem) {
	.info-cards {
		width: 400px
	}
}

@media (min-width:56rem) {
	.info-cards {
		width: 400px
	}
}

.info-cards .info-card-item {
	display: flex;
	flex-direction: column;
	background-color: #fff;
	width: 100%;
	border-radius: 6px;
	box-shadow: 0 20px 40px -14px rgba(0, 0, 0, .25);
	overflow: hidden;
	transition: transform .5s;
	-webkit-transition: transform .5s;
	z-index: 1
}

.info-cards  {
	cursor: pointer;
	transform: scale(1.1);
	-webkit-transform: scale(1.1)
}

.info-cards .info-card-item:hover .info-card-image {
	opacity: 1
}

.info-cards .card-info {
	display: flex;
	flex: 1 1 auto;
	flex-direction: column;
	padding: 10px;
	background: #fff;
	height: fit-content
}

.info-cards .info-card-title {
	font-size: 25px;
	line-height: 1.1em;
	color: #32325d;
	margin-bottom: .2em
}



.info-cards .info-card-image {
	height: 200px;
	overflow: hidden;
	background-size: contain;
	background-position: center;
	background-repeat: no-repeat;
	border-radius: 6px 6px 0 0;
	opacity: .91
}

@media only screen and (max-width:991px) {
	.card-intro {
		font-size: 13px
	}
	.info-url {
		padding: 15px
	}
	.card-title {
		font-size: 20px
	}
}

.info-url {
	background-color: #2979ff;
	border: solid;
	color: #fff;
	border-radius: 13px;
	padding: 8px 10px;
	text-align: center;
	text-decoration: none;
	display: inline-block;
	font-size: 13px;
	width: 100px
}

@media only screen and (max-width:400px) {
	.grid {
		width: 100px!important;
		height: 121px!important
	}
	.icon1 {
		position: fixed!important;
		top: 10px!important;
		right: 10px!important
	}
	.grid figure figcaption,
	.grid figure figcaption>a {
		bottom: 40px!important
	}
}


@media only screen and (max-width:1000px) {
	#info-banner {
		flex-direction: column!important
	}
	#info-contact {
		padding: 10px
	}
	#info-des {
		padding: 10px
	}
	#info-des1 {
		padding: 10px
	}
}

.menu_item {
	margin: 0 0 8px 8px
}

#info-contact {
	display: flex;
	flex-direction: column;
	width: fit-content;
	position: relative;
	align-items: center
}

#info-des {
	text-align: justify;
	color: #222;
	line-height: 1.6;
	letter-spacing: 1px
}

.card-intro {
    margin: 0;
    padding: 10px 0;
    max-height: 150px;
    overflow: auto;
}

@media only screen and (max-width: 991px){
.card-intro {
    font-size: 13px;
}
}
.center-div-info{position:absolute;margin:auto;top:0;right:0;bottom:0;text-align:center;left:0;width:75%;padding-top:22px;font-size:xx-large;height:fit-content;border-radius:3px}