import React from "react"
class Inventory extends React.Component{
    constructor(props){
        super(props);
    }
    render(){
        if(this.props.Inventory[this.props.CurrentImage]){
            return Object.keys(this.props.Inventory[this.props.CurrentImage]).map(Node=>{
                var Inven=this.props.Inventory[this.props.CurrentImage][Node];
               var splitrot=Inven.position.split(" ");
              var a= parseFloat(splitrot[0]*120);
              var b= parseFloat(splitrot[1]*120);
              var c= parseFloat(splitrot[2]*120);
                if(Inven.status=="1" || Inven.status==1){
                     return(
                    <a-image key={Node}
                    position={a+" "+b+" "+c} 
                    scale={Inven.scale?Inven.scale*120+" "+Inven.scale*120:0.05*120+" "+0.05*120}             
                    look-at='#cam1'
                    class="boxes" 
                    onClick={()=>{this.props.SetInfo("InventoryInfo",
                  Inven,
                  Node,
                  window.screen.width<768,
                  {}
                  )}}
                    src="#inventory_icon" 
                    material="transparent:true;opacity:0.9;" 
                    geometry=""></a-image>
            )
                }
                else if(Inven.status=="-1" || Inven.status==-1){
                    return(
                   <a-image key={Node}
                   position={a+" "+b+" "+c} 
                   scale={Inven.scale?Inven.scale*120+" "+Inven.scale*120:0.05*120+" "+0.05*120}             
                   look-at='#cam1'
                   class="boxes" 
                   onClick={()=>{this.props.SetInfo("InventoryInfo",
                 Inven,
                 Node,
                 window.screen.width<768,
                 {}
                 )}}
                   src="#inventory_hold_icon" 
                   material="transparent:true;opacity:0.9;" 
                   geometry=""></a-image>
           )
               } 
                else{
                    return(
                        <a-image key={Node}
                        position={a+" "+b+" "+c} 
                        scale={Inven.scale?Inven.scale*120+" "+Inven.scale*120:0.05*120+" "+0.05*120}             
                        look-at='#cam1'
                        class="boxes" 
                        onClick={()=>{this.props.SetInfo("InventoryInfo",
                      Inven,
                      Node,
                      window.screen.width<768,
                      {}
                      )}}
                        src="#inventory_sold_icon" 
                        material="transparent:true;opacity:0.9;" 
                        geometry=""></a-image>
                )
                }
                
            })
           
        }
        return (
        <></>
        )
    }
}
export default Inventory;