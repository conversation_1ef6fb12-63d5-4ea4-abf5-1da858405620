import React from "react"
// import SceneControls from "../../../Tools/ToolComponents/SceneControlsGuest";
import { connect } from "react-redux"
import * as HostActions from "../../../Actions/HostAction"
import * as ExceptionActions from "../../../Actions/Exception"
import { socket } from "../../../Actions/HostAction";

class Lark extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            lock: true,
            hasShownEndingToast: false,
            isLoading: true,
            iframeLoaded: false, // Track if iframe has loaded
            hostConnected: false, // Track if host is connected
            needsReload: false, // Track if iframe needs to be reloaded
            waitingForHost: false, // Track if we're waiting for host iframe to load
            iframeKey: Date.now() // Key to force iframe reload
        }

        this.handleIframeMessage = this.handleIframeMessage.bind(this);
        this.reloadIframe = this.reloadIframe.bind(this);
    }

    reloadIframe() {
        console.log("Reloading iframe due to host reconnection");
        this.setState({
            isLoading: true,
            iframeLoaded: false,
            needsReload: false,
            iframeKey: Date.now()
        });

        setTimeout(() => {
            this.props.CreateToast({
                message: "Reconnecting to host",
                postmessage: "Please wait while we reconnect to the host's session."
            });
        }, 500);
    }

    handleIframeMessage(event) {
        console.log("Received message from iframe:", event.data);

        if (event.data && event.data.type === 'SUCCESS') {
            console.log("Received SUCCESS message from iframe");
            this.setState({
                isLoading: false,
                iframeLoaded: true,
                hostConnected: true
            });

        }

        if (event.data && (event.data.type === 'ERROR' || event.data.type === 'DISCONNECTED')) {
            console.log("Received error or disconnection message from iframe");
            this.setState({
                isLoading: true,
                needsReload: true
            });

            this.props.CreateToast({
                message: "Connection issue detected",
                postmessage: "We'll try to reconnect you automatically."
            });
        }
    }

    componentDidMount() {
        const config = this.props.configDetails || this.props.reduxConfigDetails;
        console.log("Guest Lark component mounted with config:", config);
        console.log("Config source:", this.props.configDetails ? "From parent props" : (this.props.reduxConfigDetails ? "From Redux store" : "No config available"));

        console.log("Client data in Lark component:", this.props.clientData);

        window.addEventListener('message', this.handleIframeMessage);

        this.props.SubscribeToCustom((msg) => {
            var data = JSON.parse(msg);
            if (data.targetuser == "All" || data.targetuser == socket.id) {
                if (data.actiontype == "unlock") {
                    this.props.CreateToast({ message: "Now you have control" })
                    this.setState({
                        lock: false
                    })
                }
                if (data.actiontype == "lock") {
                    this.props.CreateToast({ message: "Your control has been revoked by host" })
                    this.setState({
                        lock: true
                    })
                }
            } else {
                this.setState({
                    lock: true
                })
            }
        })
    }
    componentDidUpdate(prevProps, prevState) {
      // Handle session ending notification
      if (this.props.isNearingEnd && !prevProps.isNearingEnd && !this.state.hasShownEndingToast) {
          this.props.CreateToast({
              message: "Session Ending Soon",
              postmessage: "The session will end in less than 5 minutes.",
          });
          this.setState({ hasShownEndingToast: true });
      }

      if (prevProps.configDetails !== this.props.configDetails && this.props.configDetails) {
          console.log("Config details updated:", this.props.configDetails);

          if (prevProps.configDetails &&
              this.props.configDetails &&
              prevProps.configDetails.authCode !== this.props.configDetails.authCode) {

              console.log("Auth code changed, host likely reconnected. Old:",
                  prevProps.configDetails.authCode, "New:", this.props.configDetails.authCode);

              // Check if host iframe is loaded
              const hostIframeLoaded = this.props.configDetails.hostIframeLoaded === true;
              console.log("Host iframe loaded status:", hostIframeLoaded);

              if (hostIframeLoaded) {
                  // If host iframe is loaded, reload immediately
                  console.log("Host iframe is already loaded, reloading guest iframe immediately");
                  if (this.state.iframeLoaded) {
                      this.setState({
                          needsReload: true,
                          hostConnected: false
                      });
                  }
              } else {
                  // If host iframe is not loaded yet, wait for the hostIframeLoaded flag
                  console.log("Host iframe not loaded yet, waiting for host to load before reloading guest iframe");
                  this.setState({
                      isLoading: true,
                      iframeLoaded: false,
                      hostConnected: false,
                      waitingForHost: true // New state to track that we're waiting for host
                  });

                  this.props.CreateToast({
                      message: "Host reconnected",
                      postmessage: "Please wait while the host prepares the session."
                  });
              }
          }

          // Check if pixel_streaming_link has changed
          if (prevProps.configDetails &&
              this.props.configDetails &&
              prevProps.configDetails.pixel_streaming_link !== this.props.configDetails.pixel_streaming_link) {

              console.log("Pixel streaming link changed, host likely reconnected");

              // If we were already loaded, we need to reload the iframe
              if (this.state.iframeLoaded) {
                  this.setState({
                      needsReload: true,
                      hostConnected: false
                  });
              }
          }

          // Check if hostIframeLoaded flag has changed from false to true
          if (prevProps.configDetails &&
              this.props.configDetails &&
              prevProps.configDetails.hostIframeLoaded === false &&
              this.props.configDetails.hostIframeLoaded === true) {

              console.log("Host iframe is now loaded, reloading guest iframe if needed");

              // If we were waiting for the host to load, reload now
              if (this.state.waitingForHost) {
                  this.setState({
                      needsReload: true,
                      waitingForHost: false
                  });
              }
          }
      }

      if (this.state.needsReload && !prevState.needsReload) {
          this.reloadIframe();
      }

      // Log when client data changes
      if (prevProps.clientData !== this.props.clientData && this.props.clientData) {
          console.log("Client data updated:", this.props.clientData);

          if (this.props.clientData.data && this.props.clientData.data.name) {
              console.log("Guest name updated to:", this.props.clientData.data.name);
          }
      }
  }

  componentWillUnmount() {
      // Clean up the event listener when the component unmounts
      window.removeEventListener('message', this.handleIframeMessage);
  }
    focusIframe() {
        var iframe = document.getElementById('showcase-iframe');
        if(iframe)
        iframe.contentWindow.focus();

    }
    render() {
        const config = this.props.configDetails || this.props.reduxConfigDetails;

        if(config && config.pixel_streaming_link){
            let streamingLink = config.pixel_streaming_link;

            if (streamingLink.includes('?')) {
                streamingLink += '&guest=true';
            } else {
                streamingLink += '?guest=true';
            }

            // Get the guest name from clientData
            let guestName = "guest";
            if (this.props.clientData && this.props.clientData.data && this.props.clientData.data.name) {
                guestName = this.props.clientData.data.name;
            }

            // Create the full iframe URL with the guest's name
            const iframeUrl = streamingLink+`&playerMode=1&userType=0&nickname=${encodeURIComponent(guestName)}&authCode=${config.authCode}`;

            // Log the iframe URL for debugging
            console.log("Guest Lark iframe URL:", iframeUrl);
            console.log("Using guest name:", guestName);

            return (
                <>
                    <iframe
                        key={this.state.iframeKey} // Use key to force iframe to reload when needed
                        onMouseEnter={() => { this.focusIframe() }}
                        onClick={() => { this.focusIframe() }}
                        style={{
                            width: "100%",
                            height: '100%',
                            position: "absolute",
                            cursor: "pointer",
                            // zIndex: 1,
                            border: "none" // Use CSS instead of frameBorder
                        }}
                        src={iframeUrl}
                        allow="fullscreen; vr"
                        id="showcase-iframe"></iframe>

                  

                    {/* Loading overlay */}
                    {this.state.isLoading && (
                        <div style={{
                            position: "absolute",
                            width: "100%",
                            height: "100%",
                            top: 0,
                            left: 0,
                            backgroundColor: "rgba(0, 0, 0, 0.7)",
                            display: "flex",
                            flexDirection: "column",
                            justifyContent: "center",
                            alignItems: "center",
                        }}>
                            <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500 mb-4"></div>

                            {/* Show different messages based on connection state */}
                            {this.state.needsReload ? (
                                <>
                                    <p className="text-white text-xl font-semibold">Reconnecting to Host...</p>
                                    <p className="text-gray-300 mt-2">The host has reconnected. Please wait while we sync with their session.</p>
                                </>
                            ) : this.state.waitingForHost ? (
                                <>
                                    <p className="text-white text-xl font-semibold">Waiting for Host to Load...</p>
                                    <p className="text-gray-300 mt-2">The host is preparing the session. Please wait for the host to fully load.</p>
                                </>
                            ) : this.state.hostConnected ? (
                                <>
                                    <p className="text-white text-xl font-semibold">Loading Pixel Streaming...</p>
                                    <p className="text-gray-300 mt-2">Please wait while we prepare your experience</p>
                                </>
                            ) : (
                                <>
                                    <p className="text-white text-xl font-semibold">Waiting for Host...</p>
                                    <p className="text-gray-300 mt-2">Please wait while the host prepares the session</p>
                                </>
                            )}
                        </div>
                    )}
                </>
            )
        }else{
            return (
                <div style={{
                    position: "absolute",
                    width: "100%",
                    height: "100%",
                    top: 0,
                    left: 0,
                    backgroundColor: "rgba(0, 0, 0, 0.7)",
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                }}>
                    <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500 mb-4"></div>
                    <p className="text-white text-xl font-semibold">Waiting for Host...</p>
                    <p className="text-gray-300 mt-2">Please wait while the host prepares the session</p>
                    <p className="text-gray-400 text-sm mt-6">This may take a moment as the host initializes the experience</p>
                </div>
            )
        }
    }
}
const mapStateToProps = state => {
    return {
      reduxConfigDetails: state.Sessions.configData,
      clientData: state.Call.ClientData,
    }
  }
const mapDispatchTothisprops = {
    ...HostActions,
    ...ExceptionActions,
}

export default connect(mapStateToProps, mapDispatchTothisprops)(Lark)