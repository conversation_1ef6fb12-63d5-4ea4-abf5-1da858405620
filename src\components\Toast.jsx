import React from "react"
import {connect} from "react-redux"
import * as ExceptionActions from "../Actions/Exception"

class Toast extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      notification: []
    }

    this.handleCloseToast = this.handleCloseToast.bind(this);
  }

  handleCloseToast(toastKey) {
    // Get the toast element
    const toastElement = document.getElementById(`toast-${toastKey}`);

    // Add the slide-out animation class
    if (toastElement) {
      toastElement.classList.add('animate-slide-out');

      // Wait for the animation to complete before removing the toast
      setTimeout(() => {
        this.props.DeleteToast(toastKey);
      }, 300); // Match this with the animation duration
    } else {
      // If element not found, just delete the toast
      this.props.DeleteToast(toastKey);
    }
  }

  render() {
    return (<>
    {Object.keys(this.props.Toasts).length ?
      <div className="toaster">
        {Object.entries(this.props.Toasts).map(([key, nodes]) => {
          return (
            <div
              id={`toast-${key}`}
              key={key}
              className="h-fit mb-1 rounded-md inset-0 flex flex-col w-full sm:w-64 pointer-events-auto bg-[black]/30 bg-opacity-40 sm:rounded backdrop-blur-xl p-2 relative shadow-lg transition-all duration-500 overflow-hidden animate-slide-in border border-white/10">
              {/* Close button - macOS style */}
              <button
                onClick={() => this.handleCloseToast(key)}
                className="absolute top-2 right-2 w-5 h-5 rounded-full bg-white/10 hover:bg-white/20 flex items-center justify-center transition-colors duration-200"
                title="Close"
              >
                <svg
                  className="w-3 h-3 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>

              <div className="flex items-center justify-start rounded-t-[0.3rem]">
                <svg className="w-4 h-4 fill-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="info"><rect transform="rotate(180 12 12)" opacity="0"/><path d="M12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2zm0 18a8 8 0 1 1 8-8 8 8 0 0 1-8 8z"/><circle cx="12" cy="8" r="1"/><path d="M12 10a1 1 0 0 0-1 1v5a1 1 0 0 0 2 0v-5a1 1 0 0 0-1-1z"/></g></g></svg>
                <p className="ml-2 text-[13px] text-base font-normal not-italic tracking-[normal] text-white text-[#222b45] my-0">
                  {nodes.message}
                </p>
              </div>

              {nodes.postmessage ?
                <div className="relative flex-auto pt-2">
                  <p className="text-[12px] font-[normal] not-italic m-0 tracking-[normal] text-white font-sans border-t border-t-solid border-t-white">
                    {nodes.postmessage}
                  </p>
                </div>
              : <></>}
            </div>
          )
        })}
      </div>
    : <></>}
    </>)
  }
}
const mapStateToProps = state => {
    return {
        Toasts:state.Exception.toasts
    };
  };

  const mapDispatchToProps = {
    DeleteToast: ExceptionActions.DeleteToast
  }

  export default connect(mapStateToProps, mapDispatchToProps)(Toast);

