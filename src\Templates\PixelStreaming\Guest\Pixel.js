import React from "react"
// import SceneControls from "../../../Tools/ToolComponents/SceneControlsGuest";
import { connect } from "react-redux"
import * as HostActions from "../../../Actions/HostAction"
import * as ExceptionActions from "../../../Actions/Exception"
import { socket } from "../../../Actions/HostAction";
import Fire, { Firebase } from "../../../config/Firebase.jsx";
import LoaderOverlay from "../../../components/LoaderOverlay.js";

class MP extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            lock: true,
            real_time_config:false,
            hasShownEndingToast: false
        }

    }

    async componentDidMount() {
        Fire.firestore().collection('sessions').doc(this.props.roomId).collection("config").doc("data").onSnapshot({includeMetadataChanges: true},(doc)=>{
            const config_data = doc.data();
            this.setState({
              real_time_config:config_data
            })
            console.log("real_time_config",config_data)
        })
        this.props.SubscribeToCustom((msg) => {
            var data = JSON.parse(msg);
            if (data.targetuser == "All" || data.targetuser == socket.id) {
                if (data.actiontype == "unlock") {
                    this.props.CreateToast({ message: "Now you have control" })
                    this.setState({
                        lock: false
                    })
                }
                if (data.actiontype == "lock") {
                    this.props.CreateToast({ message: "Your control has been revoked by host" })
                    this.setState({
                        lock: true
                    })
                }
            } else {
                this.setState({
                    lock: true
                })
            }

        })
    }
    componentDidUpdate(prevProps) {
      if (this.props.isNearingEnd && !prevProps.isNearingEnd && !this.state.hasShownEndingToast) {
          this.props.CreateToast({
              message: "Session Ending Soon",
              postmessage: "The session will end in less than 5 minutes.",
          });
          this.setState({ hasShownEndingToast: true });
      }
  }
    focusIframe() {
        var iframe = document.getElementById('showcase-iframe');
        if(iframe)
        iframe.contentWindow.focus();

    }
    render() {
        if(this.state.real_time_config && this.state.real_time_config.pixel_streaming_link){
            let streamingLink = this.state.real_time_config.pixel_streaming_link;

            if (streamingLink.includes('?')) {
                streamingLink += '&guest=true';
            } else {
                streamingLink += '?guest=true';
            }
            return (
                <>
                    <iframe
                        onMouseEnter={() => { this.focusIframe() }}
                        onClick={() => { this.focusIframe() }}
                        style={{ width: "100%", height: '100%', position: "absolute", cursor: "pointer" }}
                        src={streamingLink}
                        frameborder="0"
                        allow="fullscreen; vr"
                        id="showcase-iframe"></iframe>

                    {this.state.lock ? <><div style={{ position: "absolute", width: "100%", height: "100%", bottom: "0" }}></div>
                        <div style={{ position: "absolute", width: "100%", height: "100%", top: "0" }}></div></> : <></>}

                </>
            )
        }else{
            return (
              <LoaderOverlay
                title="Host is Joining"
                message= "Please wait while your host connects to the session..."
            />
            )
        }

    }
}
const mapStateToProps = state => {
    return {
      configDetails: state.Sessions.configData,
    }
  }
const mapDispatchTothisprops = {
    ...HostActions,
    ...ExceptionActions,
}

export default connect(mapStateToProps, mapDispatchTothisprops)(MP)