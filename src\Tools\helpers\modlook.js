export const modLook = (callbacks) => {
  var onclickstatus = false;
  var camerarotation = false;
  var previousrotationx = 0;
var previousrotationy = 0;
var previousrotationz = 0;
    const AFRAME = window.AFRAME;
    const THREE = window.THREE;
             
var isMobile = AFRAME.utils.device.isMobile();
var bind = AFRAME.utils.bind;
const $ = window.jQuery;
var infodis=0;
// To avoid recalculation at every mouse movement tick
var PI_2 = Math.PI / 2;
var radToDeg = THREE.Math.radToDeg;






if(!AFRAME.components["annotate-listen"]){

AFRAME.registerComponent("annotate-listen", {
  init: function() {
    
    this.el.addEventListener("raycaster-intersected", evt => {
      this.raycaster = evt.detail.el;
    });
    this.el.addEventListener("raycaster-intersected-cleared", evt => {
      this.raycaster = null;
   
    });
    this.el.addEventListener("mouseup",(evt)=>{
      // if(onclickstatus){  
        const dis=document.getElementById('MYVIEW').object3D.getWorldPosition().distanceTo(camerarotation);
        const angle=find_angle({...camerarotation},{...document.getElementById('MYVIEW').object3D.getWorldPosition()});
      // console.log(document.getElementById("cam1").object3D.rotation)
        // if(evt.detail.intersection.point.distanceTo(onclickstatus)<(isMobile?20:2)){
        if(dis<0.8)  
        callbacks.Annotate(evt.detail.intersection.point);

        // }
      // } 
    })
    this.el.addEventListener("mousedown",(evt)=>{
    // if(!isMobile && onclickstatus!=`clicked`)
    // {
      camerarotation=document.getElementById('MYVIEW').object3D.getWorldPosition();
      // console.log(document.getElementById('MYVIEW').object3D.getWorldPosition())
      // camerarotation={...document.getElementById('MYVIEW').object3D.getWorldPosition()},
     
      onclickstatus=evt.detail.intersection.point;
    // };
      })




  },

  tick: function() {
    if (!this.raycaster) {
      return;
    } // Not intersecting.

    let intersection = this.raycaster.components.raycaster.getIntersection(
      this.el
    );

    if (!intersection) {
      return;
    }
   
    
   
    
    
    if (intersection.point.x != previousrotationx && !isMobile && onclickstatus!="clicked") {
      
      onclickstatus = false;
      
    }
   

    previousrotationx = intersection.point.x;
    previousrotationy = intersection.point.y;
    previousrotationz = intersection.point.z;


    let up = new THREE.Vector3(0, 0, 1);
    let axis = intersection.point;
    if (
      intersection.face.normal.y > 1 ||
      intersection.face.normal.y < -1
    ) {
      axis = new THREE.Vector3(
        intersection.point.x,
        intersection.point.y,
        intersection.point.z
      );
    } else {
      axis = intersection.point.crossVectors(
        up,
        intersection.face.normal
      );
    }
    let radians = Math.acos(intersection.face.normal.dot(up));

    
  
  }
});
}






if(!AFRAME.components["modlook-controls"]){

AFRAME.registerComponent('modlook-controls', {
  dependencies: ['position', 'rotation'],

  schema: {
    enabled: {default: true},
    hmdEnabled: {default: true},
    reverseMouseDrag: {default: false},
    gyroEnabled: {default: false},
     touchEnabled: {default: true}
   // standing: {default: true}
  },

  init: function () {
    
    var sceneEl = this.el.sceneEl;
    // Aux variables
    this.previousHMDPosition = new THREE.Vector3();
    this.hmdQuaternion = new THREE.Quaternion();
    this.hmdEuler = new THREE.Euler();

    this.setupMouseControls();
    this.setupHMDControls();
    this.bindMethods();

    // Enable grab cursor class on canvas.
    function enableGrabCursor () { sceneEl.canvas.classList.add('a-grab-cursor'); }
    if (!sceneEl.canvas) {
      sceneEl.addEventListener('render-target-loaded', enableGrabCursor);
    } else {
      enableGrabCursor();
    }

    // Reset previous HMD position when we exit VR.
    sceneEl.addEventListener('exit-vr', this.onExitVR);
  },

  update: function (oldData) {
    var data = this.data;
    var hmdEnabled = data.hmdEnabled;
    if (!data.enabled) { return; }
    if (!hmdEnabled && oldData && hmdEnabled !== oldData.hmdEnabled) {
      this.pitchObject.rotation.set(0, 0, 0);
      this.yawObject.rotation.set(0, 0, 0);
    }
   // this.controls.standing = data.standing;
   // this.controls.update();
    this.updateOrientation();
    this.updatePosition();
  },

  play: function () {
    this.addEventListeners();
  },

  pause: function () {
    this.removeEventListeners();
  },

  tick: function (t) {
    this.update();
    
  },

  remove: function () {
    this.pause();
  },

  bindMethods: function () {
    this.onMouseDown = bind(this.onMouseDown, this);
    this.onMouseMove = bind(this.onMouseMove, this);
    this.releaseMouse = bind(this.releaseMouse, this);
    this.onTouchStart = bind(this.onTouchStart, this);
    this.onTouchMove = bind(this.onTouchMove, this);
    this.onTouchEnd = bind(this.onTouchEnd, this);
    this.onExitVR = bind(this.onExitVR, this);
  },

  setupMouseControls: function () {
    // The canvas where the scene is painted
    this.mouseDown = false;
    this.pitchObject = new THREE.Object3D();
    this.yawObject = new THREE.Object3D();
    this.yawObject.position.y = 10;
    this.yawObject.add(this.pitchObject);
  },

  setupHMDControls: function () {
    this.dolly = new THREE.Object3D();
    this.euler = new THREE.Euler();
    //this.controls = new THREE.VRControls(this.dolly);
   // this.controls.userHeight = 0.0;
  },

  addEventListeners: function () {
    var sceneEl = this.el.sceneEl;
    var canvasEl = sceneEl.canvas;

    // listen for canvas to load.
    if (!canvasEl) {
      sceneEl.addEventListener('render-target-loaded', bind(this.addEventListeners, this));
      return;
    }

    // Mouse Events
    canvasEl.addEventListener('mousedown', this.onMouseDown, false);
    window.addEventListener('mousemove', this.onMouseMove, false);
    window.addEventListener('mouseup', this.releaseMouse, false);

    // Touch events
    canvasEl.addEventListener('touchstart', this.onTouchStart);
    window.addEventListener('touchmove', this.onTouchMove);
    window.addEventListener('touchend', this.onTouchEnd);
  },

  removeEventListeners: function () {
    var sceneEl = this.el.sceneEl;
    var canvasEl = sceneEl && sceneEl.canvas;
    if (!canvasEl) { return; }

    // Mouse Events
    canvasEl.removeEventListener('mousedown', this.onMouseDown);
    canvasEl.removeEventListener('mousemove', this.onMouseMove);
    canvasEl.removeEventListener('mouseup', this.releaseMouse);
    canvasEl.removeEventListener('mouseout', this.releaseMouse);

    // Touch events
    canvasEl.removeEventListener('touchstart', this.onTouchStart);
    canvasEl.removeEventListener('touchmove', this.onTouchMove);
    canvasEl.removeEventListener('touchend', this.onTouchEnd);
  },

  updateOrientation: function () {
    
    var currentRotation;
    var deltaRotation;
    var hmdEuler = this.hmdEuler;
    var pitchObject = this.pitchObject;
    var yawObject = this.yawObject;
    var hmdQuaternion = this.calculateHMDQuaternion();
    var sceneEl = this.el.sceneEl;
    var rotation;
    hmdEuler.setFromQuaternion(hmdQuaternion, 'YXZ');

    if (isMobile) {
       currentRotation = this.el.getAttribute('rotation');
      // In mobile we allow camera rotation with touch events and sensors
      if (this.data.gyroEnabled) {
        pitchObject.rotation.x = 0;
        rotation = {
          x: radToDeg(hmdEuler.x) + radToDeg(pitchObject.rotation.x),
          y: radToDeg(hmdEuler.y) + radToDeg(yawObject.rotation.y),
          z: radToDeg(hmdEuler.z)
        };
      } 
      else if(this.data.reverseMouseDrag){
        
        rotation = {
          x: radToDeg(hmdEuler.x) - radToDeg(pitchObject.rotation.x),
          y: radToDeg(hmdEuler.y) - radToDeg(yawObject.rotation.y),
          
        };
      }
      else {
        rotation = {
          x: radToDeg(pitchObject.rotation.x),
          y: radToDeg(yawObject.rotation.y)
        };
      }
    } else if (!sceneEl.is('vr-mode') || isNullVector(hmdEuler) || !this.data.hmdEnabled) {
      currentRotation = this.el.getAttribute('rotation');
      deltaRotation = this.calculateDeltaRotation();
      // Mouse look only if HMD disabled or no info coming from the sensors
      if (this.data.reverseMouseDrag) {
        rotation = {
          x: radToDeg(hmdEuler.x) - radToDeg(pitchObject.rotation.x),
          y: radToDeg(hmdEuler.y) - radToDeg(yawObject.rotation.y),
          z: currentRotation.z
        };
      } else {
        rotation = {
          x: radToDeg(pitchObject.rotation.x),
          y: radToDeg(yawObject.rotation.y),
          z: currentRotation.z
        };
      }
    } else {
      // Mouse rotation ignored with an active headset.
      // The user head rotation takes priority
      rotation = {
        x: radToDeg(hmdEuler.x),
        y: radToDeg(hmdEuler.y),
        z: radToDeg(hmdEuler.z)
      };
    }
    this.el.setAttribute('rotation', rotation);
  },

  calculateDeltaRotation: function () {
    var currentRotationX = radToDeg(this.pitchObject.rotation.x);
    var currentRotationY = radToDeg(this.yawObject.rotation.y);
    var deltaRotation;
    this.previousRotationX = this.previousRotationX || currentRotationX;
    this.previousRotationY = this.previousRotationY || currentRotationY;
    deltaRotation = {
      x: currentRotationX - this.previousRotationX,
      y: currentRotationY - this.previousRotationY
    };
    this.previousRotationX = currentRotationX;
    this.previousRotationY = currentRotationY;
    return deltaRotation;
  },

  calculateHMDQuaternion: function () {
    var hmdQuaternion = this.hmdQuaternion;
    hmdQuaternion.copy(this.dolly.quaternion);
    return hmdQuaternion;
  },

  updatePosition: (function () {
    var deltaHMDPosition = new THREE.Vector3();
    return function () {
      var el = this.el;
      var currentPosition = el.getAttribute('position');
      var currentHMDPosition;
      var previousHMDPosition = this.previousHMDPosition;
      var sceneEl = this.el.sceneEl;
      currentHMDPosition = this.calculateHMDPosition();
      deltaHMDPosition.copy(currentHMDPosition).sub(previousHMDPosition);
      if (!sceneEl.is('vr-mode') || isNullVector(deltaHMDPosition)) { return; }
      previousHMDPosition.copy(currentHMDPosition);
      // Do nothing if we have not moved.
      if (!sceneEl.is('vr-mode')) { return; }
      el.setAttribute('position', {
        x: currentPosition.x + deltaHMDPosition.x,
        y: currentPosition.y + deltaHMDPosition.y,
        z: currentPosition.z + deltaHMDPosition.z
      });
    };
  })(),

  calculateHMDPosition: function () {
    var dolly = this.dolly;
    var position = new THREE.Vector3();
    dolly.updateMatrix();
    position.setFromMatrixPosition(dolly.matrix);
    return position;
  },

  onMouseMove: function (event) {
    var pitchObject = this.pitchObject;
    var yawObject = this.yawObject;
    var previousMouseEvent = this.previousMouseEvent;

    if (!this.mouseDown || !this.data.enabled) { return; }

    var movementX = event.movementX || event.mozMovementX;
    var movementY = event.movementY || event.mozMovementY;

    if (movementX === undefined || movementY === undefined) {
      movementX = event.screenX - previousMouseEvent.screenX;
      movementY = event.screenY - previousMouseEvent.screenY;
    }
    this.previousMouseEvent = event;

    yawObject.rotation.y -= movementX * 0.002;
    pitchObject.rotation.x -= movementY * 0.002;
    pitchObject.rotation.x = Math.max(-PI_2, Math.min(PI_2, pitchObject.rotation.x));
  },

  onMouseDown: function (event) {
    this.mouseDown = true;
    this.previousMouseEvent = event;
    document.body.classList.add('a-grabbing');
  },

  releaseMouse: function () {
    this.mouseDown = false;
    document.body.classList.remove('a-grabbing');
  },

  onTouchStart: function (e) {
    if (e.touches.length !== 1) { return; }
    this.touchStart = {
      x: e.touches[0].pageX,
      y: e.touches[0].pageY
    };
    this.touchStarted = true;
  },

  onTouchMove: function (e) {
    var deltaY, deltaX;
    var yawObject = this.yawObject;
    var pitchObject = this.pitchObject;
    if (!this.touchStarted) { return; }
    deltaY = 2 * Math.PI * (e.touches[0].pageX - this.touchStart.x) /
            this.el.sceneEl.canvas.clientWidth;
    if (!this.data.gyroEnabled) {
      deltaX = 2 * Math.PI * (e.touches[0].pageY - this.touchStart.y) /
        this.el.sceneEl.canvas.clientHeight;
    }
    // Limits touch orientaion to to yaw (y axis)
    yawObject.rotation.y -= deltaY * 0.5;
    pitchObject.rotation.x -= deltaY * 0.002;
    pitchObject.rotation.x = Math.max(-PI_2, Math.min(PI_2, pitchObject.rotation.x));
    if (!this.data.gyroEnabled) pitchObject.rotation.x -= deltaX * 0.5;
    this.touchStart = {
      x: e.touches[0].pageX,
      y: e.touches[0].pageY
    };
  },

  onTouchEnd: function () {
    this.touchStarted = false;
  },

  onExitVR: function () {
    this.previousHMDPosition.set(0, 0, 0);
  }
});
}
if(!AFRAME.components["info-listener"]){

AFRAME.registerComponent('info-listener', {
    
  init: function() {
   var mousedown=0;  
document.body.addEventListener("mousedown", function(){
  mousedown=1;
});
    document.body.addEventListener("mouseup", function(){
 
  mousedown=0;
});
     
    

      //  this.el.addEventListener('click',(e)=>{
  
        
      //             console.log(this);
      //             callbacks.activeinfo(this.data.info,{isMobile:true})
      //             window.onclick = function(event) {
      //               if(document.getElementById("infocards-modal")==event.target){
      //             callbacks.disableinfo()
      //               }
      //             }
      // });
   
    
      
      
        
    
    
    
  }

})
}
if(!AFRAME.components["info-banner-listener"]){
  var frustum1;
var pos1;
  AFRAME.registerComponent('info-banner-listener', {
 
    init: function() {
        var scene=document.querySelector('a-scene');
       // this.oldcam = scene.camera;
       // this.camera.setAttribute('fov',100)
       // this.newcam = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.0005, 30000);
        //this.newcam.position=this.oldcam.position;
        //this.camera=this.newcam
       // console.log(this.camera)
       
        this.camera = scene.camera;
        this.el.setAttribute('visible',true);
        if(this.el.childNodes[0])
        this.el.childNodes[0].childNodes[1].childNodes[1].setAttribute('scale','0 0 0');
      },
      tick: function() {
      
        //console.log(this.newcam.rotation.y)
        // this.newcam.rotation = document.getElementById('player').components['modlook-controls'].yawObject.rotation;
       //  console.log(this.newcam.rotation)
        //console.log(document.getElementById('player').components['modlook-controls'].yawObject.rotation.y)
        //console.log(document.querySelector('[camera]').getAttribute('rotation').y)
         
        //console.log(document.getElementById('player').components['modlook-controls'].yawObject.rotation)
        //this.newcam.rotation = document.getElementById('player').components['modlook-controls'].yawObject.rotation
        if (this.camera !== 0) {
         
          
          frustum1 = new THREE.Frustum();
          
          frustum1.setFromMatrix(new THREE.Matrix4().multiplyMatrices(this.camera.projectionMatrix, this.camera.matrixWorldInverse));
          pos1 = this.el.getAttribute('position');
          if (frustum1.containsPoint(pos1)) {
          
            this.el.childNodes[0].setAttribute('visible',true);
  
            this.el.childNodes[0].childNodes[1].setAttribute('animation',{
                property:'scale',
                from:'6 0 6',
                to:'6 6 6',
                dur:550,
                easing:	'linear'
            })
            
            this.el.childNodes[0].childNodes[3].setAttribute('animation',{
                property:'scale',
                from:'0 1 1',
                to:'1 1 1',
                dur:350,
                delay:550,
                easing:	'linear'
            })
            
          setTimeout(()=>{
            if(this.el.childNodes[0])
                this.el.childNodes[0].childNodes[3].childNodes[1].setAttribute('scale','6 6 6');
            },350)
  
  
          }
          else{
            this.el.childNodes[0].childNodes[3].setAttribute('scale','0 1 1')
            this.el.childNodes[0].childNodes[3].childNodes[1].setAttribute('scale','0 0 0');
            this.el.childNodes[0].childNodes[1].removeAttribute('animation');
            this.el.childNodes[0].childNodes[3].removeAttribute('animation');
            this.el.childNodes[0].setAttribute('visible',false);
          }
        }
      }
    })
  
}
if(!AFRAME.components["listen-camera"]){

  AFRAME.registerComponent('listen-camera', {
 
    init: function() {
      },
      tick: function() {
        if(callbacks && callbacks.SendRotation){
          callbacks.SendRotation(document.getElementById('MYVIEW').object3D.getWorldPosition().x+" "+document.getElementById('MYVIEW').object3D.getWorldPosition().y+" "+document.getElementById('MYVIEW').object3D.getWorldPosition().z);
          
        }
        
      }
    })
  
}
if(!AFRAME.components["info-listener"]){

  AFRAME.registerComponent('info-listener', {
      
    init: function() {
     var mousedown=0;  
  document.body.addEventListener("mousedown", function(){
    mousedown=1;
  });
      document.body.addEventListener("mouseup", function(){
   
    mousedown=0;
  });
       
      
  
        //  this.el.addEventListener('click',(e)=>{
    
          
        //             console.log(this);
        //             callbacks.activeinfo(this.data.info,{isMobile:true})
        //             window.onclick = function(event) {
        //               if(document.getElementById("infocards-modal")==event.target){
        //             callbacks.disableinfo()
        //               }
        //             }
        // });
     
      
        
        
          
      
      
      
    }
  
  })
  }
function isNullVector (vector) {
  return vector.x === 0 && vector.y === 0 && vector.z === 0;
}

}


function find_angle(A,C,B={_x:0,_y:0,_z:0}) {
  // var AB = Math.sqrt(Math.pow(B._x-A._x,2)+ Math.pow(B._x-A._x,2));    
  // var BC = Math.sqrt(Math.pow(B._x-C._x,2)+ Math.pow(B._x-C._x,2)); 
  // var AC = Math.sqrt(Math.pow(C._x-A._x,2)+ Math.pow(C._x-A._x,2));
  // return Math.acos((BC*BC+AB*AB-AC*AC)/(2*BC*AB));
  let angle = Math.PI + Math.atan2(A._x + C._x, A._y - C._y);
  return ((angle * 180 / Math.PI)%90)
}