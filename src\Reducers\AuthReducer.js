const Initial_State = {

    auth: false,
    plan:false,
    online:false
}


export  function AuthHandle(state = Initial_State, action) {
    switch (action.type) {
    case "AUTH_VALIDATE": 
              return {...state,auth:action.payload}
    case "PLAN_VALIDATE":
    return {...state, plan:action.payload}
    case "SET_PROFILE_DETAILS":
        return {...state,auth:{...state.auth,Profile_Pic:action.payload.Profile_Pic,UserName:action.payload.UserName}}
    case "ONLINE_STATUS":
        return {...state,online:action.payload}
        default:
            return state
    }
}