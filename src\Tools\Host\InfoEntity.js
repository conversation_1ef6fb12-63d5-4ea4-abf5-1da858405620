import React from "react"
import {Resolvepath} from "../../components/Resolvepath"
class InfoEntity extends React.Component{
    constructor(props){
        super(props);

        
    }

    render(){
        return(
            <a-entity  id="info">
{
            Object.keys(this.props.Info).map(key => {
            var splitrot=this.props.Info[key].position.split(" ");
                        var a= parseFloat(splitrot[0]*120);
                        var b= parseFloat(splitrot[1]*120);
                        var c= parseFloat(splitrot[2]*120);
                        var scalex;
                         var scaley;
                        if(this.props.Info[key]["iconscale"]!=undefined && this.props.Info[key]["iconscale"]!=""){
                            var spliticonscale=this.props.Info[key]['iconscale'].split(" ");
                         scalex=parseFloat(spliticonscale[0]*120);
                          scaley=parseFloat(spliticonscale[1]*120);
                        }
                        else{
                             scalex="0.2"
                             scaley="0.2";
                        }
                        
                        if(this.props.Info[key].infobanner!==undefined && this.props.Info[key].infobanner===1)
                        {
                          var cardColor='';
                          var min_banner='';
                          var class_onclick='';
                          var desc_banner;
                          var x1,x2,o2;

                          if(this.props.Info[key].cardColor){
                            if(this.props.Info[key].cardtextcolor!==undefined && this.props.Info[key].cardtextcolor!==''){
                              cardColor=`style="background:`+this.props.Info[key].cardColor+`;color:`+this.props.Info[key].cardtextcolor+`"`;
                            }
                            else{
                              cardColor=`style="background:`+this.props.Info[key].cardColor+`;color:#fff"`;
                            }
                            
                          }
                          else{
                             if(this.props.Info[key].cardtextcolor!==undefined && this.props.Info[key].cardtextcolor!==''){
                                cardColor=`style="background:#3366ff:color:`+this.props.Info[key].cardtextcolor+`"`;
                             }
                            else{
                               cardColor='style="background:#3366ff;color:#fff"'
                             }
                          }

                          if(this.props.Info[key].infodistance!==undefined && this.props.Info[key].infodistance!==''){
                            if(this.props.Info[key].infodistancecolor!==undefined && this.props.Info[key].infodistancecolor!==''){
                                if(this.props.Info[key].infodistancetextcolor!==undefined && this.props.Info[key].infodistancetextcolor!==''){
                                  min_banner= `<div style="background:`+this.props.Info[key].infodistancecolor+`;color:`+this.props.Info[key].infodistancetextcolor+`" class="min_banner">`+this.props.Info[key].infodistance+`</div>`
                                }
                              else{
                                  min_banner= `<div style="background:`+this.props.Info[key].infodistancecolor+`;color:#fff" class="min_banner">`+this.props.Info[key].infodistance+`</div>` 
                                }  
                            }
                            else{
                                if(this.props.Info[key].infodistancetextcolor!==undefined && this.props.Info[key].infodistancetextcolor!==''){
                                   min_banner= `<div style="background:#000;color:`+this.props.Info[key].infodistancetextcolor+`" class="min_banner">`+this.props.Info[key].infodistance+`</div>`
                                }
                                else{
                                 min_banner= `<div style="background:#000;color:#fff;" class="min_banner">`+this.props.Info[key].infodistance+`</div>`
                                }
                            }
                        }
                          if(this.props.Info[key].infobannertype){

                   
                          var Image=this.props.GetImageId(this.props.Info[key]['dest-image'])
                          x1 = Image.key;
                          x2=Image.data.rotation;
                          o2=Image.data.offset;
                          var name = Image.data.name
                          if(x2==undefined){
                            x2=null;
                          }
                          if(o2==undefined){
                            o2=0;
                          }
                      let destimageurl=Resolvepath(this.props.Info[key]['dest-image']);
                        class_onclick='class="boxes"';
                    
                }
              else{
                class_onclick='';
                 
                }
                if(min_banner!==''){
                  desc_banner = `<a-entity>
                            <a-entity position="0 2.2 0" scale="6 6 6" `+class_onclick+` htmlembed="styleSheetId: 3dcards"   rotation="0 0 0">
                              <div class="stick"></div>
                            </a-entity>
                            <a-entity>
                              <a-entity position="3.7 3.18 2" scale="6 6 6" `+class_onclick+` rotation="0 0 0" htmlembed="styleSheetId: 3dcards">
                                <div style="width:300px;display:flex;flex-direction:column"><div `+cardColor+` class="des_banner">`+this.props.Info[key].Description.toLowerCase()+`</div>`+min_banner+`</div>
                              </a-entity>
                             
                          </a-entity></a-entity>`;
                }
               else{
                  desc_banner = `<a-entity>
                            <a-entity position="0 2.2 0" scale="6 6 6" `+class_onclick+` htmlembed="styleSheetId: 3dcards"   rotation="0 0 0">
                              <div class="stick"></div>
                            </a-entity>
                            <a-entity>
                              <a-entity position="3.7 3.5 2" scale="6 6 6" `+class_onclick+` rotation="0 0 0" htmlembed="styleSheetId: 3dcards">
                                <div style="width:300px;display:flex;flex-direction:column"><div `+cardColor+` class="des_banner">`+this.props.Info[key].Description.toLowerCase()+`</div>`+min_banner+`</div>
                              </a-entity>
                             
                          </a-entity></a-entity>`;
                }
                return (
                  <a-image
                    
                    look-at='#cam1'
                    class={this.props.Info[key].infobannertype?"hotspot":""}
                    onClick={()=>{this.props.change(this.props.Info[key]['dest-image'])}}
                    src={this.props.Info[key].customicon!=undefined && this.props.Info[key].customicon!=""?this.props.Info[key].customicon:"#infoimage"}
                    scale={scalex+" "+scaley}
                    look-at="#cam1"
                    position={a+" "+b+" "+c}
                    info-banner-listener
                    dangerouslySetInnerHTML={{
                      __html: desc_banner}}>
                    
                  </a-image>
                )
              
              }else{
                          return (
                <a-image
                  look-at='#cam1'
                  onClick={()=>{this.props.SetInfo("Info",
                  this.props.Info[key],
                  key,
                  window.screen.width<768,
                  {position:a+" "+b+" "+c,scale:scalex+" "+scaley,icon:this.props.Info[key].customicon!=undefined && this.props.Info[key].customicon!=""?this.props.Info[key].customicon:"#infoimage"}
                  )}}
                  class="hotspot"
                  src={this.props.Info[key].customicon!=undefined && this.props.Info[key].customicon!=""?this.props.Info[key].customicon:"#infoimage"}
                  scale={scalex+" "+scaley}
                  look-at="#cam1"
                  position={a+" "+b+" "+c}
                  info-listener={"info:"+key}>
                </a-image>
              )
                        }
              
            })



          }</a-entity>
     
        )
    }
}

export default InfoEntity;