import React from "react"
import {connect} from "react-redux"
 class PINVIDEO extends React.Component{

  
    
    render(){
        return (

            <>
               {this.props.HostStatus?
            <a-entity    look-at="#cam1" position="0 0 -2"    rotation="0 0 0" visible="true">

           <a-videosphere   src="#HOST_VIDEO" width="1.3"  geometry="primitive: circle;radius:0.5;" rotation="0 -180 0" height="0.75" position="0 0.59 -0.301">
            <a-circle color="#2979FF" position="0 0 0.001" rotation="0 -180 0" radius="0.55"></a-circle>
           </a-videosphere>
           </a-entity>:""}
</> 
        )
    }
}

const mapStateToProps=(state)=>{
    return {
      HostStatus:state.Call.HostStatus,
    }
  }
  export default connect(mapStateToProps,null)(PINVIDEO);