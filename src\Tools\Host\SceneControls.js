import React from "react";

import BottomSlider from "./BottomSlider";
import Share from "../ToolComponents/ShareModal";
import DocumentModal from "./DocumentModal";
import Switchproject from "./Switchproject";
import Floorplan from './Floorplan';
import CloseModal from './CloseModal';
import Settings from "../ToolComponents/Settings";
// import MapModal from "./Mapmodal";
import Firebase from "../../config/Firebase"
import { connect } from "react-redux";
import * as HostActions from "../../Actions/HostAction"
import {Tooltip} from "flowbite-react";
import "./SceneControls.css"
import { isMobileScreen, addScreenResizeListener } from '../../utils/screenUtils';
class SceneControls extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      share: true,
      document: false,
      menu_bar: true,
      project: false,
      floorplan: false,
      data: this.props.data,
      pid: this.props.pid,
      close: false,
      map: false,
      floorplandata: "false",
      pdfdata: "false",
      settings: false,
      mapdata: "",
      projectList: false,
      selectedImage: false,
      isClicked: false,
      isSmallScreen: false,
    }
  }



  opneProjectList = () => {
    this.setState({ projectList: !this.state.projectList });
  }
  setSelectedImage = (e) => {
    this.setState({ selectedImage: e });
  }




  lockUser = () => {
    this.props.setlock();
  }

  componentDidMount() {


    window.scrollTo(0, 0);
    this.audioEl = document.getElementsByClassName("audio-element")[0]
    document.getElementById('menu_bar').click();
    // this.handleOutsideClick = this.handleOutsideClick.bind(this);
    // document.addEventListener('click', this.handleOutsideClick, false);
    this.menu.addEventListener("click", this.menu_bar_open);
    //this.menu_bar_open();


  }



  menu_bar_open = (event) => {

    if (this.state.menu_bar) {
      this.setState({
        menu_bar: false
      })
      document.getElementById('menu_bar').classList.remove('menu_option_click');
      // document.getElementById('tools_div').classList.remove('show');
      // document.removeEventListener('click', this.handleOutsideClick, false);

    }
    else {
      this.setState({
        menu_bar: true
      })
      // document.addEventListener('click', this.handleOutsideClick, false);
      // document.getElementById('tools_div').classList.add('show');
      document.getElementById('menu_bar').classList.add('menu_option_click');
    }
  }
  open_close = (name, flag) => {
    // document.getElementById('tools_div').classList.remove('show');

    this.setState({
      [name]: flag
    })

    if (name === 'map') {
      if (flag) {
        this.props.senddata({ actiontype: "map", data: this.props.data["latlng"] });
        this.setState({
          mapdata: this.props.data["latlng"],
          map: true
        })

      }
      else {
        this.props.senddata({ actiontype: "map", data: "false" });
        this.setState({
          mapdata: false,
          map: true
        })
      }
    }


    this.menu_bar_open();
    if (!flag) {
      this.props.senddata({ actiontype: "floorplan", roomid: this.props.roomId, data: false, pin: null })
    }


    // this.setState({
    //   [name]:flag
    // })
  }
  handleClick = () => {
    this.setState((prevState) => ({
      isClicked: !prevState.isClicked,
    }));
  };

  handleResize = () => {
    this.setState({
      isSmallScreen: isMobileScreen(), // Use the utility function
    });
  };


  componentDidMount() {
    // Initial check for screen size
    this.handleResize();

    // Add screen resize listener using the utility function
    this.removeResizeListener = addScreenResizeListener(() => {
      this.handleResize();
    });
  }

  componentWillUnmount() {
    // Clean up screen resize listener
    if (this.removeResizeListener) {
      this.removeResizeListener();
    }
  }

  render() {
    const { isClicked, isSmallScreen } = this.state;

    return (

      <>
        {/* MOBILE VIEW */}
        <div style={{ display: this.props.ShowControls ? "block" : "none" }}>

          <div id="" className="w-[150px] m-auto fixed top-2 left-1/2 -translate-x-2/4 rounded-sm border-sky-800 border-1 block sm:hidden" style={{
            boxShadow: '0px 17px 21px 0px #0000000D',
          }}>



            <div onClick={this.opneProjectList} className={`flex justify-between items-center p-3 border-sky-800 h-[40px] w-full  text-white ${this.state.projectList ? "bg-[black]/40 bg-opacity-40 backdrop-blur-lg rounded-t-[inherit]" : "bg-[black]/10 bg-opacity-10 backdrop-blur-[6px] rounded-[inherit]"} cursor-pointer `}>
              <div className="flex items-center whitespace-nowrap overflow-hidden">
                <div className="text-ellipsis whitespace-nowrap overflow-hidden">{this.state.selectedImage ? this.state.selectedImage : "select"}</div>
              </div>
              <div className="flex justify-center items-center">
                {this.state.projectList ?
                  <svg className="w-6 h-6" viewBox="0 0 12 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6.46824 1.16691L11.8083 6.50706C11.9319 6.63057 12 6.79545 12 6.97125C12 7.14706 11.9319 7.31193 11.8083 7.43544L11.4151 7.82871C11.1589 8.08461 10.7425 8.08461 10.4867 7.82871L6.00249 3.34445L1.51326 7.83369C1.38965 7.9572 1.22487 8.02539 1.04916 8.02539C0.873261 8.02539 0.708482 7.9572 0.584775 7.83369L0.191706 7.44042C0.0680971 7.31681 -3.81757e-08 7.15203 -4.58603e-08 6.97623C-5.3545e-08 6.80043 0.0680971 6.63555 0.191706 6.51204L5.53664 1.16691C5.66064 1.04311 5.8262 0.975106 6.00219 0.975497C6.17888 0.975106 6.34434 1.04311 6.46824 1.16691Z" fill="#F0F0F0" />
                  </svg>
                  :
                  <svg className="w-6 h-6" viewBox="0 0 12 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6.46824 7.83355L11.8083 2.4934C11.9319 2.36989 12 2.20501 12 2.02921C12 1.8534 11.9319 1.68852 11.8083 1.56501L11.4151 1.17175C11.1589 0.915848 10.7425 0.915848 10.4867 1.17175L6.00249 5.65601L1.51326 1.16677C1.38965 1.04326 1.22487 0.975067 1.04916 0.975067C0.873261 0.975067 0.708482 1.04326 0.584775 1.16677L0.191705 1.56004C0.0680967 1.68365 4.29381e-08 1.84843 3.52535e-08 2.02423C2.75689e-08 2.20003 0.0680967 2.36491 0.191705 2.48842L5.53664 7.83355C5.66064 7.95735 5.8262 8.02535 6.00219 8.02496C6.17888 8.02535 6.34434 7.95735 6.46824 7.83355Z" fill="#F0F0F0" />
                  </svg>
                }
              </div>
            </div>

            <div className="rounded-b-[inherit]">
              {this.state.projectList ?
                <div className="h-[190px] p-2.5 px-1 bg-[black]/40 bg-opacity-40 backdrop-blur-xl rounded-b-[inherit]">
                  <div className="flex flex-col h-full  overflow-auto rounded-b-[inherit] SceneControlsScrollbar">
                    {Object.keys(this.props.data.images).map((value, index) => {
                      if (this.props.data.images[value]["static"] == undefined && this.props.data.images[value]["static"] != 1)
                        return (

                          <div className="flex justify-start items-center py-3 px-2 border-sky-800 h-[34px] w-full text-white text-sm cursor-pointer" onClick={() => { this.props.changeImage(value); this.setSelectedImage(this.props.data.images[value]['name'].replaceAll(/_|-/g, " ")) }}>
                            <div className="flex justify-center items-center  whitespace-nowrap overflow-hidden">
                              <div className="overflow-hidden text-ellipsis text-white">{this.props.data.images[value]['name'].replaceAll(/_|-/g, " ")}</div>
                            </div>
                          </div>

                        );
                    })}
                  </div>
                </div>
                : ""}
            </div>

          </div>



          {/* for mobile view of scene controls */}
          <div className=" fixed bottom-2 right-2 cursor-pointer  border-[none] flex flex-col-reverse sm:hidden">
            <div className="flex-auto">
              <Tooltip
                arrowSize={7.5}
                background="rgb(0 0 0 / 0.1)"
                border="#fff"
                color="#fff"
                content={!isClicked ? "Show Controls" : "Hide Controls"}
                fadeDuration={0}
                fadeEasing="linear"
                fixed={false}
                fontFamily="inherit"
                fontSize="1rem"
                offset={0}
                padding={3}
                direction="left"
                radius={4}
                zIndex={1}
              >
              <button className=" shadow-[0px_17px_21px_0px_#0000000D]  rounded-full bg-[black]/10 bg-opacity-10 p-2.5" onClick={this.handleClick}>
                {!isClicked ? (<svg className="w-6 h-6" fill="white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="settings"><rect width="24" height="24" opacity="0" /><circle cx="12" cy="12" r="1.5" /><path d="M21.89 10.32L21.1 7.8a2.26 2.26 0 0 0-2.88-1.51l-.34.11a1.74 1.74 0 0 1-1.59-.26l-.11-.08a1.76 1.76 0 0 1-.69-1.43v-.28a2.37 2.37 0 0 0-.68-1.68 2.26 2.26 0 0 0-1.6-.67h-2.55a2.32 2.32 0 0 0-2.29 2.33v.24a1.94 1.94 0 0 1-.73 1.51l-.13.1a1.93 1.93 0 0 1-1.78.29 2.14 2.14 0 0 0-1.68.12 2.18 2.18 0 0 0-1.12 1.33l-.82 2.6a2.34 2.34 0 0 0 1.48 2.94h.16a1.83 1.83 0 0 1 1.12 1.22l.06.16a2.06 2.06 0 0 1-.23 1.86 2.37 2.37 0 0 0 .49 3.3l2.07 1.57a2.25 2.25 0 0 0 1.35.43A2 2 0 0 0 9 22a2.25 2.25 0 0 0 1.47-1l.23-.33a1.8 1.8 0 0 1 1.43-.77 1.75 1.75 0 0 1 1.5.78l.12.17a2.24 2.24 0 0 0 3.22.53L19 19.86a2.38 2.38 0 0 0 .5-3.23l-.26-.38A2 2 0 0 1 19 14.6a1.89 1.89 0 0 1 1.21-1.28l.2-.07a2.36 2.36 0 0 0 1.48-2.93zM12 15.5a3.5 3.5 0 1 1 3.5-3.5 3.5 3.5 0 0 1-3.5 3.5z" /></g></g></svg>) : (
                  <svg className="w-6 h-6" fill="white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="close"><rect width="24" height="24" transform="rotate(180 12 12)" opacity="0" /><path d="M13.41 12l4.3-4.29a1 1 0 1 0-1.42-1.42L12 10.59l-4.29-4.3a1 1 0 0 0-1.42 1.42l4.3 4.29-4.3 4.29a1 1 0 0 0 0 1.42 1 1 0 0 0 1.42 0l4.29-4.3 4.29 4.3a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42z" /></g></g></svg>
                )}
              </button></Tooltip>
            </div>
            <div className="flex-auto ml-2 flex  flex-col-reverse mb-[5px]">
              {/* <div onClick={() => this.open_close('share', true)} name="share" className={`${isSmallScreen && isClicked ? 'relative mt-2 ml-[-10px] h-[46px] cursor-pointer w-[46px] mb-[10px]  border-[none] shadow-[0px_17px_21px_0px_#0000000D]   p-[11px] rounded-full bg-[black]/10 bg-opacity-10' : 'hidden'}`} href="#" data-tip="Share">
              <svg className="w-6 h-6" viewBox="0 0 24 24">
                <defs>
                  <path id="prefix__b" d="M16 16c-.552 0-1-.448-1-1s.448-1 1-1 1 .448 1 1-.448 1-1 1M3 10c-.552 0-1-.448-1-1s.448-1 1-1 1 .448 1 1-.448 1-1 1m13-8c.552 0 1 .448 1 1s-.448 1-1 1-1-.448-1-1 .448-1 1-1m0 10c-.817 0-1.557.33-2.099.861L5.966 9.335C5.979 9.224 6 9.114 6 9c0-.114-.021-.224-.034-.335l7.935-3.526C14.443 5.67 15.183 6 16 6c1.654 0 3-1.346 3-3s-1.346-3-3-3-3 1.346-3 3c0 .***************.335L5.099 6.861C4.557 6.33 3.817 6 3 6 1.346 6 0 7.346 0 9s1.346 3 3 3c.817 0 1.557-.33 2.099-.861l7.935 3.526c-.013.111-.034.221-.034.335 0 1.654 1.346 3 3 3s3-1.346 3-3-1.346-3-3-3" />
                </defs>
                <g fill="#ffffff" fillRule="evenodd" transform="translate(2 3)">
                  <use className="icon_svg" className="svgicons-reseller" xlinkHref="#prefix__b" />
                </g>
              </svg>
            </div> */}

              {!this.props.lock ?
                <>
                  <Tooltip
                    arrowSize={7.5}
                    background="rgb(0 0 0 / 0.1)"
                    border="#fff"
                    color="#fff"
                    content={"Lock"}
                    fadeDuration={0}
                    fadeEasing="linear"
                    fixed={false}
                    fontFamily="inherit"
                    fontSize="1rem"
                    offset={0}
                    padding={3}
                    direction="left"
                    radius={4}
                    zIndex={1}
                  >
                    <div onClick={this.lockUser} data-toggle="tooltip" data-placement="right" className={`${isSmallScreen && isClicked ? 'relative mt-2 ml-[-10px] h-[46px] cursor-pointer w-[46px]  border-[none] shadow-[0px_17px_21px_0px_#0000000D]  p-2.5 rounded-full bg-[black]/10 bg-opacity-10' : 'hidden'}`} href="#" data-tip="Lock">
                      <svg className="w-6 h-6" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g clip-path="url(#clip0_148_23894)">
                          <path d="M21.875 10.5H21V7C21 3.1395 17.8605 0 14 0C10.1395 0 7 3.1395 7 7V10.5H6.125C4.67833 10.5 3.5 11.6772 3.5 13.125V25.375C3.5 26.8228 4.67833 28 6.125 28H21.875C23.3217 28 24.5 26.8228 24.5 25.375V13.125C24.5 11.6772 23.3217 10.5 21.875 10.5ZM9.33333 7C9.33333 4.42633 11.4263 2.33333 14 2.33333C16.5737 2.33333 18.6667 4.42633 18.6667 7V10.5H9.33333V7ZM15.1667 19.509V22.1667C15.1667 22.8107 14.6452 23.3333 14 23.3333C13.3548 23.3333 12.8333 22.8107 12.8333 22.1667V19.509C12.1392 19.1042 11.6667 18.3598 11.6667 17.5C11.6667 16.2132 12.7132 15.1667 14 15.1667C15.2868 15.1667 16.3333 16.2132 16.3333 17.5C16.3333 18.3598 15.8608 19.1042 15.1667 19.509Z" fill="white" />
                        </g>
                        <defs>
                          <clipPath id="clip0_148_23894">
                            <rect width="28" height="28" fill="white" transform="matrix(-1 0 0 1 28 0)" />
                          </clipPath>
                        </defs>
                      </svg>
                    </div></Tooltip>
                </>
                :
                <>
                  <Tooltip
                    arrowSize={7.5}
                    background="rgb(0 0 0 / 0.1)"
                    border="#fff"
                    color="#fff"
                    content={"Unlock"}
                    fadeDuration={0}
                    fadeEasing="linear"
                    fixed={false}
                    fontFamily="inherit"
                    fontSize="1rem"
                    offset={0}
                    padding={3}
                    direction="left"
                    radius={4}
                    zIndex={1}
                  >
                    <div onClick={this.lockUser} data-toggle="tooltip" data-placement="right" className={`${isSmallScreen && isClicked ? 'relative mt-2 ml-[-10px] h-[46px] cursor-pointer w-[46px]  border-[none] shadow-[0px_17px_21px_0px_#0000000D]   p-2.5 rounded-full bg-[black]/10 bg-opacity-10' : 'hidden'}`} href="#" data-tip="Unlock">
                      <svg className="w-6 h-6" viewBox="0 0 22 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M18.875 10.5H18V7C18 3.1395 14.8605 0 11 0C7.1395 0 4 3.1395 4 7V10.5H3.125C1.67833 10.5 0.5 11.6772 0.5 13.125V25.375C0.5 26.8228 1.67833 28 3.125 28H18.875C20.3217 28 21.5 26.8228 21.5 25.375V13.125C21.5 11.6772 20.3217 10.5 18.875 10.5ZM6.33333 7C6.33333 4.42633 8.42633 2.33333 11 2.33333C13.5737 2.33333 15.6667 4.42633 15.6667 7V10.5H6.33333V7ZM12.1667 19.509V22.1667C12.1667 22.8107 11.6452 23.3333 11 23.3333C10.3548 23.3333 9.83333 22.8107 9.83333 22.1667V19.509C9.13917 19.1042 8.66667 18.3598 8.66667 17.5C8.66667 16.2132 9.71317 15.1667 11 15.1667C12.2868 15.1667 13.3333 16.2132 13.3333 17.5C13.3333 18.3598 12.8608 19.1042 12.1667 19.509Z" fill="#1c74d0" />
                      </svg>
                    </div>
                  </Tooltip>
                </>

              }
              {!this.props.annotate ?
                <>
                  <Tooltip
                    arrowSize={7.5}
                    background="rgb(0 0 0 / 0.1)"
                    border="#fff"
                    color="#fff"
                    content={"Annotate"}
                    fadeDuration={0}
                    fadeEasing="linear"
                    fixed={false}
                    fontFamily="inherit"
                    fontSize="1rem"
                    offset={0}
                    padding={3}
                    direction="left"
                    radius={4}
                    zIndex={1}
                  >

                    <div onClick={this.props.EnableAnnotate} data-toggle="tooltip" data-placement="right" className={`${isSmallScreen && isClicked ? 'relative mt-2 ml-[-10px] h-[46px] cursor-pointer w-[46px]  border-[none] shadow-[0px_17px_21px_0px_#0000000D]   p-2.5 rounded-full bg-[black]/10 bg-opacity-10' : 'hidden'}`} href="#" data-tip="Annotate">
                      <svg className="w-6 h-6" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g clip-path="url(#clip0_178_22178)">
                          <path d="M19.487 21.4072H12.7417C12.3537 21.4072 12.0391 21.7218 12.0391 22.1099C12.0391 22.4979 12.3537 22.8125 12.7417 22.8125H19.487C19.8751 22.8125 20.1897 22.4979 20.1897 22.1099C20.1897 21.7218 19.8751 21.4072 19.487 21.4072Z" fill="white" />
                          <path d="M3.12402 18.7354L0.20985 21.6096C-0.236091 22.0495 0.0757399 22.8125 0.703244 22.8125H5.24698C5.43332 22.8125 5.61202 22.7385 5.74379 22.6067L6.36956 21.9809L3.12402 18.7354Z" fill="white" />
                          <path d="M5.00197 13.1182C4.21759 13.9442 3.90248 15.1323 4.17951 16.2405C4.29928 16.7196 4.24213 17.2222 4.03467 17.6583L7.4494 21.0731C7.8856 20.8656 8.38817 20.8085 8.86723 20.9282C9.97656 21.2056 11.1631 20.8894 11.989 20.1052L5.00197 13.1182Z" fill="white" />
                          <path d="M23.3046 4.22411L21.2804 2.03122C20.3093 0.979227 18.6793 0.90203 17.6128 1.85003L6.02295 12.1521L12.9625 19.0917L23.3131 7.77084C24.2313 6.76654 24.2289 5.22551 23.3046 4.22411Z" fill="white" />
                        </g>
                        <defs>
                          <clipPath id="clip0_178_22178">
                            <rect width="24" height="24" fill="white" />
                          </clipPath>
                        </defs>
                      </svg>
                    </div></Tooltip>
                </>
                :
                <>
                  <Tooltip
                    arrowSize={7.5}
                    background="rgb(0 0 0 / 0.1)"
                    border="#fff"
                    color="#fff"
                    content={"Annotate"}
                    fadeDuration={0}
                    fadeEasing="linear"
                    fixed={false}
                    fontFamily="inherit"
                    fontSize="1rem"
                    offset={0}
                    padding={3}
                    direction="left"
                    radius={4}
                    zIndex={1}
                  >
                    <div onClick={this.props.EnableAnnotate} data-toggle="tooltip" data-placement="right" className={`${isSmallScreen && isClicked ? 'relative mt-2 ml-[-10px] h-[46px] cursor-pointer w-[46px]  border-[none] shadow-[0px_17px_21px_0px_#0000000D]   p-2.5 rounded-full bg-[black]/10 bg-opacity-10' : 'hidden'}`} href="#" data-tip="Annotate">
                      <svg className="w-6 h-6" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g clip-path="url(#clip0_178_22234)">
                          <path d="M19.487 21.4072H12.7417C12.3537 21.4072 12.0391 21.7218 12.0391 22.1099C12.0391 22.4979 12.3537 22.8125 12.7417 22.8125H19.487C19.8751 22.8125 20.1897 22.4979 20.1897 22.1099C20.1897 21.7218 19.8751 21.4072 19.487 21.4072Z" fill="#1C74D0" />
                          <path d="M3.12402 18.7354L0.20985 21.6096C-0.236091 22.0495 0.0757399 22.8125 0.703244 22.8125H5.24698C5.43332 22.8125 5.61202 22.7385 5.74379 22.6067L6.36956 21.9809L3.12402 18.7354Z" fill="#1C74D0" />
                          <path d="M5.00197 13.1182C4.21759 13.9442 3.90248 15.1323 4.17951 16.2405C4.29928 16.7196 4.24213 17.2222 4.03467 17.6583L7.4494 21.0731C7.8856 20.8656 8.38817 20.8085 8.86723 20.9282C9.97656 21.2056 11.1631 20.8894 11.989 20.1052L5.00197 13.1182Z" fill="#1c74d0" />
                          <path d="M23.3046 4.22411L21.2804 2.03122C20.3093 0.979227 18.6793 0.90203 17.6128 1.85003L6.02295 12.1521L12.9625 19.0917L23.3131 7.77084C24.2313 6.76654 24.2289 5.22551 23.3046 4.22411Z" fill="#1C74D0" />
                        </g>
                        <defs>
                          <clipPath id="clip0_178_22234">
                            <rect width="24" height="24" fill="white" />
                          </clipPath>
                        </defs>
                      </svg>

                    </div></Tooltip>

                </>
              }
              <Tooltip
                arrowSize={7.5}
                background="rgb(0 0 0 / 0.1)"
                border="#fff"
                color="#fff"
                content={"Change projects"}
                fadeDuration={0}
                fadeEasing="linear"
                fixed={false}
                fontFamily="inherit"
                fontSize="1rem"
                offset={0}
                padding={3}
                direction="left"
                radius={4}
                zIndex={1}
              >
                <div onClick={() => this.open_close('project', true)} className={`${isSmallScreen && isClicked ? 'relative mt-2 ml-[-10px] h-[46px] cursor-pointer w-[46px]  border-[none] shadow-[0px_17px_21px_0px_#0000000D]   p-2.5 rounded-full bg-[black]/10 bg-opacity-10' : 'hidden'}`} data-toggle="tooltip" href="#" data-tip="Switch project">
                  <svg className="w-6 h-6" viewBox="0 0 29 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_147_20044)">
                      <path d="M6.04018 11.8126C6.38088 12.1538 6.9365 12.1538 7.27721 11.8126L8.40869 10.6811C8.74994 10.3404 8.74994 9.78476 8.40869 9.44406L7.27721 8.31258H22.8462C24.2938 8.31258 25.4712 9.49 25.4712 10.9376C25.4712 11.6611 26.0602 12.2501 26.7837 12.2501H27.6587C28.3822 12.2501 28.9712 11.6611 28.9712 10.9376C28.9712 7.56008 26.2237 4.81258 22.8462 4.81258H7.27721L8.40869 3.68109C8.74994 3.34039 8.74994 2.78477 8.40869 2.44406L7.27721 1.31258C6.9365 0.971328 6.38088 0.971328 6.04018 1.31258L1.09916 6.25305C0.928535 6.42422 0.928535 6.70094 1.09916 6.87211L6.04018 11.8126Z" fill="white" />
                      <path d="M23.9022 16.1875C23.5615 15.8462 23.0064 15.8462 22.6652 16.1875L21.5337 17.319C21.1918 17.6609 21.1912 18.2136 21.5337 18.556L22.6652 19.6875H7.09619C5.64861 19.6875 4.47119 18.5101 4.47119 17.0625C4.47119 16.339 3.88221 15.75 3.15869 15.75H2.28369C1.56018 15.75 0.971191 16.339 0.971191 17.0625C0.971191 20.44 3.71869 23.1875 7.09619 23.1875H22.6652L21.5337 24.319C21.1918 24.6609 21.1912 25.2136 21.5337 25.556L22.6652 26.6875C23.0065 27.0288 23.561 27.0287 23.9022 26.6875L28.8432 21.747C29.0138 21.5759 29.0138 21.2991 28.8432 21.128L23.9022 16.1875Z" fill="white" />
                    </g>
                    <defs>
                      <clipPath id="clip0_147_20044">
                        <rect width="28" height="28" fill="white" transform="translate(0.971191)" />
                      </clipPath>
                    </defs>
                  </svg>
                </div></Tooltip>
              {this.props.data.hasOwnProperty('brochure') ? <>
                <Tooltip
                  arrowSize={7.5}
                  background="rgb(0 0 0 / 0.1)"
                  border="#fff"
                  color="#fff"
                  content={"Brochure"}
                  fadeDuration={0}
                  fadeEasing="linear"
                  fixed={false}
                  fontFamily="inherit"
                  fontSize="1rem"
                  offset={0}
                  padding={3}
                  direction="left"
                  radius={4}
                  zIndex={1}
                >
                  <div onClick={() => this.open_close('document', true)} data-toggle="tooltip" data-placement="right" title="" className={`${isSmallScreen && isClicked ? 'relative mt-2 ml-[-10px] h-[46px] cursor-pointer w-[46px]  border-[none] shadow-[0px_17px_21px_0px_#0000000D] p-2.5 rounded-full bg-[black]/10 bg-opacity-10' : 'hidden'}`} href="#" data-tip="Brochure">
                    <svg className="w-6 h-6 m-auto" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <g clip-path="url(#clip0_178_22153)">
                        <path d="M11.7812 3.70312H18.7502V6.28078H11.7812V3.70312Z" fill="white" />
                        <path d="M20.7016 0H9.82984C8.83797 0 8.03125 0.806719 8.03125 1.79859V18.5883C8.03125 18.9769 8.34578 19.2914 8.73438 19.2914H20.7016C21.693 19.2914 22.5002 18.4847 22.5002 17.4933V1.79859C22.5002 0.806719 21.693 0 20.7016 0ZM13.1687 17.5308H11.0781C10.6895 17.5308 10.375 17.2162 10.375 16.8277C10.375 16.4395 10.6895 16.1245 11.0781 16.1245H13.1687C13.5569 16.1245 13.8719 16.4395 13.8719 16.8277C13.8719 17.2162 13.5569 17.5308 13.1687 17.5308ZM18.3916 15.187H11.0781C10.6895 15.187 10.375 14.8725 10.375 14.4839C10.375 14.0958 10.6895 13.7808 11.0781 13.7808H18.3916C18.7797 13.7808 19.0947 14.0958 19.0947 14.4839C19.0947 14.8725 18.7797 15.187 18.3916 15.187ZM10.375 12.1402C10.375 11.752 10.6895 11.437 11.0781 11.437H17.5783C17.9664 11.437 18.2814 11.752 18.2814 12.1402C18.2814 12.5287 17.9664 12.8433 17.5783 12.8433H11.0781C10.6895 12.8433 10.375 12.5287 10.375 12.1402ZM19.4533 10.4995H11.0781C10.6895 10.4995 10.375 10.185 10.375 9.79641C10.375 9.40828 10.6895 9.09328 11.0781 9.09328H19.4533C19.8414 9.09328 20.1564 9.40828 20.1564 9.79641C20.1564 10.185 19.8414 10.4995 19.4533 10.4995ZM20.1564 6.98391C20.1564 7.3725 19.8414 7.68703 19.4533 7.68703H11.0781C10.6895 7.68703 10.375 7.3725 10.375 6.98391V3C10.375 2.61187 10.6895 2.29687 11.0781 2.29687H19.4533C19.8414 2.29687 20.1564 2.61187 20.1564 3V6.98391Z" fill="white" />
                        <path d="M6.62484 18.5887V3.77148H3.29625C2.30578 3.77148 1.5 4.5782 1.5 5.56961V22.2018C1.5 23.1937 2.30578 24.0004 3.29625 24.0004H13.9542L7.78781 20.4735C7.09875 20.1262 6.62484 19.4118 6.62484 18.5887Z" fill="white" />
                        <path d="M15.9567 23.4249C15.9642 23.3832 15.9685 23.3405 15.9685 23.2965V20.6973H11.1099L15.9567 23.4249Z" fill="white" />
                      </g>
                      <defs>
                        <clipPath id="clip0_178_22153">
                          <rect width="24" height="24" fill="white" />
                        </clipPath>
                      </defs>
                    </svg>
                  </div></Tooltip>
              </> : <>
                {/* <div data-toggle="tooltip" data-placement="right" title="" className={`${isSmallScreen && isClicked ? 'relative mt-2 ml-[-10px] h-[46px] cursor-pointer w-[46px]  border-[none] shadow-[0px_17px_21px_0px_#0000000D] p-2.5  rounded-full bg-[black]/10 bg-opacity-10' : 'hidden'}`} href="#" data-tip="Brochure">
                <svg className="w-6 h-6 m-auto" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_147_21637)">
                    <path d="M13.7441 4.32031H21.8745V7.32758H13.7441V4.32031Z" fill="#1C74D0"  />
                    <path d="M24.1512 0H11.4675C10.3103 0 9.36914 0.941172 9.36914 2.09836V21.6863C9.36914 22.1397 9.73609 22.5066 10.1895 22.5066H24.1512C25.3078 22.5066 26.2495 21.5655 26.2495 20.4088V2.09836C26.2495 0.941172 25.3078 0 24.1512 0ZM15.3629 20.4526H12.9238C12.4705 20.4526 12.1035 20.0856 12.1035 19.6323C12.1035 19.1795 12.4705 18.812 12.9238 18.812H15.3629C15.8157 18.812 16.1832 19.1795 16.1832 19.6323C16.1832 20.0856 15.8157 20.4526 15.3629 20.4526ZM21.4562 17.7182H12.9238C12.4705 17.7182 12.1035 17.3512 12.1035 16.8979C12.1035 16.4451 12.4705 16.0776 12.9238 16.0776H21.4562C21.909 16.0776 22.2765 16.4451 22.2765 16.8979C22.2765 17.3512 21.909 17.7182 21.4562 17.7182ZM12.1035 14.1635C12.1035 13.7107 12.4705 13.3432 12.9238 13.3432H20.5073C20.9602 13.3432 21.3277 13.7107 21.3277 14.1635C21.3277 14.6169 20.9602 14.9838 20.5073 14.9838H12.9238C12.4705 14.9838 12.1035 14.6169 12.1035 14.1635ZM22.6948 12.2495H12.9238C12.4705 12.2495 12.1035 11.8825 12.1035 11.4291C12.1035 10.9763 12.4705 10.6088 12.9238 10.6088H22.6948C23.1477 10.6088 23.5152 10.9763 23.5152 11.4291C23.5152 11.8825 23.1477 12.2495 22.6948 12.2495ZM23.5152 8.14789C23.5152 8.60125 23.1477 8.9682 22.6948 8.9682H12.9238C12.4705 8.9682 12.1035 8.60125 12.1035 8.14789V3.5C12.1035 3.04719 12.4705 2.67969 12.9238 2.67969H22.6948C23.1477 2.67969 23.5152 3.04719 23.5152 3.5V8.14789Z" fill="#1C74D0"  />
                    <path d="M7.72898 21.6871V4.40039H3.84562C2.69008 4.40039 1.75 5.34156 1.75 6.4982V25.9024C1.75 27.0596 2.69008 28.0008 3.84562 28.0008H16.2799L9.08578 23.8861C8.28188 23.4809 7.72898 22.6474 7.72898 21.6871Z" fill="#1C74D0"  />
                    <path d="M18.6156 27.3288C18.6244 27.2801 18.6293 27.2303 18.6293 27.1789V24.1465H12.9609L18.6156 27.3288Z" fill="#1C74D0"  />
                  </g>
                  <defs>
                    <clipPath id="clip0_147_21637">
                      <rect width="28" height="28" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
              </div>
               */}
              </>}

              {this.props.data.plans ?
                <><Tooltip
                  arrowSize={7.5}
                  background="rgb(0 0 0 / 0.1)"
                  border="#fff"
                  color="#fff"
                  content={"Floor Plans"}
                  fadeDuration={0}
                  fadeEasing="linear"
                  fixed={false}
                  fontFamily="inherit"
                  fontSize="1rem"
                  offset={0}
                  padding={3}
                  direction="left"
                  radius={4}
                  zIndex={1}
                >
                  <div onClick={() => this.open_close('floorplan', true)} data-toggle="tooltip" data-placement="right" title="" className={`${isSmallScreen && isClicked ? 'relative mt-2  -ml-2 p-[11px] h-[46px] cursor-pointer w-[46px]  border-[none] shadow-[0px_17px_21px_0px_#0000000D]   p-2.5 rounded-full bg-[black]/10 bg-opacity-10' : 'hidden'}`} href="#" data-tip="Floor Plan">
                    <svg className="w-6 h-6" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M15.5294 7.7647V11.2941C15.5294 11.684 15.8454 12 16.2353 12C16.6252 12 16.9412 11.684 16.9412 11.2941V7.7647H24V22.5882C24 23.3679 23.3679 24 22.5882 24H16.9412V15.5294C16.9412 15.1395 16.6252 14.8235 16.2353 14.8235C15.8454 14.8235 15.5294 15.1395 15.5294 15.5294V17.647H12.7059C12.316 17.647 12 17.9631 12 18.3529C12 18.7428 12.316 19.0588 12.7059 19.0588H15.5294V24H8.47059V18.3529C8.47059 17.9631 8.15456 17.647 7.7647 17.647C7.37484 17.647 7.05881 17.9631 7.05881 18.3529V24H1.41178C0.632062 24 0 23.3679 0 22.5882V13.4118H7.05881V14.1177C7.05881 14.5075 7.37484 14.8236 7.7647 14.8236C8.15456 14.8236 8.47059 14.5075 8.47059 14.1177V11.2942C8.47059 10.9043 8.15456 10.5883 7.7647 10.5883C7.37484 10.5883 7.05881 10.9043 7.05881 11.2942V12H0V1.41178C0 0.632062 0.632062 0 1.41178 0H7.05886V7.05881C7.05886 7.44867 7.37489 7.7647 7.76475 7.7647C8.15461 7.7647 8.47064 7.44867 8.47064 7.05881V0H22.5883C23.3679 0 24 0.632062 24 1.41178V6.35297H12.7059C12.316 6.35297 12 6.669 12 7.05886C12 7.44872 12.316 7.76475 12.7059 7.76475L15.5294 7.7647Z" fill="white" />
                    </svg>
                  </div></Tooltip>

                </>
                : <>
                  {/* <div data-toggle="tooltip" data-placement="right" title="" className={"opacity-80 "+`${isSmallScreen && isClicked ? 'relative h-[46px] -ml-2 p-[11px] cursor-pointer w-[46px]  border-[none] shadow-[0px_17px_21px_0px_#0000000D]   p-[11px] rounded-full bg-[black]/10 bg-opacity-10' : 'hidden'}`} href="#" data-tip="Floor Plan">
                  <svg className="w-6 h-6" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M15.5294 7.7647V11.2941C15.5294 11.684 15.8454 12 16.2353 12C16.6252 12 16.9412 11.684 16.9412 11.2941V7.7647H24V22.5882C24 23.3679 23.3679 24 22.5882 24H16.9412V15.5294C16.9412 15.1395 16.6252 14.8235 16.2353 14.8235C15.8454 14.8235 15.5294 15.1395 15.5294 15.5294V17.647H12.7059C12.316 17.647 12 17.9631 12 18.3529C12 18.7428 12.316 19.0588 12.7059 19.0588H15.5294V24H8.47059V18.3529C8.47059 17.9631 8.15456 17.647 7.7647 17.647C7.37484 17.647 7.05881 17.9631 7.05881 18.3529V24H1.41178C0.632062 24 0 23.3679 0 22.5882V13.4118H7.05881V14.1177C7.05881 14.5075 7.37484 14.8236 7.7647 14.8236C8.15456 14.8236 8.47059 14.5075 8.47059 14.1177V11.2942C8.47059 10.9043 8.15456 10.5883 7.7647 10.5883C7.37484 10.5883 7.05881 10.9043 7.05881 11.2942V12H0V1.41178C0 0.632062 0.632062 0 1.41178 0H7.05886V7.05881C7.05886 7.44867 7.37489 7.7647 7.76475 7.7647C8.15461 7.7647 8.47064 7.44867 8.47064 7.05881V0H22.5883C23.3679 0 24 0.632062 24 1.41178V6.35297H12.7059C12.316 6.35297 12 6.669 12 7.05886C12 7.44872 12.316 7.76475 12.7059 7.76475L15.5294 7.7647Z" fill="#1C74D0" />
                  </svg>
                </div>
                 */}
                </>}

            </div>
          </div>

          {/* .................big screen dimension.................. */}
          <div x-placement="top-start" className={`flex flex-nowrap backdrop-blur-[6px] w-fit fixed rounded shadow-[0px_17px_21px_0px_#0000000D] m-auto left-1/2 transform -translate-x-1/2 bottom-14 bg-[black]/10 bg-opacity-10 backdrop-blur-[6px]  ${isSmallScreen ? 'hidden' : 'flex  w-fit'}`}>
            {/* <div onClick={() => this.open_close('share', true)} name="share" className={`${isSmallScreen ? (isClicked ? 'relative h-[46px] mb-[10px] cursor-pointer w-[46px]  border-[none] shadow-[0px_17px_21px_0px_#0000000D]   p-[11px] rounded-full bg-[#ffffff1a]' : '') : 'm-[auto] p-[10px]'}` } href="#" data-tip="Share">
            <svg className="w-6 h-6" viewBox="0 0 24 24">
              <defs>
                <path id="prefix__b" d="M16 16c-.552 0-1-.448-1-1s.448-1 1-1 1 .448 1 1-.448 1-1 1M3 10c-.552 0-1-.448-1-1s.448-1 1-1 1 .448 1 1-.448 1-1 1m13-8c.552 0 1 .448 1 1s-.448 1-1 1-1-.448-1-1 .448-1 1-1m0 10c-.817 0-1.557.33-2.099.861L5.966 9.335C5.979 9.224 6 9.114 6 9c0-.114-.021-.224-.034-.335l7.935-3.526C14.443 5.67 15.183 6 16 6c1.654 0 3-1.346 3-3s-1.346-3-3-3-3 1.346-3 3c0 .***************.335L5.099 6.861C4.557 6.33 3.817 6 3 6 1.346 6 0 7.346 0 9s1.346 3 3 3c.817 0 1.557-.33 2.099-.861l7.935 3.526c-.013.111-.034.221-.034.335 0 1.654 1.346 3 3 3s3-1.346 3-3-1.346-3-3-3" />
              </defs>
              <g fill="none" fillRule="evenodd" transform="translate(2 3)">
                <use className="icon_svg" className="svgicons-reseller" xlinkHref="#prefix__b" />
              </g>
            </svg>
          </div>
           */}

            {!this.props.lock ?
              <><Tooltip
                arrowSize={7.5}
                background="rgb(0 0 0 / 0.1)"
                border="#fff"
                color="#fff"
                content={"Lock"}
                fadeDuration={0}
                fadeEasing="linear"
                fixed={false}
                fontFamily="inherit"
                fontSize="1rem"
                offset={0}
                padding={3}
                direction="up"
                radius={4}
                zIndex={1}
              >
                <div onClick={this.lockUser} data-toggle="tooltip" data-placement="right" className={`${isSmallScreen ? (isClicked ? 'relative h-[46px] cursor-pointer w-[46px]  border-[none] shadow-[0px_17px_21px_0px_#0000000D]   p-[11px] rounded-full bg-[black]/10 bg-opacity-10' : '') : 'm-[auto] p-[10px]'}`} href="#" data-tip="Lock">
                  <svg className="w-6 h-6" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_148_23894)">
                      <path d="M21.875 10.5H21V7C21 3.1395 17.8605 0 14 0C10.1395 0 7 3.1395 7 7V10.5H6.125C4.67833 10.5 3.5 11.6772 3.5 13.125V25.375C3.5 26.8228 4.67833 28 6.125 28H21.875C23.3217 28 24.5 26.8228 24.5 25.375V13.125C24.5 11.6772 23.3217 10.5 21.875 10.5ZM9.33333 7C9.33333 4.42633 11.4263 2.33333 14 2.33333C16.5737 2.33333 18.6667 4.42633 18.6667 7V10.5H9.33333V7ZM15.1667 19.509V22.1667C15.1667 22.8107 14.6452 23.3333 14 23.3333C13.3548 23.3333 12.8333 22.8107 12.8333 22.1667V19.509C12.1392 19.1042 11.6667 18.3598 11.6667 17.5C11.6667 16.2132 12.7132 15.1667 14 15.1667C15.2868 15.1667 16.3333 16.2132 16.3333 17.5C16.3333 18.3598 15.8608 19.1042 15.1667 19.509Z" fill="white" />
                    </g>
                    <defs>
                      <clipPath id="clip0_148_23894">
                        <rect width="28" height="28" fill="white" transform="matrix(-1 0 0 1 28 0)" />
                      </clipPath>
                    </defs>
                  </svg>
                </div></Tooltip>
              </>
              :
              <>
                <Tooltip
                  arrowSize={7.5}
                  background="rgb(0 0 0 / 0.1)"
                  border="#fff"
                  color="#fff"
                  content={"Unlock"}
                  fadeDuration={0}
                  fadeEasing="linear"
                  fixed={false}
                  fontFamily="inherit"
                  fontSize="1rem"
                  offset={0}
                  padding={3}
                  direction="up"
                  radius={4}
                  zIndex={1}
                >
                  <div onClick={this.lockUser} data-toggle="tooltip" data-placement="right" className={`${isSmallScreen ? (isClicked ? 'relative h-[46px] cursor-pointer w-[46px]  border-[none] shadow-[0px_17px_21px_0px_#0000000D]   p-[11px] rounded-full bg-[black]/10 bg-opacity-10' : '') : 'm-[auto] p-[10px]'}`} href="#" data-tip="Unlock">
                    <svg className="w-6 h-6" viewBox="0 0 22 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M18.875 10.5H18V7C18 3.1395 14.8605 0 11 0C7.1395 0 4 3.1395 4 7V10.5H3.125C1.67833 10.5 0.5 11.6772 0.5 13.125V25.375C0.5 26.8228 1.67833 28 3.125 28H18.875C20.3217 28 21.5 26.8228 21.5 25.375V13.125C21.5 11.6772 20.3217 10.5 18.875 10.5ZM6.33333 7C6.33333 4.42633 8.42633 2.33333 11 2.33333C13.5737 2.33333 15.6667 4.42633 15.6667 7V10.5H6.33333V7ZM12.1667 19.509V22.1667C12.1667 22.8107 11.6452 23.3333 11 23.3333C10.3548 23.3333 9.83333 22.8107 9.83333 22.1667V19.509C9.13917 19.1042 8.66667 18.3598 8.66667 17.5C8.66667 16.2132 9.71317 15.1667 11 15.1667C12.2868 15.1667 13.3333 16.2132 13.3333 17.5C13.3333 18.3598 12.8608 19.1042 12.1667 19.509Z" fill="#1C74D0" />
                    </svg>
                  </div></Tooltip>

              </>

            }{!this.props.annotate ?
              <>
                <Tooltip
                  arrowSize={7.5}
                  background="rgb(0 0 0 / 0.1)"
                  border="#fff"
                  color="#fff"
                  content={"Annotate"}
                  fadeDuration={0}
                  fadeEasing="linear"
                  fixed={false}
                  fontFamily="inherit"
                  fontSize="1rem"
                  offset={0}
                  padding={3}
                  direction="up"
                  radius={4}
                  zIndex={1}
                >
                  <div onClick={this.props.EnableAnnotate} data-toggle="tooltip" data-placement="right" className={`${isSmallScreen ? (isClicked ? 'relative h-[46px] cursor-pointer w-[46px]  border-[none] shadow-[0px_17px_21px_0px_#0000000D]   p-[11px] rounded-full bg-[black]/10 bg-opacity-10' : '') : 'm-[auto] p-[10px]'}`} href="#" data-tip="Annotate">
                    <svg className="w-6 h-6" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <g clip-path="url(#clip0_178_22178)">
                        <path d="M19.487 21.4072H12.7417C12.3537 21.4072 12.0391 21.7218 12.0391 22.1099C12.0391 22.4979 12.3537 22.8125 12.7417 22.8125H19.487C19.8751 22.8125 20.1897 22.4979 20.1897 22.1099C20.1897 21.7218 19.8751 21.4072 19.487 21.4072Z" fill="white" />
                        <path d="M3.12402 18.7354L0.20985 21.6096C-0.236091 22.0495 0.0757399 22.8125 0.703244 22.8125H5.24698C5.43332 22.8125 5.61202 22.7385 5.74379 22.6067L6.36956 21.9809L3.12402 18.7354Z" fill="white" />
                        <path d="M5.00197 13.1182C4.21759 13.9442 3.90248 15.1323 4.17951 16.2405C4.29928 16.7196 4.24213 17.2222 4.03467 17.6583L7.4494 21.0731C7.8856 20.8656 8.38817 20.8085 8.86723 20.9282C9.97656 21.2056 11.1631 20.8894 11.989 20.1052L5.00197 13.1182Z" fill="white" />
                        <path d="M23.3046 4.22411L21.2804 2.03122C20.3093 0.979227 18.6793 0.90203 17.6128 1.85003L6.02295 12.1521L12.9625 19.0917L23.3131 7.77084C24.2313 6.76654 24.2289 5.22551 23.3046 4.22411Z" fill="white" />
                      </g>
                      <defs>
                        <clipPath id="clip0_178_22178">
                          <rect width="24" height="24" fill="white" />
                        </clipPath>
                      </defs>
                    </svg>
                  </div></Tooltip>
              </>
              :
              <>
                <Tooltip
                  arrowSize={7.5}
                  background="rgb(0 0 0 / 0.1)"
                  border="#fff"
                  color="#fff"
                  content={"Annotate"}
                  fadeDuration={0}
                  fadeEasing="linear"
                  fixed={false}
                  fontFamily="inherit"
                  fontSize="1rem"
                  offset={0}
                  padding={3}
                  direction="up"
                  radius={4}
                  zIndex={1}
                >
                  <div onClick={this.props.EnableAnnotate} data-toggle="tooltip" data-placement="right" className={`${isSmallScreen ? (isClicked ? 'relative h-[46px] cursor-pointer w-[46px]  border-[none] shadow-[0px_17px_21px_0px_#0000000D]   p-[11px] rounded-full bg-[black]/10 bg-opacity-10' : '') : 'm-[auto] p-[10px]'}`} href="#" data-tip="Annotate">
                    <svg className="w-6 h-6" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <g clip-path="url(#clip0_178_22234)">
                        <path d="M19.487 21.4072H12.7417C12.3537 21.4072 12.0391 21.7218 12.0391 22.1099C12.0391 22.4979 12.3537 22.8125 12.7417 22.8125H19.487C19.8751 22.8125 20.1897 22.4979 20.1897 22.1099C20.1897 21.7218 19.8751 21.4072 19.487 21.4072Z" fill="#1C74D0" />
                        <path d="M3.12402 18.7354L0.20985 21.6096C-0.236091 22.0495 0.0757399 22.8125 0.703244 22.8125H5.24698C5.43332 22.8125 5.61202 22.7385 5.74379 22.6067L6.36956 21.9809L3.12402 18.7354Z" fill="#1C74D0" />
                        <path d="M5.00197 13.1182C4.21759 13.9442 3.90248 15.1323 4.17951 16.2405C4.29928 16.7196 4.24213 17.2222 4.03467 17.6583L7.4494 21.0731C7.8856 20.8656 8.38817 20.8085 8.86723 20.9282C9.97656 21.2056 11.1631 20.8894 11.989 20.1052L5.00197 13.1182Z" fill="#1C74D0" />
                        <path d="M23.3046 4.22411L21.2804 2.03122C20.3093 0.979227 18.6793 0.90203 17.6128 1.85003L6.02295 12.1521L12.9625 19.0917L23.3131 7.77084C24.2313 6.76654 24.2289 5.22551 23.3046 4.22411Z" fill="#1C74D0" />
                      </g>
                      <defs>
                        <clipPath id="clip0_178_22234">
                          <rect width="24" height="24" fill="white" />
                        </clipPath>
                      </defs>
                    </svg>
                  </div></Tooltip>

              </>

            }
            <Tooltip
              arrowSize={7.5}
              background="rgb(0 0 0 / 0.1)"
              border="#fff"
              color="#fff"
              content={"Change Projects"}
              fadeDuration={0}
              fadeEasing="linear"
              fixed={false}
              fontFamily="inherit"
              fontSize="1rem"
              offset={0}
              padding={3}
              direction="up"
              radius={4}
              zIndex={1}
            >
              <div onClick={() => this.open_close('project', true)} className={`${isSmallScreen ? (isClicked ? 'relative h-[46px] cursor-pointer w-[46px]  border-[none] shadow-[0px_17px_21px_0px_#0000000D]   p-[11px] rounded-full bg-[black]/10 bg-opacity-10' : '') : 'm-[auto] p-[10px]'}`} data-toggle="tooltip" href="#" data-tip="Switch project">
                <svg className="w-6 h-6" viewBox="0 0 29 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_147_20044)">
                    <path d="M6.04018 11.8126C6.38088 12.1538 6.9365 12.1538 7.27721 11.8126L8.40869 10.6811C8.74994 10.3404 8.74994 9.78476 8.40869 9.44406L7.27721 8.31258H22.8462C24.2938 8.31258 25.4712 9.49 25.4712 10.9376C25.4712 11.6611 26.0602 12.2501 26.7837 12.2501H27.6587C28.3822 12.2501 28.9712 11.6611 28.9712 10.9376C28.9712 7.56008 26.2237 4.81258 22.8462 4.81258H7.27721L8.40869 3.68109C8.74994 3.34039 8.74994 2.78477 8.40869 2.44406L7.27721 1.31258C6.9365 0.971328 6.38088 0.971328 6.04018 1.31258L1.09916 6.25305C0.928535 6.42422 0.928535 6.70094 1.09916 6.87211L6.04018 11.8126Z" fill="white" />
                    <path d="M23.9022 16.1875C23.5615 15.8462 23.0064 15.8462 22.6652 16.1875L21.5337 17.319C21.1918 17.6609 21.1912 18.2136 21.5337 18.556L22.6652 19.6875H7.09619C5.64861 19.6875 4.47119 18.5101 4.47119 17.0625C4.47119 16.339 3.88221 15.75 3.15869 15.75H2.28369C1.56018 15.75 0.971191 16.339 0.971191 17.0625C0.971191 20.44 3.71869 23.1875 7.09619 23.1875H22.6652L21.5337 24.319C21.1918 24.6609 21.1912 25.2136 21.5337 25.556L22.6652 26.6875C23.0065 27.0288 23.561 27.0287 23.9022 26.6875L28.8432 21.747C29.0138 21.5759 29.0138 21.2991 28.8432 21.128L23.9022 16.1875Z" fill="white" />
                  </g>
                  <defs>
                    <clipPath id="clip0_147_20044">
                      <rect width="28" height="28" fill="white" transform="translate(0.971191)" />
                    </clipPath>
                  </defs>
                </svg>
              </div></Tooltip>

            {this.props.data.hasOwnProperty('brochure') ? <><Tooltip
              arrowSize={7.5}
              background="rgb(0 0 0 / 0.1)"
              border="#fff"
              color="#fff"
              content={"Brochure"}
              fadeDuration={0}
              fadeEasing="linear"
              fixed={false}
              fontFamily="inherit"
              fontSize="1rem"
              offset={0}
              padding={3}
              direction="up"
              radius={4}
              zIndex={1}
            >
              <div onClick={() => this.open_close('document', true)} data-toggle="tooltip" data-placement="right" title="" className={`${isSmallScreen ? (isClicked ? 'relative h-[46px] cursor-pointer w-[46px]  border-[none] shadow-[0px_17px_21px_0px_#0000000D]   p-[11px] rounded-full bg-[black]/10 bg-opacity-10' : '') : 'm-[auto] p-[10px]'}`} href="#" data-tip="Brochure">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 text-white">
                  <path fill-rule="evenodd" d="M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0016.5 9h-1.875a1.875 1.875 0 01-1.875-1.875V5.25A3.75 3.75 0 009 1.5H5.625zM7.5 15a.75.75 0 01.75-.75h7.5a.75.75 0 010 1.5h-7.5A.75.75 0 017.5 15zm.75 2.25a.75.75 0 000 1.5H12a.75.75 0 000-1.5H8.25z" clip-rule="evenodd" />
                  <path d="M12.971 1.816A5.23 5.23 0 0114.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 013.434 1.279 9.768 9.768 0 00-6.963-6.963z" />
                </svg>
              </div></Tooltip>
            </> : <>
              {/* <div data-toggle="tooltip" data-placement="right" title="" className={`${isSmallScreen ? (isClicked ? 'relative h-[46px] cursor-pointer w-[46px]  border-[none] shadow-[0px_17px_21px_0px_#0000000D]   p-[11px] rounded-full bg-[black]/10 bg-opacity-10' : '') : 'm-[auto] p-[10px]'}`} href="#" data-tip="Brochure">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 text-white">
                <path fill-rule="evenodd" d="M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0016.5 9h-1.875a1.875 1.875 0 01-1.875-1.875V5.25A3.75 3.75 0 009 1.5H5.625zM7.5 15a.75.75 0 01.75-.75h7.5a.75.75 0 010 1.5h-7.5A.75.75 0 017.5 15zm.75 2.25a.75.75 0 000 1.5H12a.75.75 0 000-1.5H8.25z" clip-rule="evenodd" />
                <path d="M12.971 1.816A5.23 5.23 0 0114.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 013.434 1.279 9.768 9.768 0 00-6.963-6.963z" />
              </svg>


            </div>
             */}
            </>}

            {this.props.data.plans ?
              <>
                <Tooltip
                  arrowSize={7.5}
                  background="rgb(0 0 0 / 0.1)"
                  border="#fff"
                  color="#fff"
                  content={"Floor plan"}
                  fadeDuration={0}
                  fadeEasing="linear"
                  fixed={false}
                  fontFamily="inherit"
                  fontSize="1rem"
                  offset={0}
                  padding={3}
                  direction="up"
                  radius={4}
                  zIndex={1}
                >
                  <div onClick={() => this.open_close('floorplan', true)} data-toggle="tooltip" data-placement="right" title="" className=" p-[10px]  " href="#" data-tip="Floor Plan">
                    <svg className="w-6 h-6" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M15.5294 7.7647V11.2941C15.5294 11.684 15.8454 12 16.2353 12C16.6252 12 16.9412 11.684 16.9412 11.2941V7.7647H24V22.5882C24 23.3679 23.3679 24 22.5882 24H16.9412V15.5294C16.9412 15.1395 16.6252 14.8235 16.2353 14.8235C15.8454 14.8235 15.5294 15.1395 15.5294 15.5294V17.647H12.7059C12.316 17.647 12 17.9631 12 18.3529C12 18.7428 12.316 19.0588 12.7059 19.0588H15.5294V24H8.47059V18.3529C8.47059 17.9631 8.15456 17.647 7.7647 17.647C7.37484 17.647 7.05881 17.9631 7.05881 18.3529V24H1.41178C0.632062 24 0 23.3679 0 22.5882V13.4118H7.05881V14.1177C7.05881 14.5075 7.37484 14.8236 7.7647 14.8236C8.15456 14.8236 8.47059 14.5075 8.47059 14.1177V11.2942C8.47059 10.9043 8.15456 10.5883 7.7647 10.5883C7.37484 10.5883 7.05881 10.9043 7.05881 11.2942V12H0V1.41178C0 0.632062 0.632062 0 1.41178 0H7.05886V7.05881C7.05886 7.44867 7.37489 7.7647 7.76475 7.7647C8.15461 7.7647 8.47064 7.44867 8.47064 7.05881V0H22.5883C23.3679 0 24 0.632062 24 1.41178V6.35297H12.7059C12.316 6.35297 12 6.669 12 7.05886C12 7.44872 12.316 7.76475 12.7059 7.76475L15.5294 7.7647Z" fill="white" />
                    </svg>
                  </div>
                </Tooltip>
              </>
              : <>
                {/* <div data-toggle="tooltip" data-placement="right" title="" className={"opacity-80 "+`${isSmallScreen ? (isClicked ? 'relative h-[46px] cursor-pointer w-[46px]  border-[none] shadow-[0px_17px_21px_0px_#0000000D]   p-[11px] rounded-full bg-[black]/10 bg-opacity-10' : '') : 'm-[auto] p-[10px]'}`} href="#" data-tip="Floor Plan">
                <svg className="w-6 h-6" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M15.5294 7.7647V11.2941C15.5294 11.684 15.8454 12 16.2353 12C16.6252 12 16.9412 11.684 16.9412 11.2941V7.7647H24V22.5882C24 23.3679 23.3679 24 22.5882 24H16.9412V15.5294C16.9412 15.1395 16.6252 14.8235 16.2353 14.8235C15.8454 14.8235 15.5294 15.1395 15.5294 15.5294V17.647H12.7059C12.316 17.647 12 17.9631 12 18.3529C12 18.7428 12.316 19.0588 12.7059 19.0588H15.5294V24H8.47059V18.3529C8.47059 17.9631 8.15456 17.647 7.7647 17.647C7.37484 17.647 7.05881 17.9631 7.05881 18.3529V24H1.41178C0.632062 24 0 23.3679 0 22.5882V13.4118H7.05881V14.1177C7.05881 14.5075 7.37484 14.8236 7.7647 14.8236C8.15456 14.8236 8.47059 14.5075 8.47059 14.1177V11.2942C8.47059 10.9043 8.15456 10.5883 7.7647 10.5883C7.37484 10.5883 7.05881 10.9043 7.05881 11.2942V12H0V1.41178C0 0.632062 0.632062 0 1.41178 0H7.05886V7.05881C7.05886 7.44867 7.37489 7.7647 7.76475 7.7647C8.15461 7.7647 8.47064 7.44867 8.47064 7.05881V0H22.5883C23.3679 0 24 0.632062 24 1.41178V6.35297H12.7059C12.316 6.35297 12 6.669 12 7.05886C12 7.44872 12.316 7.76475 12.7059 7.76475L15.5294 7.7647Z" fill="white" />
                </svg>
              </div>
               */}
              </>}
            {/* {
            this.props.bookurl ? <>
              <a onClick={(e) => {
                this.props.senddata({
                  actiontype: "bookurl",
                  url: this.props.bookurl
                })
              }} target="_blank" name="book" className={`${isSmallScreen ? (isClicked ? 'relative h-[46px] cursor-pointer w-[46px]  border-[none] shadow-[0px_17px_21px_0px_#0000000D]   p-[11px] rounded-full bg-[black]/10 bg-opacity-10' : '') : ''}` } href={this.props.bookurl} data-tip="Book">
                <svg className="w-6 h-6"  viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="shopping-cart" className="svgicons-reseller"><rect className="w-6 h-6"  opacity={0} /><path d="M21.08 7a2 2 0 0 0-1.7-1H6.58L6 3.74A1 1 0 0 0 5 3H3a1 1 0 0 0 0 2h1.24L7 15.26A1 1 0 0 0 8 16h9a1 1 0 0 0 .89-.55l3.28-6.56A2 2 0 0 0 21.08 7zm-4.7 7H8.76L7.13 8h12.25z" /><circle cx="7.5" cy="19.5" r="1.5" /><circle cx="17.5" cy="19.5" r="1.5" /></g></g></svg>
              </a>

            </> : <>
              <div style={{ "opacity": "0.5" }} name="book" className={`${isSmallScreen ? (isClicked ? 'relative h-[46px] cursor-pointer w-[46px]  border-[none] shadow-[0px_17px_21px_0px_#0000000D]   p-[11px] rounded-full bg-[black]/10 bg-opacity-10' : '') : ''}` } data-tip="Book">
                <svg className="w-6 h-6"  viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="shopping-cart" className="svgicons-reseller"><rect className="w-6 h-6"  opacity={0} /><path d="M21.08 7a2 2 0 0 0-1.7-1H6.58L6 3.74A1 1 0 0 0 5 3H3a1 1 0 0 0 0 2h1.24L7 15.26A1 1 0 0 0 8 16h9a1 1 0 0 0 .89-.55l3.28-6.56A2 2 0 0 0 21.08 7zm-4.7 7H8.76L7.13 8h12.25z" /><circle cx="7.5" cy="19.5" r="1.5" /><circle cx="17.5" cy="19.5" r="1.5" /></g></g></svg>
              </div>

            </>} */}
          </div>

          <div id="bottom" className="container hidden sm:block " style={{ position: "absolute" }} ref={this.bottom} >
            <div style={{ flexWrap: 'nowrap' }} className="row">
              <div className="content_padding dropup" style={{ marginLeft: "9px", display: 'none' }} >
                <button style={{ marginLeft: "9px", display: 'none' }} ref={elem => this.menu = elem} id="menu_bar" type="button" className="menu_option dropdown-toggle menu_option_click">
                  <span id="menu_bar_up" style={{ display: this.state.menu_bar === false ? 'block' : 'none' }}>
                    <svg id="menu_bar_up_icon" style={{ transform: 'translateY(-1px)' }} className="w-6 h-6" viewBox="0 0 24 24">
                      <defs>
                        <path id="prefix__a" d="M6.373 7.22c.371-.298.901-.294 1.267.012l6 5c.424.354.481.984.128 1.408-.198.238-.482.36-.769.36-.225 0-.452-.076-.639-.232L6.99 9.293l-5.363 4.314c-.43.347-1.059.279-1.406-.152-.347-.43-.278-1.06.152-1.406zm0-7c.371-.298.901-.295 1.267.012l6 5c.425.353.482.983.128 1.408-.198.237-.482.36-.768.36-.226 0-.453-.077-.64-.232L6.989 2.292 1.627 6.607c-.43.346-1.059.278-1.406-.152-.346-.43-.278-1.06.152-1.407z" />
                      </defs>
                      <g fill="none" fillRule="evenodd" transform="translate(5 5)">
                        <use fill="#FFF" xlinkHref="#prefix__a" />
                      </g>
                    </svg>
                  </span>
                  <span style={{ display: this.state.menu_bar === true ? 'block' : 'none', transform: 'translateY(-1px)' }} id="menu_bar_down">
                    <svg id="menu_bar_down_icon" className="w-6 h-6" viewBox="0 0 24 24">
                      <defs>
                        <path id="prefix__down" d="M.232 7.36c.353-.424.983-.482 1.408-.128l5.371 4.476 5.362-4.315c.43-.345 1.061-.277 1.406.152.346.43.278 1.06-.152 1.407l-6 4.828c-.183.147-.405.22-.627.22-.228 0-.455-.077-.64-.232l-6-5c-.425-.353-.482-.983-.128-1.408zm0-7c.354-.425.983-.48 1.408-.128l5.37 4.475L12.374.393c.43-.346 1.06-.278 1.406.152.347.43.278 1.06-.152 1.406l-6 4.828C7.444 6.926 7.222 7 7 7c-.227 0-.455-.077-.64-.232l-6-5C-.064 1.414-.121.784.232.36z" />
                      </defs>
                      <g fill="none" fillRule="evenodd" transform="translate(5 5)">
                        <use className="svgicons-reseller" xlinkHref="#prefix__down" />
                      </g>
                    </svg>
                  </span>
                </button>
                <div className="menudrop dropdown-menu show" x-placement="top-start" style={{ position: 'absolute', top: '-23px', left: '3px', willChange: 'top, left' }}>

                  <div onClick={() => this.open_close('share', true)} name="share" className={`${isSmallScreen ? (isClicked ? 'relative h-[46px] mb-[10px] cursor-pointer w-[46px]  border-[none] shadow-[0px_17px_21px_0px_#0000000D]   p-[11px] rounded-full bg-[black]/10 bg-opacity-10' : '') : 'm-[auto] p-[10px]'}`} href="#" data-tip="Share">
                    <svg className="w-6 h-6" viewBox="0 0 24 24">
                      <defs>
                        <path id="prefix__b" d="M16 16c-.552 0-1-.448-1-1s.448-1 1-1 1 .448 1 1-.448 1-1 1M3 10c-.552 0-1-.448-1-1s.448-1 1-1 1 .448 1 1-.448 1-1 1m13-8c.552 0 1 .448 1 1s-.448 1-1 1-1-.448-1-1 .448-1 1-1m0 10c-.817 0-1.557.33-2.099.861L5.966 9.335C5.979 9.224 6 9.114 6 9c0-.114-.021-.224-.034-.335l7.935-3.526C14.443 5.67 15.183 6 16 6c1.654 0 3-1.346 3-3s-1.346-3-3-3-3 1.346-3 3c0 .***************.335L5.099 6.861C4.557 6.33 3.817 6 3 6 1.346 6 0 7.346 0 9s1.346 3 3 3c.817 0 1.557-.33 2.099-.861l7.935 3.526c-.013.111-.034.221-.034.335 0 1.654 1.346 3 3 3s3-1.346 3-3-1.346-3-3-3" />
                      </defs>
                      <g fill="none" fillRule="evenodd" transform="translate(2 3)">
                        <use className="icon_svg  svgicons-reseller"  xlinkHref="#prefix__b" />
                      </g>
                    </svg>
                  </div>


                  <div onClick={() => this.open_close('project', true)} data-toggle="tooltip" data-placement="right" title="" className={`${isSmallScreen ? (isClicked ? 'relative h-[46px] cursor-pointer w-[46px]  border-[none] shadow-[0px_17px_21px_0px_#0000000D]   p-[11px] rounded-full bg-[black]/10 bg-opacity-10' : '') : ''}`} href="#" data-tip="Switch project">
                    <svg className="w-6 h-6" viewBox="0 0 24 24">
                      <defs>
                        <path id="prefix__c" d="M4.4 9.2c.443-.33 1.069-.242 1.4.2.331.442.242 1.07-.2 1.4L4 12h13c.552 0 1 .447 1 1 0 .553-.448 1-1 1H3.918l1.558 1.21c.436.34.515.968.176 1.403-.197.254-.492.387-.79.387-.215 0-.431-.068-.613-.21l-3.862-3c-.247-.19-.389-.486-.387-.799.002-.31.15-.604.4-.79zM12.348.387c.339-.437.968-.516 1.403-.177l3.862 3c.247.191.389.486.387.8-.002.31-.15.603-.4.79l-4 3c-.18.135-.391.2-.599.2-.304 0-.605-.138-.801-.4-.331-.442-.242-1.069.2-1.4L14 5H1c-.552 0-1-.447-1-1 0-.553.448-1 1-1h13.082l-1.558-1.21c-.436-.339-.515-.968-.176-1.403z" />
                      </defs>
                      <g fill="none" fillRule="evenodd" transform="translate(3 3)">
                        <use className="icon_svg svgicons-reseller" xlinkHref="#prefix__c" />
                      </g>
                    </svg>
                  </div>

                  {!this.props.lock ?
                    <>

                      <div onClick={this.lockUser} data-toggle="tooltip" data-placement="right" className={`${isSmallScreen ? (isClicked ? 'relative h-[46px] cursor-pointer w-[46px]  border-[none] shadow-[0px_17px_21px_0px_#0000000D]   p-[11px] rounded-full bg-[black]/10 bg-opacity-10' : '') : 'm-[auto] p-[10px]'}`} href="#" data-tip="Lock">
                        <svg className="w-6 h-6" viewBox="0 0 24 24">
                          <g data-name="Layer 2">
                            <g className="svgicons-reseller" data-name="lock">
                              <rect width="24" height="24" opacity="0" />
                              <path d="M17 8h-1V6.11a4 4 0 1 0-8 0V8H7a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3v-8a3 3 0 0 0-3-3zm-7-1.89A2.06 2.06 0 0 1 12 4a2.06 2.06 0 0 1 2 2.11V8h-4zM18 19a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1v-8a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1z" />
                              <path d="M12 12a3 3 0 1 0 3 3 3 3 0 0 0-3-3zm0 4a1 1 0 1 1 1-1 1 1 0 0 1-1 1z" />
                            </g>
                          </g>
                        </svg>
                      </div>
                    </>
                    :
                    <>

                      <div onClick={this.lockUser} data-toggle="tooltip" data-placement="right" className={`${isSmallScreen ? (isClicked ? 'relative h-[46px] cursor-pointer w-[46px]  border-[none] shadow-[0px_17px_21px_0px_#0000000D]   p-[11px] rounded-full bg-[black]/10 bg-opacity-10' : '') : 'm-[auto] p-[10px]'}`} href="#" data-tip="Unlock">
                        <svg className="w-6 h-6" viewBox="0 0 24 24">
                          <g data-name="Layer 2">
                            <g fill="#1C74D0" data-name="unlock">
                              <rect width="24" height="24" opacity="0" />
                              <path d="M17 8h-7V6a2 2 0 0 1 4 0 1 1 0 0 0 2 0 4 4 0 0 0-8 0v2H7a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3v-8a3 3 0 0 0-3-3zm1 11a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1v-8a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1z" />
                              <path d="M12 12a3 3 0 1 0 3 3 3 3 0 0 0-3-3zm0 4a1 1 0 1 1 1-1 1 1 0 0 1-1 1z" />
                            </g></g>
                        </svg>
                      </div>
                    </>
                  }{!this.props.annotate ?
                    <>

                      <div onClick={this.props.EnableAnnotate} data-toggle="tooltip" data-placement="right" className={`${isSmallScreen ? (isClicked ? 'relative h-[46px] cursor-pointer w-[46px]  border-[none] shadow-[0px_17px_21px_0px_#0000000D]   p-[11px] rounded-full bg-[black]/10 bg-opacity-10' : '') : 'm-[auto] p-[10px]'}`} href="#" data-tip="Annotate">
                        <svg className="w-6 h-6" viewBox="0 0 44 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <g clip-path="url(#clip0_147_20137)">
                            <g clip-path="url(#clip1_147_20137)">
                              <path d="M22.7352 24.9756H14.8656C14.4129 24.9756 14.0459 25.3426 14.0459 25.7953C14.0459 26.248 14.4129 26.6151 14.8656 26.6151H22.7352C23.1879 26.6151 23.5549 26.248 23.5549 25.7953C23.5549 25.3426 23.1879 24.9756 22.7352 24.9756Z" fill="white" />
                              <path d="M3.64469 21.8584L0.244825 25.2117C-0.27544 25.7249 0.0883633 26.6151 0.820451 26.6151H6.12147C6.33887 26.6151 6.54736 26.5287 6.70109 26.375L7.43115 25.6449L3.64469 21.8584Z" fill="white" />
                              <path d="M5.83604 15.3057C4.92093 16.2694 4.5533 17.6554 4.8765 18.9483C5.01624 19.5073 4.94956 20.0937 4.70752 20.6025L8.69137 24.5864C9.20027 24.3443 9.78661 24.2777 10.3455 24.4174C11.6397 24.741 13.024 24.3721 13.9875 23.4572L5.83604 15.3057Z" fill="white" />
                              <path d="M27.1888 4.92845L24.8272 2.37008C23.6943 1.14276 21.7926 1.05269 20.5483 2.1587L7.02686 14.1778L15.123 22.274L27.1987 9.06631C28.2699 7.89462 28.2672 6.09675 27.1888 4.92845Z" fill="white" />
                            </g>
                            <g clip-path="url(#clip2_147_20137)">
                              <path d="M40.3122 16.2227L43.8722 12.6626C43.9546 12.5803 44 12.4704 44 12.3531C44 12.2359 43.9546 12.126 43.8722 12.0437L43.6101 11.7815C43.4393 11.6109 43.1617 11.6109 42.9912 11.7815L40.0017 14.771L37.0088 11.7782C36.9264 11.6959 36.8166 11.6504 36.6994 11.6504C36.5822 11.6504 36.4723 11.6959 36.3899 11.7782L36.1278 12.0404C36.0454 12.1228 36 12.2326 36 12.3498C36 12.467 36.0454 12.577 36.1278 12.6593L39.6911 16.2227C39.7738 16.3052 39.8841 16.3506 40.0015 16.3503C40.1193 16.3506 40.2296 16.3052 40.3122 16.2227Z" fill="white" />
                            </g>
                          </g>
                          <defs>
                            <clipPath id="clip0_147_20137">
                              <rect width="44" height="28" fill="white" />
                            </clipPath>
                            <clipPath id="clip1_147_20137">
                              <rect width="28" height="28" fill="white" />
                            </clipPath>
                            <clipPath id="clip2_147_20137">
                              <rect width="8" height="8" fill="white" transform="matrix(-4.37114e-08 -1 -1 4.37114e-08 44 18)" />
                            </clipPath>
                          </defs>
                        </svg>
                      </div>
                    </>
                    :
                    <>

                      <div onClick={this.props.EnableAnnotate} data-toggle="tooltip" data-placement="right" className={`${isSmallScreen ? (isClicked ? 'relative h-[46px] cursor-pointer w-[46px]  border-[none] shadow-[0px_17px_21px_0px_#0000000D]   p-[11px] rounded-full bg-[black]/10 bg-opacity-10' : '') : 'm-[auto] p-[10px]'}`} href="#" data-tip="Annotate">
                        <svg className="w-6 h-6" viewBox="0 0 21 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M20.1888 3.92845L17.8272 1.37008C16.6943 0.142757 14.7926 0.0526944 13.5483 1.1587L0.0268555 13.1778L8.12305 21.274L20.1987 8.06631C21.2699 6.89462 21.2672 5.09675 20.1888 3.92845Z" fill="#1C74D0" />
                        </svg>
                      </div>
                    </>
                  }
                  {this.props.data.hasOwnProperty('brochure') ? <>
                    <div onClick={() => this.open_close('document', true)} data-toggle="tooltip" data-placement="right" title="" className={`${isSmallScreen ? (isClicked ? 'relative h-[46px] cursor-pointer w-[46px]  border-[none] shadow-[0px_17px_21px_0px_#0000000D]   p-[11px] rounded-full bg-[black]/10 bg-opacity-10' : '') : 'm-[auto] p-[10px]'}`} href="#" data-tip="Brochure">
                      <svg className="w-6 h-6" viewBox="0 0 24 24">
                        <defs>
                          <path id="prefix__d" d="M11 14c.553 0 1 .448 1 1s-.447 1-1 1H5c-.552 0-1-.448-1-1s.448-1 1-1zm-3-4c.552 0 1 .448 1 1s-.448 1-1 1H5c-.552 0-1-.448-1-1s.448-1 1-1zm5.444 8H2.555C2.25 18 2 17.776 2 17.5v-15c0-.276.25-.5.555-.5H8v3.15C8 6.722 9.217 8 10.714 8H14v9.5c0 .276-.249.5-.556.5zM10 2.978L12.742 6h-2.028C10.32 6 10 5.619 10 5.15V2.978zm5.74 3.35l-5.444-6C10.106.119 9.838 0 9.556 0h-7C1.145 0 0 1.122 0 2.5v15C0 18.878 1.146 20 2.555 20h10.89C14.852 20 16 18.878 16 17.5V7c0-.249-.093-.488-.26-.672z" />
                        </defs>
                        <g className="svgicons-reseller" fillRule="evenodd" transform="translate(4 2)">
                          <use className="icon_svg svgicons-reseller" xlinkHref="#prefix__d" />
                        </g>
                      </svg>
                    </div>
                  </> : <>
                    {/* <div data-toggle="tooltip" data-placement="right" title="" className={`${isSmallScreen ? (isClicked ? 'relative h-[46px] cursor-pointer w-[46px]  border-[none] shadow-[0px_17px_21px_0px_#0000000D]   p-[11px] rounded-full bg-[black]/10 bg-opacity-10' : '') : ''}`} href="#" data-tip="Brochure">
                    <svg className="w-6 h-6" viewBox="0 0 24 24">
                      <defs>
                          <path fill="#1C74D0"  id="prefix__d" d="M11 14c.553 0 1 .448 1 1s-.447 1-1 1H5c-.552 0-1-.448-1-1s.448-1 1-1zm-3-4c.552 0 1 .448 1 1s-.448 1-1 1H5c-.552 0-1-.448-1-1s.448-1 1-1zm5.444 8H2.555C2.25 18 2 17.776 2 17.5v-15c0-.276.25-.5.555-.5H8v3.15C8 6.722 9.217 8 10.714 8H14v9.5c0 .276-.249.5-.556.5zM10 2.978L12.742 6h-2.028C10.32 6 10 5.619 10 5.15V2.978zm5.74 3.35l-5.444-6C10.106.119 9.838 0 9.556 0h-7C1.145 0 0 1.122 0 2.5v15C0 18.878 1.146 20 2.555 20h10.89C14.852 20 16 18.878 16 17.5V7c0-.249-.093-.488-.26-.672z" />
                      </defs>
                      <g className="svgicons-reseller" style={{ "opacity": "0.5" }} fillRule="evenodd" transform="translate(4 2)">
                          <use className="icon_svg" fill="#1C74D0" xlinkHref="#prefix__d" />
                      </g>
                    </svg>
                  </div>
                   */}
                  </>}

                  {this.props.data.plans ?
                    <>
                      <div onClick={() => this.open_close('floorplan', true)} data-toggle="tooltip" data-placement="right" title="" className={`${isSmallScreen ? (isClicked ? 'relative h-[46px] cursor-pointer w-[46px]  border-[none] shadow-[0px_17px_21px_0px_#0000000D]   p-[11px] rounded-full bg-[black]/10 bg-opacity-10' : '') : 'm-[auto] p-[10px]'}`} href="#" data-tip="Floor Plan">
                        <svg className="w-6 h-6" viewBox="0 0 24 24">
                          <defs>
                            <path id="prefix__e" d="M15.69 12.963L10 15.607V8.89l6-2.787v6.435c0 .174-.118.336-.31.424zm-13.387 0c-.189-.087-.305-.25-.303-.431V6.104L8 8.89v6.718l-5.697-2.645zm6.404-10.9C8.798 2.022 8.9 2 9 2c.1 0 .201.02.292.064l5.33 2.474L9 7.15 3.378 4.538l5.33-2.474zm8.95 2.196c-.004-.008-.003-.017-.007-.025-.003-.008-.01-.013-.016-.021-.046-.076-.102-.143-.155-.213-.03-.034-.056-.072-.09-.101-.235-.276-.512-.52-.856-.678l-6.4-2.973-.002-.001c-.719-.332-1.544-.331-2.265 0L1.47 3.22c-.344.159-.622.4-.857.677-.037.03-.064.073-.097.11-.052.067-.105.132-.149.204-.005.009-.013.015-.017.023-.004.009-.003.018-.007.027C.132 4.623 0 5.03 0 5.458v7.067c-.008.95.564 1.834 1.458 2.252l6.4 2.972c.361.169.75.253 1.138.253.388 0 .776-.084 1.137-.252l6.397-2.972c.892-.412 1.47-1.29 1.47-2.238V5.457c0-.428-.132-.835-.344-1.198z" />
                          </defs>
                          <g fill="none" fillRule="evenodd" transform="translate(3 3)">
                            <use className="icon_svg svgicons-reseller"  xlinkHref="#prefix__e" />
                          </g>
                        </svg>
                      </div>

                    </>
                    :
                    <>
                      {/* <div data-toggle="tooltip" data-placement="right" title="" className={"opacity-80 "+`${isSmallScreen ? (isClicked ? 'relative h-[46px] cursor-pointer w-[46px]  border-[none] shadow-[0px_17px_21px_0px_#0000000D]   p-[11px] rounded-full bg-[black]/10 bg-opacity-10' : '') : 'm-[auto] p-[10px]'}`} href="#" data-tip="Floor Plan">
                      <svg className="w-6 h-6" viewBox="0 0 24 24">
                        <defs>
                          <path fill="#1C74D0"  id="prefix__e" d="M15.69 12.963L10 15.607V8.89l6-2.787v6.435c0 .174-.118.336-.31.424zm-13.387 0c-.189-.087-.305-.25-.303-.431V6.104L8 8.89v6.718l-5.697-2.645zm6.404-10.9C8.798 2.022 8.9 2 9 2c.1 0 .201.02.292.064l5.33 2.474L9 7.15 3.378 4.538l5.33-2.474zm8.95 2.196c-.004-.008-.003-.017-.007-.025-.003-.008-.01-.013-.016-.021-.046-.076-.102-.143-.155-.213-.03-.034-.056-.072-.09-.101-.235-.276-.512-.52-.856-.678l-6.4-2.973-.002-.001c-.719-.332-1.544-.331-2.265 0L1.47 3.22c-.344.159-.622.4-.857.677-.037.03-.064.073-.097.11-.052.067-.105.132-.149.204-.005.009-.013.015-.017.023-.004.009-.003.018-.007.027C.132 4.623 0 5.03 0 5.458v7.067c-.008.95.564 1.834 1.458 2.252l6.4 2.972c.361.169.75.253 1.138.253.388 0 .776-.084 1.137-.252l6.397-2.972c.892-.412 1.47-1.29 1.47-2.238V5.457c0-.428-.132-.835-.344-1.198z" />
                        </defs>
                        <g className="svgicons-reseller" style={{ "opacity": "0.5" }} fillRule="evenodd" transform="translate(3 3)">
                          <use className="icon_svg" fill="var(--main-color)7d" xlinkHref="#prefix__e" />
                        </g>
                      </svg>
                    </div>
                     */}
                    </>
                  }
                  {
                    this.props.bookurl ? <>
                      <a onClick={(e) => {
                        this.props.senddata({
                          actiontype: "bookurl",
                          url: this.props.bookurl
                        })
                      }} target="_blank" name="book" className={`${isSmallScreen ? (isClicked ? 'relative h-[46px] cursor-pointer w-[46px]  border-[none] shadow-[0px_17px_21px_0px_#0000000D]   p-[11px] rounded-full bg-[black]/10 bg-opacity-10' : '') : 'm-[auto] p-[10px]'}`} href={this.props.bookurl} data-tip="Book">
                        <svg className="w-6 h-6" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="shopping-cart" className="svgicons-reseller"><rect className="w-6 h-6" opacity={0} /><path d="M21.08 7a2 2 0 0 0-1.7-1H6.58L6 3.74A1 1 0 0 0 5 3H3a1 1 0 0 0 0 2h1.24L7 15.26A1 1 0 0 0 8 16h9a1 1 0 0 0 .89-.55l3.28-6.56A2 2 0 0 0 21.08 7zm-4.7 7H8.76L7.13 8h12.25z" /><circle cx="7.5" cy="19.5" r="1.5" /><circle cx="17.5" cy="19.5" r="1.5" /></g></g></svg>
                      </a>

                    </> : <>
                      <div style={{ "opacity": "0.5" }} name="book" className={`${isSmallScreen ? (isClicked ? 'relative h-[46px] cursor-pointer w-[46px]  border-[none] shadow-[0px_17px_21px_0px_#0000000D]   p-[11px] rounded-full bg-[black]/10 bg-opacity-10' : '') : 'm-[auto] p-[10px]'}`} data-tip="Book">
                        <svg className="w-6 h-6" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="shopping-cart" className="svgicons-reseller"><rect className="w-6 h-6" opacity={0} /><path fill="#1C74D0" d="M21.08 7a2 2 0 0 0-1.7-1H6.58L6 3.74A1 1 0 0 0 5 3H3a1 1 0 0 0 0 2h1.24L7 15.26A1 1 0 0 0 8 16h9a1 1 0 0 0 .89-.55l3.28-6.56A2 2 0 0 0 21.08 7zm-4.7 7H8.76L7.13 8h12.25z" /><circle cx="7.5" cy="19.5" r="1.5" /><circle cx="17.5" cy="19.5" r="1.5" /></g></g></svg>
                      </div>

                    </>}
                </div>
                <span style={{ marginLeft: "14px" }} className="content_separate" />
              </div>

              {this.props.data ?
                <BottomSlider ref={this.props.slider_mover} data={this.props.data} changeImage={this.props.changeImage} />
                : ""}

            </div>

          </div>
          {/* commented because it is opening two times */}
          {/* <Share  open_close={this.open_close} share={this.state.share} pid={this.props.roompid} roomId={this.props.roomId} user_id={Firebase.auth().currentUser.uid}  ></Share> */}
          {this.props.data ? <DocumentModal senddata={this.props.senddata} room={this.props.roomId} data={this.props.data} open_close={this.open_close} document={this.state.document}></DocumentModal> : ""}
          {this.props.user_id ? <Switchproject switch={this.props.switch} changeProject={this.props.changeProject} data={this.props.data} open_close={this.open_close} project={this.state.project} pid={this.state.pid} user_id={this.props.user_id}></Switchproject> : ""}
          {this.state.floorplan ?
            <Floorplan annotate={this.props.annotate} senddata={this.props.senddata} room={this.props.roomId} changeImage={this.props.changeImage} data={this.props.data} open_close={this.open_close} floorplan={this.state.floorplan}></Floorplan>
            : <></>}<CloseModal recordertime={this.props.recordertime} recorder={this.props.recorder} SetDuration={this.SetDuration} duration={this.state.duration} setlastupdate={() => { this.setState({ lastupdate: false }) }} destruct={this.props.destruct} project={this.props.pid} room={this.props.roomId} admin={this.props.admin} job={this.job} lastupdate={this.state.lastupdate} close={this.state.close}  open_close={this.open_close} ></CloseModal>

          {/* <Map pid={this.state.pid} user_id={this.props.user_id} open_close={this.open_close} map={this.state.map}></Map> */}

          {this.state.settings ? <Settings videoinput={this.props.videoinput}
            audioinput={this.props.audioinput} changedevice={this.props.changedevice} open_close={this.open_close} /> : <></>}




          {/* {this.state.map?<MapModal
        open_close={this.open_close}
        data={this.state.mapdata}
      />:<></>} */}

        </div>

      </>


    )
  }
}
const mapStateToProps = state => {
  return {
    LocalStream: state.Call.LocalStream,
    Video: state.Call.Video,
    Audio: state.Call.Audio,
    usercount: state.Call.userscount,
    ScreenShare: state.Call.ScreenShare,
    Recorder: state.Call.Recorder,
    ShowControls: state.Call.ShowControls

  }
}

const mapDispatchToProps = {
  ...HostActions
}

export default connect(mapStateToProps, null)(SceneControls)
