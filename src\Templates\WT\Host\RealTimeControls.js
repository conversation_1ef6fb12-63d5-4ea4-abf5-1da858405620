import React from "react"
import * as ExceptionActions from "../../../Actions/Exception";
import { connect } from "react-redux";
import * as HostActions from "../../../Actions/HostAction";
import { Firebase } from '../../../config/Firebase';
import EndSession from "./EndSessionModal"
import Share from "../../../Tools/ToolComponents/ShareModal";
import DocumentModal from '../../../Tools/Host/DocumentModal';
import Switchproject from '../../../Tools/Host/Switchproject';
import {Tooltip} from "flowbite-react";
import Timer from "../../../Tools/ToolComponents/Timer";
import EndSessionApi from "../../../Tools/helpers/BackendApis";



class RealTimeControls extends React.Component {

  constructor(props) {
    super(props);
    this.state = {
      share: true,
      upload: 0,
      recorder: false,
      recordertime: false,
      close: false,
      fullscreen: false,
      fullscreenModal: true,
      project_data: false,
      showAll: false,
    }
    this.VideoControl = this.VideoControl.bind(this);
    this.AudioControl = this.AudioControl.bind(this);
    this.ShareScreen = this.ShareScreen.bind(this);
    this.OpenCloseModal = this.OpenCloseModal.bind(this);
    this.CloseSession = this.CloseSession.bind(this);
    this.switchToFullscreen = this.switchToFullscreen.bind(this)


  }



  componentDidMount() {
  }
  toggleDivs = () => {
    this.setState(prevState => ({
      showAll: !prevState.showAll,
    }));
  };
  // open modal for mobile devices

  state = {
    isPopUpOpen: false
  };

  handleButtonClick = () => {
    this.setState(prevState => ({
      isPopUpOpen: !prevState.isPopUpOpen
    }));
  };


  open_close = (name, flag) => {
    // document.getElementById('tools_div').classList.remove('show');

    this.setState({
      [name]: flag
    })

    if (name === 'map') {
      if (flag) {
        this.props.senddata({ actiontype: "map", data: this.props.data["latlng"] });
        this.setState({
          mapdata: this.props.data["latlng"],
          map: true
        })

      }
      else {
        this.props.senddata({ actiontype: "map", data: "false" });
        this.setState({
          mapdata: false,
          map: true
        })
      }
    }

  }

  CloseSession() {
    EndSessionApi(this.props.roomId)
  }
  VideoControl() {
    if (this.props.CameraAccess) {
      this.props.ToggleUserVideo({
        roomId: this.props.roomId,
        Peers: this.props.Peers,
        Audio: this.props.Audio,
        Video: this.props.Video,
        LocalStream: this.props.LocalStream
      })
    }
    else {
      this.props.SetModalException("Camera is not Attached")
    }
  }





  AudioControl() {
    if (this.props.MicrophoneAccess) {
      this.props.ToggleUserAudio({
        roomId: this.props.roomId,
        Peers: this.props.Peers,
        Audio: this.props.Audio,
        Video: this.props.Video,
        LocalStream: this.props.LocalStream
      })
    } else {
      this.props.SetModalException("Mic is not Attached")
    }
  }
  ShareScreen() {
    this.props.ToggleScreenVideo({
      roomId: this.props.roomId,
      Peers: this.props.Peers,
      Audio: this.props.Audio,
      Video: this.props.Video,
      Screen: this.props.ScreenShare,
      LocalStream: this.props.LocalStream
    })
  };

  OpenCloseModal() {
    if (this.props.Recorder) {
      this.props.SetModalException("Recording is still on please stop the record and close this session")
    }
    else {
      this.setState({ close: true })
    }
  }

  switchToFullscreen() {
    this.setState({ fullscreenModal: false })
    if (this.state.fullscreen == false || window.innerHeight != window.screen.height) {
      this.setState({ fullscreen: true })
      let element = document.getElementById("MainContentDiv");
      if (element.requestFullscreen) {
        element.requestFullscreen();
      } else if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen();
      } else if (element.webkitRequestFullscreen) {
        element.webkitRequestFullscreen();
      } else if (element.msRequestFullscreen) {
        element.msRequestFullscreen();
      }
    }
    else {
      this.setState({ fullscreen: false })
      // Exit fullscreen mode
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen();
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
      }

    }
  }



  render() {
    const { showAll } = this.state;
    const { isPopUpOpen } = this.state;
    return (
      <>
{/* show controls */}
<div className={`flex fixed  ${this.props.SessionDetails.type !== "pixel_streaming" ? "left-[50%] transform -translate-x-[50%] bottom-2" : "right-2 top-2"} `} >
          {this.props.ShowControls ? <Tooltip
            // arrow={3.5}
            background="rgb(0 0 0 / 0.1)"
            border="#fff"
            color="#fff"
            content={"Hide Controls"}
            fadeDuration={0}
            fadeEasing="linear"
            fixed={false}
            fontFamily="inherit"
            fontSize="1rem"
            offset={0}
            padding={3}
            direction="down"
            radius={4}
            zIndex={1}
          >
            <button
              type="button"
              className="text-white  mr-2 p-2 backdrop-blur-[6px] shadow-[0px_17px_21px_0px_#0000000D]  rounded-full border-[none] bg-[black]/10 bg-opacity-10"
              onClick={() => { this.props.INVERT_CONTROLS(this.props.ShowControls); }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6 text-white">
                <path d="M12 15a3 3 0 100-6 3 3 0 000 6z" />
                <path fillRule="evenodd" d="M1.323 11.447C2.811 6.976 7.028 3.75 12.001 3.75c4.97 0 9.185 3.223 10.675 7.69.12.362.12.752 0 1.113-1.487 4.471-5.705 7.697-10.677 7.697-4.97 0-9.186-3.223-10.675-7.69a1.762 1.762 0 010-1.113zM17.25 12a5.25 5.25 0 11-10.5 0 5.25 5.25 0 0110.5 0z" clipRule="evenodd" />
              </svg>
            </button>
          </Tooltip> : <Tooltip
            // arrow={3.5}
            background="rgb(0 0 0 / 0.1)"
            border="#fff"
            color="#fff"
            content={"Show Controls"}
            fadeDuration={0}
            fadeEasing="linear"
            fixed={false}
            fontFamily="inherit"
            fontSize="1rem"
            offset={0}
            padding={2}
            direction="down"
            radius={0}
            zIndex={1}
          >
            <button
              type="button"
              className="text-white  mr-2 p-2 backdrop-blur-[6px] shadow-[0px_17px_21px_0px_#0000000D]  rounded-full border-[none] bg-[black]/10 bg-opacity-10"
              onClick={() => { this.props.INVERT_CONTROLS(this.props.ShowControls); }}
            >
              <svg className="w-6 h-6 text-white" fill="#fff" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="eye-off"><rect width="24" height="24" opacity="0" /><path d="M4.71 3.29a1 1 0 0 0-1.42 1.42l5.63 5.63a3.5 3.5 0 0 0 4.74 4.74l5.63 5.63a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM12 13.5a1.5 1.5 0 0 1-1.5-1.5v-.07l1.56 1.56z" /><path d="M12.22 17c-4.3.1-7.12-3.59-8-5a13.7 13.7 0 0 1 2.24-2.72L5 7.87a15.89 15.89 0 0 0-2.87 3.63 1 1 0 0 0 0 1c.63 1.09 4 6.5 9.89 6.5h.25a9.48 9.48 0 0 0 3.23-.67l-1.58-1.58a7.74 7.74 0 0 1-1.7.25z" /><path d="M21.87 11.5c-.64-1.11-4.17-6.68-10.14-6.5a9.48 9.48 0 0 0-3.23.67l1.58 1.58a7.74 7.74 0 0 1 1.7-.25c4.29-.11 7.11 3.59 8 5a13.7 13.7 0 0 1-2.29 2.72L19 16.13a15.89 15.89 0 0 0 2.91-3.63 1 1 0 0 0-.04-1z" /></g></g></svg>
            </button>
          </Tooltip>}

          <Tooltip
            arrow={7.5}
            background="rgb(0 0 0 / 0.1)"
            border="#fff"
            color="#fff"
            content={"Full Screen"}
            fadeDuration={0}
            fadeEasing="linear"
            fixed={false}
            fontFamily="inherit"
            fontSize="1rem"
            offset={0}
            padding={3}
            direction="down"
            radius={4}
            zIndex={1}
          >
            <button className="relative mr-2  cursor-pointer  border-[none] shadow-[0px_17px_21px_0px_#0000000D] backdrop-blur-[6px] p-2 rounded-full bg-[black]/10 bg-opacity-10" onClick={() => { this.switchToFullscreen() }} style={{ display: !this.state.fullscreen ? 'block' : 'none' }}>
              <svg class="h-6 w-6 text-white" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">  <path stroke="none" d="M0 0h24v24H0z" />  <path d="M4 8v-2a2 2 0 0 1 2 -2h2" />  <path d="M4 16v2a2 2 0 0 0 2 2h2" />  <path d="M16 4h2a2 2 0 0 1 2 2v2" />  <path d="M16 20h2a2 2 0 0 0 2 -2v-2" /></svg>
            </button>
          </Tooltip>

          <Tooltip
            arrow={7.5}
            background="rgb(0 0 0 / 0.1)"
            border="#fff"
            color="#fff"
            content={"Normal Screen"}
            fadeDuration={0}
            fadeEasing="linear"
            fixed={false}
            fontFamily="inherit"
            fontSize="1rem"
            offset={0}
            padding={3}
            direction="down"
            radius={4}
            zIndex={1}
          >
            <button className="relative backdrop-blur-[6px] mr-2 cursor-pointer  border-[none] shadow-[0px_17px_21px_0px_#0000000D] p-2 rounded-full bg-[black]/10 bg-opacity-10" onClick={() => { this.switchToFullscreen() }} style={{ display: this.state.fullscreen ? 'block' : 'none' }}>
              <svg class="h-6 w-6 text-white" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">  <path stroke="none" d="M0 0h24v24H0z" />  <path d="M15 19v-2a2 2 0 0 1 2 -2h2" />  <path d="M15 5v2a2 2 0 0 0 2 2h2" />  <path d="M5 15h2a2 2 0 0 1 2 2v2" />  <path d="M5 9h2a2 2 0 0 0 2 -2v-2" /></svg>
            </button>
          </Tooltip>


        </div>

        {/* for bigger dimensions */}
        <div className="w-full  h-fit max-h-fit relative m-auto  rounded-none bg-[#000]" style={{ display: this.props.ShowControls ? "block" : "none" }}>

        <div id="video-controls" className={`flex flex-nowrap w-fit fixed rounded shadow-[0px_17px_21px_0px_#0000000D] m-auto  ${this.props.SessionDetails.type !== "pixel_streaming" ? "sm:left-[50%] transform -translate-x-[50%]" : "sm:left-2 "} sm:top-[0.5rem] bg-[black]/10 bg-opacity-10 backdrop-blur-[6px] controllerStyle `}>
        <Timer style={{position:'relative', border:'2px solid red'}} position={"relative"} SessionDetails={this.props.SessionDetails} start={this.props.SessionDetails.start} roomId={this.props.roomId} trackInactivity={false} trackMaxtimeout={this.props.SessionDetails.type !== "pixel_streaming"} />
            {this.props.Audio ? <Tooltip
              arrow={7.5}
              background="rgb(0 0 0 / 0.1)"                                                                                 
              border="#fff"
              color="#fff"
              content={"Mute"}
              fadeDuration={0}
              fadeEasing="linear"
              fixed={false}
              fontFamily="inherit"
              fontSize="1rem"
              offset={0}
              padding={3}
              direction="down"
              radius={4}
              zIndex={1}
            ><button id="AudioCtrl" onClick={this.AudioControl} className="p-2 mr-3 mr-2 rounded bg-[transparent] relative  cursor-pointer  border-[none] transitioning">
                <svg className="w-6 h-6 text-white" fill="white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="mic"><rect width="24" height="24" opacity="0" /><path d="M12 15a4 4 0 0 0 4-4V6a4 4 0 0 0-8 0v5a4 4 0 0 0 4 4z" /><path d="M19 11a1 1 0 0 0-2 0 5 5 0 0 1-10 0 1 1 0 0 0-2 0 7 7 0 0 0 6 6.92V20H8.89a.89.89 0 0 0-.89.89v.22a.89.89 0 0 0 .89.89h6.22a.89.89 0 0 0 .89-.89v-.22a.89.89 0 0 0-.89-.89H13v-2.08A7 7 0 0 0 19 11z" /></g></g></svg>
              </button>
            </Tooltip> : <Tooltip
              arrow={7.5}
              background="rgb(0 0 0 / 0.1)"
              border="#fff"
              color="#fff"
              content={"UnMute"}
              fadeDuration={0}
              fadeEasing="linear"
              fixed={false}
              fontFamily="inherit"
              fontSize="0.3rem"
              offset={0}
              padding={3}
              direction="down"
              radius={4}
              zIndex={1}
              ><button id="AudioCtrl" onClick={this.AudioControl} className="p-2 mr-3 mr-2 rounded bg-[transparent] relative  cursor-pointer  border-[none] transitioning">
                <svg className="w-6 h-6 text-white" fill="white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="mic-off"><rect width="24" height="24" opacity="0" /><path d="M15.58 12.75A4 4 0 0 0 16 11V6a4 4 0 0 0-7.92-.75" /><path d="M19 11a1 1 0 0 0-2 0 4.86 4.86 0 0 1-.69 2.48L17.78 15A7 7 0 0 0 19 11z" /><path d="M12 15h.16L8 10.83V11a4 4 0 0 0 4 4z" /><path fill="white" d="M20.71 19.29l-16-16a1 1 0 0 0-1.42 1.42l16 16a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42z" /><path d="M15 20h-2v-2.08a7 7 0 0 0 1.65-.44l-1.6-1.6A4.57 4.57 0 0 1 12 16a5 5 0 0 1-5-5 1 1 0 0 0-2 0 7 7 0 0 0 6 6.92V20H9a1 1 0 0 0 0 2h6a1 1 0 0 0 0-2z" /></g></g></svg>
              </button></Tooltip>}
            {this.props.Video ? <Tooltip
              arrow={7.5}
              background="rgb(0 0 0 / 0.1)"
              border="#fff"
              color="#fff"
              content={"TurnOff Video"}
              fadeDuration={0}
              fadeEasing="linear"
              fixed={false}
              fontFamily="inherit"
              fontSize="1rem"
              offset={0}
              padding={3}
              direction="down"
              radius={4}
              zIndex={1}
            >
              <button id="VideoCtrl" onClick={this.VideoControl} className="p-2 mr-3 mr-2 rounded bg-[transparent] relative  cursor-pointer  border-[none] transitioning">
                <svg className="w-6 h-6 text-white" fill="white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="video"><rect width="24" height="24" opacity="0" /><path d="M21 7.15a1.7 1.7 0 0 0-1.85.3l-2.15 2V8a3 3 0 0 0-3-3H5a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h9a3 3 0 0 0 3-3v-1.45l2.16 2a1.74 1.74 0 0 0 1.16.45 1.68 1.68 0 0 0 .69-.15 1.6 1.6 0 0 0 1-1.48V8.63A1.6 1.6 0 0 0 21 7.15z" /></g></g></svg>
              </button>
            </Tooltip> : <Tooltip
              arrow={7.5}
              background="rgb(0 0 0 / 0.1)"
              border="#fff"
              color="#fff"
              content={"TurnOn Video"}
              fadeDuration={0}
              fadeEasing="linear"
              fixed={false}
              fontFamily="inherit"
              fontSize="1rem"
              offset={0}
              padding={3}
              direction="down"
              radius={4}
              zIndex={1}
            >
                <button id="VideoCtrl" onClick={this.VideoControl} className="p-2 mr-3 mr-2 rounded bg-[transparent] relative  cursor-pointer  border-[none] transitioning">
                <svg className="w-6 h-6 text-white" fill="white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="video-off"><rect width="24" height="24" opacity="0" /><path d="M14.22 17.05L4.88 7.71 3.12 6 3 5.8A3 3 0 0 0 2 8v8a3 3 0 0 0 3 3h9a2.94 2.94 0 0 0 1.66-.51z" /><path d="M21 7.15a1.7 1.7 0 0 0-1.85.3l-2.15 2V8a3 3 0 0 0-3-3H7.83l1.29 1.29 6.59 6.59 2 2 2 2a1.73 1.73 0 0 0 .6.11 1.68 1.68 0 0 0 .69-.15 1.6 1.6 0 0 0 1-1.48V8.63a1.6 1.6 0 0 0-1-1.48z" /><path fill="white" d="M17 15.59l-2-2L8.41 7l-2-2-1.7-1.71a1 1 0 0 0-1.42 1.42l.54.53L5.59 7l9.34 9.34 1.46 1.46 2.9 2.91a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42z" /></g></g></svg>
              </button>
            </Tooltip>}

            {navigator.mediaDevices.getDisplayMedia ?
              <>
                {this.props.SessionDetails.type === "pixel_streaming" ?
                <>{showAll ?this.props.ScreenShare ?
                <Tooltip
                  arrow={7.5}
                  background="rgb(0 0 0 / 0.1)"
                  border="#fff"
                  color="#fff"
                  content={"Stop Presenting"}
                  fadeDuration={0}
                  fadeEasing="linear"
                  fixed={false}
                  fontFamily="inherit"
                  fontSize="1rem"
                  offset={0}
                  padding={3}
                  direction="down"
                  radius={4}
                  zIndex={1}
                ><button onClick={this.ShareScreen} className="p-2 mr-3 mr-2 rounded bg-[transparent] relative cursor-pointer border-[none] transitioning">

                      <svg class="h-6 w-6 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">  <path d="M2 16.1A5 5 0 0 1 5.9 20M2 12.05A9 9 0 0 1 9.95 20M2 8V6a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2h-6" />  <line x1="2" y1="20" x2="2.01" y2="20" /></svg>

                  </button>
                </Tooltip> : <Tooltip
                  arrow={7.5}
                  background="rgb(0 0 0 / 0.1)"
                  border="#fff"
                  color="#fff"
                  content={"Present"}
                  fadeDuration={0}
                  fadeEasing="linear"
                  fixed={false}
                  fontFamily="inherit"
                  fontSize="1rem"
                  offset={0}
                  padding={3}
                  direction="down"
                  radius={4}
                  zIndex={1}
                  ><button onClick={this.ShareScreen} className="p-2 mr-3 mr-2 rounded bg-[transparent] relative cursor-pointer border-[none] transitioning">


                      <svg class="h-6 w-6 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">  <path d="M2 16.1A5 5 0 0 1 5.9 20M2 12.05A9 9 0 0 1 9.95 20M2 8V6a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2h-6" />  <line x1="2" y1="20" x2="2.01" y2="20" /></svg>
                  </button>
                </Tooltip>
                :null
                }</> :
                  <>{ this.props.ScreenShare ?
                    <Tooltip
                      arrow={7.5}
                      background="rgb(0 0 0 / 0.1)"
                      border="#fff"
                      color="#fff"
                      content={"Stop Presenting"}
                      fadeDuration={0}
                      fadeEasing="linear"
                      fixed={false}
                      fontFamily="inherit"
                      fontSize="1rem"
                      offset={0}
                      padding={3}
                      direction="down"
                      radius={4}
                      zIndex={1}
                    ><button onClick={this.ShareScreen} className="p-2 mr-3 mr-2 rounded bg-[transparent] relative cursor-pointer border-[none] transitioning">

                        <svg class="h-6 w-6 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">  <path d="M2 16.1A5 5 0 0 1 5.9 20M2 12.05A9 9 0 0 1 9.95 20M2 8V6a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2h-6" />  <line x1="2" y1="20" x2="2.01" y2="20" /></svg>

                      </button>
                    </Tooltip> : <Tooltip
                      arrow={7.5}
                      background="rgb(0 0 0 / 0.1)"
                      border="#fff"
                      color="#fff"
                      content={"Present"}
                      fadeDuration={0}
                      fadeEasing="linear"
                      fixed={false}
                      fontFamily="inherit"
                      fontSize="1rem"
                      offset={0}
                      padding={3}
                      direction="down"
                      radius={4}
                      zIndex={1}
                    ><button onClick={this.ShareScreen} className="p-2 mr-3 mr-2 rounded bg-[transparent] relative cursor-pointer border-[none] transitioning">


                        <svg class="h-6 w-6 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">  <path d="M2 16.1A5 5 0 0 1 5.9 20M2 12.05A9 9 0 0 1 9.95 20M2 8V6a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2h-6" />  <line x1="2" y1="20" x2="2.01" y2="20" /></svg>
                      </button>
                    </Tooltip>
                  }</>
                  } </>: <></>}


            {/* {this.props.ShowSwitchProject?<div onClick={() => this.open_close('project', true)} className="p-2 mr-3 mr-2  rounded bg-[transparent] relative cursor-pointer  border-[none]" data-toggle="tooltip" href="#" data-tip="Switch project">
              <svg className="w-6 h-6" viewBox="0 0 29 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_147_20044)">
                  <path d="M6.04018 11.8126C6.38088 12.1538 6.9365 12.1538 7.27721 11.8126L8.40869 10.6811C8.74994 10.3404 8.74994 9.78476 8.40869 9.44406L7.27721 8.31258H22.8462C24.2938 8.31258 25.4712 9.49 25.4712 10.9376C25.4712 11.6611 26.0602 12.2501 26.7837 12.2501H27.6587C28.3822 12.2501 28.9712 11.6611 28.9712 10.9376C28.9712 7.56008 26.2237 4.81258 22.8462 4.81258H7.27721L8.40869 3.68109C8.74994 3.34039 8.74994 2.78477 8.40869 2.44406L7.27721 1.31258C6.9365 0.971328 6.38088 0.971328 6.04018 1.31258L1.09916 6.25305C0.928535 6.42422 0.928535 6.70094 1.09916 6.87211L6.04018 11.8126Z" fill="white" />
                  <path d="M23.9022 16.1875C23.5615 15.8462 23.0064 15.8462 22.6652 16.1875L21.5337 17.319C21.1918 17.6609 21.1912 18.2136 21.5337 18.556L22.6652 19.6875H7.09619C5.64861 19.6875 4.47119 18.5101 4.47119 17.0625C4.47119 16.339 3.88221 15.75 3.15869 15.75H2.28369C1.56018 15.75 0.971191 16.339 0.971191 17.0625C0.971191 20.44 3.71869 23.1875 7.09619 23.1875H22.6652L21.5337 24.319C21.1918 24.6609 21.1912 25.2136 21.5337 25.556L22.6652 26.6875C23.0065 27.0288 23.561 27.0287 23.9022 26.6875L28.8432 21.747C29.0138 21.5759 29.0138 21.2991 28.8432 21.128L23.9022 16.1875Z" fill="white" />
                </g>
                <defs>
                  <clipPath id="clip0_147_20044">
                    <rect width="28" height="28" fill="white" transform="translate(0.971191)" />
                  </clipPath>
                </defs>
              </svg>
            </div>:<></>} */}




            {this.props.SessionDetails.type === "pixel_streaming" ?<> {showAll ? <div style={{ display: "flex" }} className="transitioning">
              <Tooltip
                arrow={7.5}
                background="rgb(0 0 0 / 0.1)"
                border="#fff"
                color="#fff"
                content={"Members"}
                fadeDuration={0}
                fadeEasing="linear"
                fixed={false}
                fontFamily="inherit"
                fontSize="1rem"
                offset={0}
                padding={3}
                direction="down"
                radius={4}
                zIndex={1}
              >
                <button onClick={() => { this.props.TabControl(this.props.Tab == "MEMBERS" ? false : "MEMBERS") }} className="p-2 mr-3 mr-2 relative">
                  <span className="absolute font-bold text-[13px] text-white h-4 w-4 flex items-center justify-center rounded-full right-0 top-[0.1rem] bg-[#ff4444]">
                    {this.props.userscount + 1}
                  </span>
                  <svg className="w-6 h-6" viewBox="0 0 29 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_147_20048)">
                      <path d="M21.1324 15.9842C18.6984 15.9842 16.7252 14.0111 16.7252 11.5771C16.7252 9.14307 18.6984 7.16992 21.1324 7.16992C23.5664 7.16992 25.5396 9.14307 25.5396 11.5771C25.5396 14.0111 23.5664 15.9842 21.1324 15.9842Z" fill="white" />
                      <path fill-rule="evenodd" clip-rule="evenodd" d="M0.971037 20.4065C0.971037 19.2267 1.66567 18.0898 2.96258 17.2052C4.56518 16.1126 7.14339 15.375 10.0585 15.375C12.9736 15.375 15.5518 16.1126 17.1545 17.2052C18.4514 18.0898 19.146 19.2267 19.146 20.4065V21.6436C19.146 22.1184 18.9574 22.5724 18.6231 22.908C18.2875 23.2423 17.8335 23.4309 17.3587 23.4309H2.75705C2.28356 23.4309 1.82957 23.2423 1.49397 22.908C1.15966 22.5724 0.971037 22.1184 0.971037 21.6436V20.4065Z" fill="white" />
                      <path fill-rule="evenodd" clip-rule="evenodd" d="M19.8772 23.4312C20.2453 22.9122 20.447 22.2891 20.447 21.6439V20.4068C20.447 19.0332 19.7914 17.6699 18.4645 16.5655C19.2931 16.4042 20.192 16.3145 21.1325 16.3145C23.634 16.3145 25.8453 16.9519 27.2203 17.8897C28.3663 18.6702 28.9712 19.6797 28.9712 20.7216V21.7688C28.9712 22.2097 28.7969 22.6325 28.4847 22.9447C28.1725 23.2556 27.751 23.4312 27.3101 23.4312H19.8772Z" fill="white" />
                      <path d="M10.0588 14.6314C7.27994 14.6314 5.02724 12.3787 5.02724 9.5999C5.02724 6.82106 7.27994 4.56836 10.0588 4.56836C12.8376 4.56836 15.0903 6.82106 15.0903 9.5999C15.0903 12.3787 12.8376 14.6314 10.0588 14.6314Z" fill="white" />
                    </g>
                    <defs>
                      <clipPath id="clip0_147_20048">
                        <rect width="28" height="28" fill="white" transform="matrix(-1 0 0 1 28.9712 0)" />
                      </clipPath>
                    </defs>
                  </svg>
                </button></Tooltip>
              <Tooltip
                arrow={7.5}
                background="rgb(0 0 0 / 0.1)"
                border="#fff"
                color="#fff"
                content={"Chats"}
                fadeDuration={0}
                fadeEasing="linear"
                fixed={false}
                fontFamily="inherit"
                fontSize="1rem"
                offset={0}
                padding={3}
                direction="down"
                radius={4}
                zIndex={1}
              >
                <button onClick={() => { this.props.TabControl(this.props.Tab == "CHAT" ? false : "CHAT") }} className="p-2 mr-2 rounded bg-[transparent] relative  cursor-pointer  border-[none] transitioning">
                  {this.props.UnreadCount > 0 ? <span className="absolute font-bold text-[13px] text-white h-4 w-4 flex items-center justify-center rounded-full right-1 top-[0.1rem] bg-[#ff4444]">{this.props.UnreadCount}</span> : ""}
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 text-[white]">
                    <path fill-rule="evenodd" d="M12 2.25c-2.429 0-4.817.178-7.152.521C2.87 3.061 1.5 4.795 1.5 6.741v6.018c0 1.946 1.37 3.68 3.348 3.97.877.129 1.761.234 2.652.316V21a.75.75 0 001.28.53l4.184-4.183a.39.39 0 01.266-.112c2.006-.05 3.982-.22 5.922-.506 1.978-.29 3.348-2.023 3.348-3.97V6.741c0-1.947-1.37-3.68-3.348-3.97A49.145 49.145 0 0012 2.25zM8.25 8.625a1.125 1.125 0 100 2.25 1.125 1.125 0 000-2.25zm2.625 1.125a1.125 1.125 0 112.25 0 1.125 1.125 0 01-2.25 0zm4.875-1.125a1.125 1.125 0 100 2.25 1.125 1.125 0 000-2.25z" clip-rule="evenodd" />
                  </svg> </button></Tooltip>
            </div> : null}</> : <div style={{ display: "flex" }} className="transitioning">
              <Tooltip
                arrow={7.5}
                background="rgb(0 0 0 / 0.1)"
                border="#fff"
                color="#fff"
                content={"Members"}
                fadeDuration={0}
                fadeEasing="linear"
                fixed={false}
                fontFamily="inherit"
                fontSize="1rem"
                offset={0}
                padding={3}
                direction="down"
                radius={4}
                zIndex={1}
              >
                <button onClick={() => { this.props.TabControl(this.props.Tab == "MEMBERS" ? false : "MEMBERS") }} className="p-2 mr-3 mr-2 relative">
                  <span className="absolute font-bold text-[13px] text-white h-4 w-4 flex items-center justify-center rounded-full right-0 top-[0.1rem] bg-[#ff4444]">
                    {this.props.userscount + 1}
                  </span>
                  <svg className="w-6 h-6" viewBox="0 0 29 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_147_20048)">
                      <path d="M21.1324 15.9842C18.6984 15.9842 16.7252 14.0111 16.7252 11.5771C16.7252 9.14307 18.6984 7.16992 21.1324 7.16992C23.5664 7.16992 25.5396 9.14307 25.5396 11.5771C25.5396 14.0111 23.5664 15.9842 21.1324 15.9842Z" fill="white" />
                      <path fill-rule="evenodd" clip-rule="evenodd" d="M0.971037 20.4065C0.971037 19.2267 1.66567 18.0898 2.96258 17.2052C4.56518 16.1126 7.14339 15.375 10.0585 15.375C12.9736 15.375 15.5518 16.1126 17.1545 17.2052C18.4514 18.0898 19.146 19.2267 19.146 20.4065V21.6436C19.146 22.1184 18.9574 22.5724 18.6231 22.908C18.2875 23.2423 17.8335 23.4309 17.3587 23.4309H2.75705C2.28356 23.4309 1.82957 23.2423 1.49397 22.908C1.15966 22.5724 0.971037 22.1184 0.971037 21.6436V20.4065Z" fill="white" />
                      <path fill-rule="evenodd" clip-rule="evenodd" d="M19.8772 23.4312C20.2453 22.9122 20.447 22.2891 20.447 21.6439V20.4068C20.447 19.0332 19.7914 17.6699 18.4645 16.5655C19.2931 16.4042 20.192 16.3145 21.1325 16.3145C23.634 16.3145 25.8453 16.9519 27.2203 17.8897C28.3663 18.6702 28.9712 19.6797 28.9712 20.7216V21.7688C28.9712 22.2097 28.7969 22.6325 28.4847 22.9447C28.1725 23.2556 27.751 23.4312 27.3101 23.4312H19.8772Z" fill="white" />
                      <path d="M10.0588 14.6314C7.27994 14.6314 5.02724 12.3787 5.02724 9.5999C5.02724 6.82106 7.27994 4.56836 10.0588 4.56836C12.8376 4.56836 15.0903 6.82106 15.0903 9.5999C15.0903 12.3787 12.8376 14.6314 10.0588 14.6314Z" fill="white" />
                    </g>
                    <defs>
                      <clipPath id="clip0_147_20048">
                        <rect width="28" height="28" fill="white" transform="matrix(-1 0 0 1 28.9712 0)" />
                      </clipPath>
                    </defs>
                  </svg>
                </button></Tooltip>
              <Tooltip
                arrow={7.5}
                background="rgb(0 0 0 / 0.1)"
                border="#fff"
                color="#fff"
                content={"Chats"}
                fadeDuration={0}
                fadeEasing="linear"
                fixed={false}
                fontFamily="inherit"
                fontSize="1rem"
                offset={0}
                padding={3}
                direction="down"
                radius={4}
                zIndex={1}
              >
                <button onClick={() => { this.props.TabControl(this.props.Tab == "CHAT" ? false : "CHAT") }} className="p-2 mr-2 rounded bg-[transparent] relative  cursor-pointer  border-[none] transitioning">
                  {this.props.UnreadCount > 0 ? <span className="absolute font-bold text-[13px] text-white h-4 w-4 flex items-center justify-center rounded-full right-1 top-[0.1rem] bg-[#ff4444]">{this.props.UnreadCount}</span> : ""}
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 text-[white]">
                    <path fill-rule="evenodd" d="M12 2.25c-2.429 0-4.817.178-7.152.521C2.87 3.061 1.5 4.795 1.5 6.741v6.018c0 1.946 1.37 3.68 3.348 3.97.877.129 1.761.234 2.652.316V21a.75.75 0 001.28.53l4.184-4.183a.39.39 0 01.266-.112c2.006-.05 3.982-.22 5.922-.506 1.978-.29 3.348-2.023 3.348-3.97V6.741c0-1.947-1.37-3.68-3.348-3.97A49.145 49.145 0 0012 2.25zM8.25 8.625a1.125 1.125 0 100 2.25 1.125 1.125 0 000-2.25zm2.625 1.125a1.125 1.125 0 112.25 0 1.125 1.125 0 01-2.25 0zm4.875-1.125a1.125 1.125 0 100 2.25 1.125 1.125 0 000-2.25z" clip-rule="evenodd" />
                  </svg> </button></Tooltip>
            </div>}


            {this.props.SessionDetails.type === "pixel_streaming" ?<>{showAll ?<Tooltip
              arrow={7.5}
              background="rgb(0 0 0 / 0.1)"
              border="#fff"
              color="#fff"
              content={"End call"}
              fadeDuration={0}
              fadeEasing="linear"
              fixed={false}
              fontFamily="inherit"
              fontSize="1rem"
              offset={0}
              padding={3}
              direction="down"
              radius={4}
              zIndex={1}
            >
              <button className="p-2 mr-2 rounded bg-[transparent] relative  cursor-pointer   border-[none] transitioning" onClick={() => { this.OpenCloseModal() }}>
                <svg className="w-6 h-6" viewBox="0 0 29 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M18.4832 14.0042C18.5439 14.0844 18.5436 14.1951 18.4818 14.2756L14.2604 19.7661C14.2181 19.8218 14.1524 19.854 14.083 19.854C14.0136 19.854 13.948 19.8218 13.9055 19.7661L9.68382 14.2756C9.65324 14.2357 9.63744 14.1877 9.63744 14.1394C9.63744 14.0918 9.6526 14.044 9.68272 14.0042C9.74317 13.9238 9.85017 13.8935 9.94408 13.9312L12.7391 15.0344V8.56424C12.7391 8.44053 12.839 8.34072 12.963 8.34072H15.2032C15.3265 8.34072 15.427 8.44053 15.427 8.56424V15.0347L18.222 13.9315C18.3157 13.8938 18.4227 13.9241 18.4832 14.0042ZM12.7313 0.312883C-1.74107 0.590678 -0.0121922 7.75616 0.319555 8.80831C0.674874 10.596 2.04426 11.4905 3.43393 10.9834L6.07802 10.156C7.50257 9.63604 8.40635 7.69784 8.09535 5.8258L8.03645 5.47138C12.844 4.1108 16.9905 4.46001 20.2252 5.36045L20.1998 5.51158C19.8903 7.38382 20.7927 9.32253 22.2181 9.84215L24.862 10.9443C26.057 11.3805 27.2363 10.5405 27.7698 9.19915C27.7767 9.20493 27.7809 9.20878 27.7809 9.20878C27.7809 9.20878 27.8541 9.02817 27.9279 8.71903C27.9338 8.69494 27.9383 8.66854 27.9447 8.64484C27.957 8.58826 27.9693 8.52975 27.9816 8.46596C27.989 8.42993 27.9974 8.39583 28.0043 8.35947L28.0017 8.35722C28.3881 6.07623 27.9886 0.0216653 12.7313 0.312883Z" fill="#FF4444" />
                </svg>
              </button>
            </Tooltip> : null}</> : <Tooltip
              arrow={7.5}
              background="rgb(0 0 0 / 0.1)"
              border="#fff"
              color="#fff"
              content={"End call"}
              fadeDuration={0}
              fadeEasing="linear"
              fixed={false}
              fontFamily="inherit"
              fontSize="1rem"
              offset={0}
              padding={3}
              direction="down"
              radius={4}
              zIndex={1}
            >
              <button className="p-2 mr-2 rounded bg-[transparent] relative  cursor-pointer   border-[none] transitioning" onClick={() => { this.OpenCloseModal() }}>
                <svg className="w-6 h-6" viewBox="0 0 29 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M18.4832 14.0042C18.5439 14.0844 18.5436 14.1951 18.4818 14.2756L14.2604 19.7661C14.2181 19.8218 14.1524 19.854 14.083 19.854C14.0136 19.854 13.948 19.8218 13.9055 19.7661L9.68382 14.2756C9.65324 14.2357 9.63744 14.1877 9.63744 14.1394C9.63744 14.0918 9.6526 14.044 9.68272 14.0042C9.74317 13.9238 9.85017 13.8935 9.94408 13.9312L12.7391 15.0344V8.56424C12.7391 8.44053 12.839 8.34072 12.963 8.34072H15.2032C15.3265 8.34072 15.427 8.44053 15.427 8.56424V15.0347L18.222 13.9315C18.3157 13.8938 18.4227 13.9241 18.4832 14.0042ZM12.7313 0.312883C-1.74107 0.590678 -0.0121922 7.75616 0.319555 8.80831C0.674874 10.596 2.04426 11.4905 3.43393 10.9834L6.07802 10.156C7.50257 9.63604 8.40635 7.69784 8.09535 5.8258L8.03645 5.47138C12.844 4.1108 16.9905 4.46001 20.2252 5.36045L20.1998 5.51158C19.8903 7.38382 20.7927 9.32253 22.2181 9.84215L24.862 10.9443C26.057 11.3805 27.2363 10.5405 27.7698 9.19915C27.7767 9.20493 27.7809 9.20878 27.7809 9.20878C27.7809 9.20878 27.8541 9.02817 27.9279 8.71903C27.9338 8.69494 27.9383 8.66854 27.9447 8.64484C27.957 8.58826 27.9693 8.52975 27.9816 8.46596C27.989 8.42993 27.9974 8.39583 28.0043 8.35947L28.0017 8.35722C28.3881 6.07623 27.9886 0.0216653 12.7313 0.312883Z" fill="#FF4444" />
                </svg>
              </button>
            </Tooltip>}

            {this.props.SessionDetails.type === "pixel_streaming" ?<>{!showAll ? <Tooltip
              arrow={7.5}
              background="rgb(0 0 0 / 0.1)"
              border="#fff"
              color="#fff"
              content={"expand"}
              fadeDuration={0}
              fadeEasing="linear"
              fixed={false}
              fontFamily="inherit"
              fontSize="1rem"
              offset={0}
              padding={3}
              direction="down"
              radius={4}
              zIndex={1}
              ><button className="p-2 mr-2 rounded bg-[transparent] text-white relative  cursor-pointer  border-[none] transitioning" onClick={this.toggleDivs}><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6">
            <g data-name="arrowhead-right"><rect width="24" height="24" transform="rotate(-90 12 12)" opacity="0"/><path d="M18.78 11.37l-4.78-6a1 1 0 0 0-1.41-.15 1 1 0 0 0-.15 1.41L16.71 12l-4.48 5.37a1 1 0 0 0 .13 1.41A1 1 0 0 0 13 19a1 1 0 0 0 .77-.36l5-6a1 1 0 0 0 .01-1.27z"/><path d="M7 5.37a1 1 0 0 0-1.61 1.26L9.71 12l-4.48 5.36a1 1 0 0 0 .13 1.41A1 1 0 0 0 6 19a1 1 0 0 0 .77-.36l5-6a1 1 0 0 0 0-1.27z"/></g>
     </svg></button></Tooltip> : <Tooltip
                  arrow={7.5}
                  background="rgb(0 0 0 / 0.1)"
                  border="#fff"
                  color="#fff"
                  content={"collapse"}
                  fadeDuration={0}
                  fadeEasing="linear"
                  fixed={false}
                  fontFamily="inherit"
                  fontSize="1rem"
                  offset={0}
                  padding={3}
                  direction="down"
                  radius={4}
                  zIndex={1}
                ><button className="p-2  mr-2 rounded bg-[transparent] text-white relative  cursor-pointer  border-[none] transitioning" onClick={this.toggleDivs}><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6">
                  <g data-name="Layer 2"><g data-name="arrowhead-left"><rect width="24" height="24" transform="rotate(90 12 12)" opacity="0"/><path d="M11.64 5.23a1 1 0 0 0-1.41.13l-5 6a1 1 0 0 0 0 1.27l4.83 6a1 1 0 0 0 .78.37 1 1 0 0 0 .78-1.63L7.29 12l4.48-5.37a1 1 0 0 0-.13-1.4z"/><path d="M14.29 12l4.48-5.37a1 1 0 0 0-1.54-1.28l-5 6a1 1 0 0 0 0 1.27l4.83 6a1 1 0 0 0 .78.37 1 1 0 0 0 .78-1.63z"/></g></g>
                </svg></button></Tooltip>}</>:null}

            </div>
        </div>


        {/* for mobile view */}
        {this.props.ShowControls ? <div className="sm:hidden flex-nowrap backdrop-blur-[6px]  fixed  shadow-[0px_17px_21px_0px_#0000000D] m-auto left-[50%] transform -translate-x-[50%] bottom-2 bg-[black]/10 bg-opacity-10   rounded  realtimeControllerMobile">

          {this.props.SessionDetails.type === "pixel_streaming" ? <>{<button onClick={this.handleButtonClick} className="p-2 mr-2 ml-2">
            <svg className="w-6 h-6" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M18.2716 2.26137L18.2931 2.28289L18.7173 2.70715L18.7389 2.72867C19.1166 3.10637 19.4318 3.42162 19.6683 3.70022C19.915 3.99078 20.1198 4.29043 20.2367 4.65043C20.4162 5.20275 20.4162 5.7977 20.2367 6.35002C20.1198 6.71001 19.915 7.00966 19.6683 7.30023C19.4318 7.57882 19.1166 7.89408 18.7389 8.27178L18.7173 8.29329L18.2931 8.71756L18.2716 8.73907C17.894 9.11682 17.5786 9.43204 17.3 9.66854C17.0094 9.91524 16.7098 10.1199 16.3498 10.2369C15.7975 10.4164 15.2025 10.4164 14.6502 10.2369C14.2902 10.1199 13.9906 9.91524 13.7 9.66854C13.4214 9.43204 13.1061 9.11674 12.7284 8.73907L12.7069 8.71756L12.2827 8.29329L12.2611 8.27178C11.8834 7.89408 11.5682 7.57882 11.3317 7.30023C11.085 7.00966 10.8802 6.71001 10.7633 6.35002C10.5838 5.7977 10.5838 5.20275 10.7633 4.65043C10.8802 4.29043 11.085 3.99078 11.3317 3.70022C11.5682 3.42162 11.8834 3.10637 12.2611 2.72867L12.2827 2.70715L12.7069 2.28289L12.7284 2.26137C13.1061 1.88365 13.4214 1.56839 13.7 1.33188C13.9906 1.0852 14.2902 0.880471 14.6502 0.763501C15.2025 0.584041 15.7975 0.584041 16.3498 0.763501C16.7098 0.880471 17.0094 1.0852 17.3 1.33188C17.5786 1.56839 17.8939 1.88365 18.2716 2.26137ZM4.16957 1.25018H4.2H4.8H4.83044C5.36459 1.25017 5.81044 1.25016 6.17467 1.27992C6.55456 1.31096 6.91121 1.37807 7.24848 1.54991C7.76592 1.81357 8.1866 2.23426 8.4503 2.75171C8.6221 3.08897 8.6892 3.44563 8.7203 3.82551C8.75 4.18974 8.75 4.63557 8.75 5.16971V5.20018V5.80018V5.83061C8.75 6.36475 8.75 6.81063 8.7203 7.17485C8.6892 7.55474 8.6221 7.91139 8.4503 8.24866C8.1866 8.7661 7.76592 9.18684 7.24848 9.45044C6.91121 9.62234 6.55456 9.68944 6.17467 9.72044C5.81044 9.75024 5.36459 9.75024 4.83043 9.75014H4.8H4.2H4.16957C3.63542 9.75024 3.18957 9.75024 2.82533 9.72044C2.44545 9.68944 2.08879 9.62234 1.75153 9.45044C1.23408 9.18684 0.81339 8.7661 0.54973 8.24866C0.37789 7.91139 0.31078 7.55474 0.27974 7.17485C0.24998 6.81063 0.24999 6.36479 0.25 5.83065V5.80018V5.20018V5.16975C0.24999 4.63562 0.24998 4.18974 0.27974 3.82551C0.31078 3.44563 0.37789 3.08897 0.54973 2.75171C0.81339 2.23426 1.23408 1.81357 1.75153 1.54991C2.08879 1.37807 2.44545 1.31096 2.82533 1.27992C3.18956 1.25016 3.63541 1.25017 4.16957 1.25018ZM4.2 12.2501H4.16957C3.63541 12.2501 3.18956 12.2501 2.82533 12.2799C2.44545 12.3109 2.08879 12.378 1.75153 12.5499C1.23408 12.8135 0.81339 13.2342 0.54973 13.7517C0.37789 14.0889 0.31078 14.4456 0.27974 14.8255C0.24998 15.1897 0.24999 15.6355 0.25 16.1697V16.2001V16.8001V16.8306C0.24999 17.3647 0.24998 17.8106 0.27974 18.1748C0.31078 18.5547 0.37789 18.9114 0.54973 19.2486C0.81339 19.7661 1.23408 20.1868 1.75153 20.4504C2.08879 20.6223 2.44545 20.6894 2.82533 20.7204C3.18955 20.7502 3.63537 20.7502 4.16949 20.7501H4.2H4.8H4.83043C5.36455 20.7502 5.81046 20.7502 6.17467 20.7204C6.55456 20.6894 6.91121 20.6223 7.24848 20.4504C7.76592 20.1868 8.1866 19.7661 8.4503 19.2486C8.6221 18.9114 8.6892 18.5547 8.7203 18.1748C8.75 17.8106 8.75 17.3647 8.75 16.8306V16.8001V16.2001V16.1697C8.75 15.6355 8.75 15.1897 8.7203 14.8255C8.6892 14.4456 8.6221 14.0889 8.4503 13.7517C8.1866 13.2342 7.76592 12.8135 7.24848 12.5499C6.91121 12.378 6.55456 12.3109 6.17467 12.2799C5.81044 12.2501 5.36459 12.2501 4.83044 12.2501H4.8H4.2ZM15.1696 12.2501H15.2H15.8H15.8304C16.3646 12.2501 16.8104 12.2501 17.1747 12.2799C17.5546 12.3109 17.9112 12.378 18.2485 12.5499C18.7659 12.8135 19.1866 13.2342 19.4503 13.7517C19.6221 14.0889 19.6892 14.4456 19.7203 14.8255C19.75 15.1897 19.75 15.6356 19.75 16.1697V16.2001V16.8001V16.8306C19.75 17.3647 19.75 17.8106 19.7203 18.1748C19.6892 18.5547 19.6221 18.9114 19.4503 19.2486C19.1866 19.7661 18.7659 20.1868 18.2485 20.4504C17.9112 20.6223 17.5546 20.6894 17.1747 20.7204C16.8105 20.7502 16.3646 20.7502 15.8305 20.7501H15.8H15.2H15.1696C14.6355 20.7502 14.1895 20.7502 13.8253 20.7204C13.4454 20.6894 13.0888 20.6223 12.7515 20.4504C12.2341 20.1868 11.8134 19.7661 11.5497 19.2486C11.3779 18.9114 11.3108 18.5547 11.2797 18.1748C11.25 17.8106 11.25 17.3647 11.25 16.8306V16.8001V16.2001V16.1697C11.25 15.6356 11.25 15.1897 11.2797 14.8255C11.3108 14.4456 11.3779 14.0889 11.5497 13.7517C11.8134 13.2342 12.2341 12.8135 12.7515 12.5499C13.0888 12.378 13.4454 12.3109 13.8253 12.2799C14.1896 12.2501 14.6354 12.2501 15.1696 12.2501Z" fill="white" />
            </svg>
          </button>}</> : <button onClick={this.handleButtonClick} className="p-2 mr-2 ml-2">
            <svg className="w-6 h-6" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M18.2716 2.26137L18.2931 2.28289L18.7173 2.70715L18.7389 2.72867C19.1166 3.10637 19.4318 3.42162 19.6683 3.70022C19.915 3.99078 20.1198 4.29043 20.2367 4.65043C20.4162 5.20275 20.4162 5.7977 20.2367 6.35002C20.1198 6.71001 19.915 7.00966 19.6683 7.30023C19.4318 7.57882 19.1166 7.89408 18.7389 8.27178L18.7173 8.29329L18.2931 8.71756L18.2716 8.73907C17.894 9.11682 17.5786 9.43204 17.3 9.66854C17.0094 9.91524 16.7098 10.1199 16.3498 10.2369C15.7975 10.4164 15.2025 10.4164 14.6502 10.2369C14.2902 10.1199 13.9906 9.91524 13.7 9.66854C13.4214 9.43204 13.1061 9.11674 12.7284 8.73907L12.7069 8.71756L12.2827 8.29329L12.2611 8.27178C11.8834 7.89408 11.5682 7.57882 11.3317 7.30023C11.085 7.00966 10.8802 6.71001 10.7633 6.35002C10.5838 5.7977 10.5838 5.20275 10.7633 4.65043C10.8802 4.29043 11.085 3.99078 11.3317 3.70022C11.5682 3.42162 11.8834 3.10637 12.2611 2.72867L12.2827 2.70715L12.7069 2.28289L12.7284 2.26137C13.1061 1.88365 13.4214 1.56839 13.7 1.33188C13.9906 1.0852 14.2902 0.880471 14.6502 0.763501C15.2025 0.584041 15.7975 0.584041 16.3498 0.763501C16.7098 0.880471 17.0094 1.0852 17.3 1.33188C17.5786 1.56839 17.8939 1.88365 18.2716 2.26137ZM4.16957 1.25018H4.2H4.8H4.83044C5.36459 1.25017 5.81044 1.25016 6.17467 1.27992C6.55456 1.31096 6.91121 1.37807 7.24848 1.54991C7.76592 1.81357 8.1866 2.23426 8.4503 2.75171C8.6221 3.08897 8.6892 3.44563 8.7203 3.82551C8.75 4.18974 8.75 4.63557 8.75 5.16971V5.20018V5.80018V5.83061C8.75 6.36475 8.75 6.81063 8.7203 7.17485C8.6892 7.55474 8.6221 7.91139 8.4503 8.24866C8.1866 8.7661 7.76592 9.18684 7.24848 9.45044C6.91121 9.62234 6.55456 9.68944 6.17467 9.72044C5.81044 9.75024 5.36459 9.75024 4.83043 9.75014H4.8H4.2H4.16957C3.63542 9.75024 3.18957 9.75024 2.82533 9.72044C2.44545 9.68944 2.08879 9.62234 1.75153 9.45044C1.23408 9.18684 0.81339 8.7661 0.54973 8.24866C0.37789 7.91139 0.31078 7.55474 0.27974 7.17485C0.24998 6.81063 0.24999 6.36479 0.25 5.83065V5.80018V5.20018V5.16975C0.24999 4.63562 0.24998 4.18974 0.27974 3.82551C0.31078 3.44563 0.37789 3.08897 0.54973 2.75171C0.81339 2.23426 1.23408 1.81357 1.75153 1.54991C2.08879 1.37807 2.44545 1.31096 2.82533 1.27992C3.18956 1.25016 3.63541 1.25017 4.16957 1.25018ZM4.2 12.2501H4.16957C3.63541 12.2501 3.18956 12.2501 2.82533 12.2799C2.44545 12.3109 2.08879 12.378 1.75153 12.5499C1.23408 12.8135 0.81339 13.2342 0.54973 13.7517C0.37789 14.0889 0.31078 14.4456 0.27974 14.8255C0.24998 15.1897 0.24999 15.6355 0.25 16.1697V16.2001V16.8001V16.8306C0.24999 17.3647 0.24998 17.8106 0.27974 18.1748C0.31078 18.5547 0.37789 18.9114 0.54973 19.2486C0.81339 19.7661 1.23408 20.1868 1.75153 20.4504C2.08879 20.6223 2.44545 20.6894 2.82533 20.7204C3.18955 20.7502 3.63537 20.7502 4.16949 20.7501H4.2H4.8H4.83043C5.36455 20.7502 5.81046 20.7502 6.17467 20.7204C6.55456 20.6894 6.91121 20.6223 7.24848 20.4504C7.76592 20.1868 8.1866 19.7661 8.4503 19.2486C8.6221 18.9114 8.6892 18.5547 8.7203 18.1748C8.75 17.8106 8.75 17.3647 8.75 16.8306V16.8001V16.2001V16.1697C8.75 15.6355 8.75 15.1897 8.7203 14.8255C8.6892 14.4456 8.6221 14.0889 8.4503 13.7517C8.1866 13.2342 7.76592 12.8135 7.24848 12.5499C6.91121 12.378 6.55456 12.3109 6.17467 12.2799C5.81044 12.2501 5.36459 12.2501 4.83044 12.2501H4.8H4.2ZM15.1696 12.2501H15.2H15.8H15.8304C16.3646 12.2501 16.8104 12.2501 17.1747 12.2799C17.5546 12.3109 17.9112 12.378 18.2485 12.5499C18.7659 12.8135 19.1866 13.2342 19.4503 13.7517C19.6221 14.0889 19.6892 14.4456 19.7203 14.8255C19.75 15.1897 19.75 15.6356 19.75 16.1697V16.2001V16.8001V16.8306C19.75 17.3647 19.75 17.8106 19.7203 18.1748C19.6892 18.5547 19.6221 18.9114 19.4503 19.2486C19.1866 19.7661 18.7659 20.1868 18.2485 20.4504C17.9112 20.6223 17.5546 20.6894 17.1747 20.7204C16.8105 20.7502 16.3646 20.7502 15.8305 20.7501H15.8H15.2H15.1696C14.6355 20.7502 14.1895 20.7502 13.8253 20.7204C13.4454 20.6894 13.0888 20.6223 12.7515 20.4504C12.2341 20.1868 11.8134 19.7661 11.5497 19.2486C11.3779 18.9114 11.3108 18.5547 11.2797 18.1748C11.25 17.8106 11.25 17.3647 11.25 16.8306V16.8001V16.2001V16.1697C11.25 15.6356 11.25 15.1897 11.2797 14.8255C11.3108 14.4456 11.3779 14.0889 11.5497 13.7517C11.8134 13.2342 12.2341 12.8135 12.7515 12.5499C13.0888 12.378 13.4454 12.3109 13.8253 12.2799C14.1896 12.2501 14.6354 12.2501 15.1696 12.2501Z" fill="white" />
            </svg>
          </button>}
          {this.props.SessionDetails.type === "pixel_streaming" ? <>{isPopUpOpen && (
            <div className="w-[135px] bg-[black]/10 bg-opacity-10  h-[auto] shadow-[0px_17px_21px_0px_#0000000D] rounded gap-1 px-2 py-3 fixed left-[0px] bottom-[47px] backdrop-blur-[18px] ">
              <div className="flex flex-col gap-[15px] text-white font-[Nunito Sans] text-base  text-[17px] font-normal leading-[22px] tracking-normal text-left ">
                {navigator.mediaDevices.getDisplayMedia ?
                  <div onClick={this.ShareScreen}>Share Content</div> : <></>}
                <div onClick={() => { this.props.TabControl(this.props.Tab == "MEMBERS" ? false : "MEMBERS") }}>Participants</div>
                <div onClick={() => { this.props.TabControl(this.props.Tab == "CHAT" ? false : "CHAT") }}>Chats</div>
              </div>
            </div>
          )}</> : <>{(
            <div className="w-[135px] bg-[black]/10 bg-opacity-10  h-[auto] shadow-[0px_17px_21px_0px_#0000000D] rounded gap-1 px-2 py-3 fixed left-[0px] bottom-[47px] backdrop-blur-[18px] ">
              <div className="flex flex-col gap-[15px] text-white font-[Nunito Sans] text-base  text-[17px] font-normal leading-[22px] tracking-normal text-left ">
                {navigator.mediaDevices.getDisplayMedia ?
                  <div onClick={this.ShareScreen}>Share Content</div> : <></>}
                <div onClick={() => { this.props.TabControl(this.props.Tab == "MEMBERS" ? false : "MEMBERS") }}>Participants</div>
                <div onClick={() => { this.props.TabControl(this.props.Tab == "CHAT" ? false : "CHAT") }}>Chats</div>
              </div>
            </div>
          )}</>}


          {this.props.SessionDetails.type === "pixel_streaming" ? <>{this.props.Audio ? <Tooltip
            arrow={7.5}
            background="rgb(0 0 0 / 0.1)"
            border="#fff"
            color="#fff"
            content={"Mute"}
            fadeDuration={0}
            fadeEasing="linear"
            fixed={false}
            fontFamily="inherit"
            fontSize="1rem"
            offset={0}
            padding={3}
            direction="up"
            radius={4}
            zIndex={1}
          ><button id="AudioCtrl" onClick={this.AudioControl} className="p-2 mr-2 rounded bg-[transparent] relative  cursor-pointer  border-[none]">
              <svg className="w-6 h-6 text-white" fill="white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="mic"><rect width="24" height="24" opacity="0" /><path d="M12 15a4 4 0 0 0 4-4V6a4 4 0 0 0-8 0v5a4 4 0 0 0 4 4z" /><path d="M19 11a1 1 0 0 0-2 0 5 5 0 0 1-10 0 1 1 0 0 0-2 0 7 7 0 0 0 6 6.92V20H8.89a.89.89 0 0 0-.89.89v.22a.89.89 0 0 0 .89.89h6.22a.89.89 0 0 0 .89-.89v-.22a.89.89 0 0 0-.89-.89H13v-2.08A7 7 0 0 0 19 11z" /></g></g></svg>
            </button>
          </Tooltip> : <Tooltip
            arrow={7.5}
            background="rgb(0 0 0 / 0.1)"
            border="#fff"
            color="#fff"
            content={"UnMute"}
            fadeDuration={0}
            fadeEasing="linear"
            fixed={false}
            fontFamily="inherit"
            fontSize="1rem"
            offset={0}
            padding={3}
            direction="up"
            radius={4}
            zIndex={1}
          ><button id="AudioCtrl" onClick={this.AudioControl} className="p-2 mr-2 rounded bg-[transparent] relative  cursor-pointer  border-[none]">
              <svg className="w-6 h-6 text-white" fill="white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="mic-off"><rect width="24" height="24" opacity="0" /><path d="M15.58 12.75A4 4 0 0 0 16 11V6a4 4 0 0 0-7.92-.75" /><path d="M19 11a1 1 0 0 0-2 0 4.86 4.86 0 0 1-.69 2.48L17.78 15A7 7 0 0 0 19 11z" /><path d="M12 15h.16L8 10.83V11a4 4 0 0 0 4 4z" /><path fill="white" d="M20.71 19.29l-16-16a1 1 0 0 0-1.42 1.42l16 16a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42z" /><path d="M15 20h-2v-2.08a7 7 0 0 0 1.65-.44l-1.6-1.6A4.57 4.57 0 0 1 12 16a5 5 0 0 1-5-5 1 1 0 0 0-2 0 7 7 0 0 0 6 6.92V20H9a1 1 0 0 0 0 2h6a1 1 0 0 0 0-2z" /></g></g></svg>
            </button>
          </Tooltip>}</> : this.props.Audio ? <Tooltip
            arrow={7.5}
            background="rgb(0 0 0 / 0.1)"
            border="#fff"
            color="#fff"
            content={"Mute"}
            fadeDuration={0}
            fadeEasing="linear"
            fixed={false}
            fontFamily="inherit"
            fontSize="1rem"
            offset={0}
            padding={3}
            direction="up"
            radius={4}
            zIndex={1}
          ><button id="AudioCtrl" onClick={this.AudioControl} className="p-2 mr-2 rounded bg-[transparent] relative  cursor-pointer  border-[none]">
              <svg className="w-6 h-6 text-white" fill="white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="mic"><rect width="24" height="24" opacity="0" /><path d="M12 15a4 4 0 0 0 4-4V6a4 4 0 0 0-8 0v5a4 4 0 0 0 4 4z" /><path d="M19 11a1 1 0 0 0-2 0 5 5 0 0 1-10 0 1 1 0 0 0-2 0 7 7 0 0 0 6 6.92V20H8.89a.89.89 0 0 0-.89.89v.22a.89.89 0 0 0 .89.89h6.22a.89.89 0 0 0 .89-.89v-.22a.89.89 0 0 0-.89-.89H13v-2.08A7 7 0 0 0 19 11z" /></g></g></svg>
            </button>
          </Tooltip> : <Tooltip
            arrow={7.5}
            background="rgb(0 0 0 / 0.1)"
            border="#fff"
            color="#fff"
            content={"UnMute"}
            fadeDuration={0}
            fadeEasing="linear"
            fixed={false}
            fontFamily="inherit"
            fontSize="1rem"
            offset={0}
            padding={3}
            direction="up"
            radius={4}
            zIndex={1}
          ><button id="AudioCtrl" onClick={this.AudioControl} className="p-2 mr-2 rounded bg-[transparent] relative  cursor-pointer  border-[none]">
              <svg className="w-6 h-6 text-white" fill="white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="mic-off"><rect width="24" height="24" opacity="0" /><path d="M15.58 12.75A4 4 0 0 0 16 11V6a4 4 0 0 0-7.92-.75" /><path d="M19 11a1 1 0 0 0-2 0 4.86 4.86 0 0 1-.69 2.48L17.78 15A7 7 0 0 0 19 11z" /><path d="M12 15h.16L8 10.83V11a4 4 0 0 0 4 4z" /><path fill="white" d="M20.71 19.29l-16-16a1 1 0 0 0-1.42 1.42l16 16a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42z" /><path d="M15 20h-2v-2.08a7 7 0 0 0 1.65-.44l-1.6-1.6A4.57 4.57 0 0 1 12 16a5 5 0 0 1-5-5 1 1 0 0 0-2 0 7 7 0 0 0 6 6.92V20H9a1 1 0 0 0 0 2h6a1 1 0 0 0 0-2z" /></g></g></svg>
            </button>
          </Tooltip>}


          {this.props.SessionDetails.type === "pixel_streaming" ? <>{this.props.Video ? <Tooltip
            arrow={7.5}
            background="rgb(0 0 0 / 0.1)"
            border="#fff"
            color="#fff"
            content={"TurnOff Video"}
            fadeDuration={0}
            fadeEasing="linear"
            fixed={false}
            fontFamily="inherit"
            fontSize="1rem"
            offset={0}
            padding={3}
            direction="up"
            radius={4}
            zIndex={1}
          >
            <button id="VideoCtrl" onClick={this.VideoControl} className="p-2 mr-2 rounded bg-[transparent] relative  cursor-pointer  border-[none]">
              <svg className="w-6 h-6 text-white" fill="white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="video"><rect width="24" height="24" opacity="0" /><path d="M21 7.15a1.7 1.7 0 0 0-1.85.3l-2.15 2V8a3 3 0 0 0-3-3H5a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h9a3 3 0 0 0 3-3v-1.45l2.16 2a1.74 1.74 0 0 0 1.16.45 1.68 1.68 0 0 0 .69-.15 1.6 1.6 0 0 0 1-1.48V8.63A1.6 1.6 0 0 0 21 7.15z" /></g></g></svg>
            </button>
          </Tooltip> : <Tooltip
            arrow={7.5}
            background="rgb(0 0 0 / 0.1)"
            border="#fff"
            color="#fff"
            content={"TurnOn Video"}
            fadeDuration={0}
            fadeEasing="linear"
            fixed={false}
            fontFamily="inherit"
            fontSize="1rem"
            offset={0}
            padding={3}
            direction="up"
            radius={4}
            zIndex={1}
          >
            <button id="VideoCtrl" onClick={this.VideoControl} className="p-2 mr-2 rounded bg-[transparent] relative  cursor-pointer  border-[none]">
              <svg className="w-6 h-6 text-white" fill="white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="video-off"><rect width="24" height="24" opacity="0" /><path d="M14.22 17.05L4.88 7.71 3.12 6 3 5.8A3 3 0 0 0 2 8v8a3 3 0 0 0 3 3h9a2.94 2.94 0 0 0 1.66-.51z" /><path d="M21 7.15a1.7 1.7 0 0 0-1.85.3l-2.15 2V8a3 3 0 0 0-3-3H7.83l1.29 1.29 6.59 6.59 2 2 2 2a1.73 1.73 0 0 0 .6.11 1.68 1.68 0 0 0 .69-.15 1.6 1.6 0 0 0 1-1.48V8.63a1.6 1.6 0 0 0-1-1.48z" /><path fill="white" d="M17 15.59l-2-2L8.41 7l-2-2-1.7-1.71a1 1 0 0 0-1.42 1.42l.54.53L5.59 7l9.34 9.34 1.46 1.46 2.9 2.91a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42z" /></g></g></svg>
            </button>
          </Tooltip>}</> : this.props.Video ? <Tooltip
            arrow={7.5}
            background="rgb(0 0 0 / 0.1)"
            border="#fff"
            color="#fff"
            content={"TurnOff Video"}
            fadeDuration={0}
            fadeEasing="linear"
            fixed={false}
            fontFamily="inherit"
            fontSize="1rem"
            offset={0}
            padding={3}
            direction="up"
            radius={4}
            zIndex={1}
          >
            <button id="VideoCtrl" onClick={this.VideoControl} className="p-2 mr-2 rounded bg-[transparent] relative  cursor-pointer  border-[none]">
              <svg className="w-6 h-6 text-white" fill="white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="video"><rect width="24" height="24" opacity="0" /><path d="M21 7.15a1.7 1.7 0 0 0-1.85.3l-2.15 2V8a3 3 0 0 0-3-3H5a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h9a3 3 0 0 0 3-3v-1.45l2.16 2a1.74 1.74 0 0 0 1.16.45 1.68 1.68 0 0 0 .69-.15 1.6 1.6 0 0 0 1-1.48V8.63A1.6 1.6 0 0 0 21 7.15z" /></g></g></svg>
            </button>
          </Tooltip> : <Tooltip
            arrow={7.5}
            background="rgb(0 0 0 / 0.1)"
            border="#fff"
            color="#fff"
            content={"TurnOn Video"}
            fadeDuration={0}
            fadeEasing="linear"
            fixed={false}
            fontFamily="inherit"
            fontSize="1rem"
            offset={0}
            padding={3}
            direction="up"
            radius={4}
            zIndex={1}
          >
            <button id="VideoCtrl" onClick={this.VideoControl} className="p-2 mr-2 rounded bg-[transparent] relative  cursor-pointer  border-[none]">
              <svg className="w-6 h-6 text-white" fill="white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="video-off"><rect width="24" height="24" opacity="0" /><path d="M14.22 17.05L4.88 7.71 3.12 6 3 5.8A3 3 0 0 0 2 8v8a3 3 0 0 0 3 3h9a2.94 2.94 0 0 0 1.66-.51z" /><path d="M21 7.15a1.7 1.7 0 0 0-1.85.3l-2.15 2V8a3 3 0 0 0-3-3H7.83l1.29 1.29 6.59 6.59 2 2 2 2a1.73 1.73 0 0 0 .6.11 1.68 1.68 0 0 0 .69-.15 1.6 1.6 0 0 0 1-1.48V8.63a1.6 1.6 0 0 0-1-1.48z" /><path fill="white" d="M17 15.59l-2-2L8.41 7l-2-2-1.7-1.71a1 1 0 0 0-1.42 1.42l.54.53L5.59 7l9.34 9.34 1.46 1.46 2.9 2.91a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42z" /></g></g></svg>
            </button>
          </Tooltip>}

          {this.props.SessionDetails.type === "pixel_streaming" ? <>{ <Tooltip
            arrow={7.5}
            background="rgb(0 0 0 / 0.1)"
            border="#fff"
            color="#fff"
            content={"End call"}
            fadeDuration={0}
            fadeEasing="linear"
            fixed={false}
            fontFamily="inherit"
            fontSize="1rem"
            offset={0}
            padding={3}
            direction="up"
            radius={4}
            zIndex={1}
          >
            <button className="p-2 mr-2 rounded bg-[transparent] relative  cursor-pointer   border-[none]" onClick={() => { this.OpenCloseModal() }}>
              <svg className="w-6 h-6" viewBox="0 0 29 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M18.4832 14.0042C18.5439 14.0844 18.5436 14.1951 18.4818 14.2756L14.2604 19.7661C14.2181 19.8218 14.1524 19.854 14.083 19.854C14.0136 19.854 13.948 19.8218 13.9055 19.7661L9.68382 14.2756C9.65324 14.2357 9.63744 14.1877 9.63744 14.1394C9.63744 14.0918 9.6526 14.044 9.68272 14.0042C9.74317 13.9238 9.85017 13.8935 9.94408 13.9312L12.7391 15.0344V8.56424C12.7391 8.44053 12.839 8.34072 12.963 8.34072H15.2032C15.3265 8.34072 15.427 8.44053 15.427 8.56424V15.0347L18.222 13.9315C18.3157 13.8938 18.4227 13.9241 18.4832 14.0042ZM12.7313 0.312883C-1.74107 0.590678 -0.0121922 7.75616 0.319555 8.80831C0.674874 10.596 2.04426 11.4905 3.43393 10.9834L6.07802 10.156C7.50257 9.63604 8.40635 7.69784 8.09535 5.8258L8.03645 5.47138C12.844 4.1108 16.9905 4.46001 20.2252 5.36045L20.1998 5.51158C19.8903 7.38382 20.7927 9.32253 22.2181 9.84215L24.862 10.9443C26.057 11.3805 27.2363 10.5405 27.7698 9.19915C27.7767 9.20493 27.7809 9.20878 27.7809 9.20878C27.7809 9.20878 27.8541 9.02817 27.9279 8.71903C27.9338 8.69494 27.9383 8.66854 27.9447 8.64484C27.957 8.58826 27.9693 8.52975 27.9816 8.46596C27.989 8.42993 27.9974 8.39583 28.0043 8.35947L28.0017 8.35722C28.3881 6.07623 27.9886 0.0216653 12.7313 0.312883Z" fill="#FF4444" />
              </svg>
            </button>
          </Tooltip>}</> : <Tooltip
            arrow={7.5}
            background="rgb(0 0 0 / 0.1)"
            border="#fff"
            color="#fff"
            content={"End call"}
            fadeDuration={0}
            fadeEasing="linear"
            fixed={false}
            fontFamily="inherit"
            fontSize="1rem"
            offset={0}
            padding={3}
            direction="up"
            radius={4}
            zIndex={1}
          >
            <button className="p-2 mr-2 rounded bg-[transparent] relative  cursor-pointer   border-[none]" onClick={() => { this.OpenCloseModal() }}>
              <svg className="w-6 h-6" viewBox="0 0 29 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M18.4832 14.0042C18.5439 14.0844 18.5436 14.1951 18.4818 14.2756L14.2604 19.7661C14.2181 19.8218 14.1524 19.854 14.083 19.854C14.0136 19.854 13.948 19.8218 13.9055 19.7661L9.68382 14.2756C9.65324 14.2357 9.63744 14.1877 9.63744 14.1394C9.63744 14.0918 9.6526 14.044 9.68272 14.0042C9.74317 13.9238 9.85017 13.8935 9.94408 13.9312L12.7391 15.0344V8.56424C12.7391 8.44053 12.839 8.34072 12.963 8.34072H15.2032C15.3265 8.34072 15.427 8.44053 15.427 8.56424V15.0347L18.222 13.9315C18.3157 13.8938 18.4227 13.9241 18.4832 14.0042ZM12.7313 0.312883C-1.74107 0.590678 -0.0121922 7.75616 0.319555 8.80831C0.674874 10.596 2.04426 11.4905 3.43393 10.9834L6.07802 10.156C7.50257 9.63604 8.40635 7.69784 8.09535 5.8258L8.03645 5.47138C12.844 4.1108 16.9905 4.46001 20.2252 5.36045L20.1998 5.51158C19.8903 7.38382 20.7927 9.32253 22.2181 9.84215L24.862 10.9443C26.057 11.3805 27.2363 10.5405 27.7698 9.19915C27.7767 9.20493 27.7809 9.20878 27.7809 9.20878C27.7809 9.20878 27.8541 9.02817 27.9279 8.71903C27.9338 8.69494 27.9383 8.66854 27.9447 8.64484C27.957 8.58826 27.9693 8.52975 27.9816 8.46596C27.989 8.42993 27.9974 8.39583 28.0043 8.35947L28.0017 8.35722C28.3881 6.07623 27.9886 0.0216653 12.7313 0.312883Z" fill="#FF4444" />
              </svg>
            </button>
          </Tooltip>}


        </div> : <></>}



        <div style={{ display: this.state.fullscreenModal === true && !this.state.share ? 'block' : 'none' }} className="bg-[black]/40 bg-opacity-40 backdrop-blur fixed z-[10000] hidden overflow-hidden inset-0 p-4 h-full w-full" id="share_modal" tabIndex="-1" role="dialog">
          <div className=" px-3 sm:mx-auto flex justify-center items-center sm:py-7 h-full w-full" role="document">
            <div className="max-w-full p-4 sm:max-w-lg h-fit flex flex-col w-full pointer-events-auto bg-transparent backdrop-blur m-auto rounded inset-0 sm:shadow">
              <div className="flex items-start justify-between  rounded-t-[0.3rem] mb-3">
                <h5 className="text-lg font-[bold] not-italic tracking-[normal] text-white mb-0 font-sans mt-0">Would you like to switch to full-screen mode?</h5>

              </div>
              <div className="relative flex-auto">
                <p className="text-sm mt-0 mb-3 mx-0 text-white ">To enhance your viewing experience, we recommend switching to full-screen mode. Would you like to make the switch?</p>
              </div>
              <div className=" block border-t-[none]">
                <center className="flex justify-center mt-2 sm:mt-3">
                  <button onClick={() => { this.setState({ fullscreenModal: false }) }} type="button" className="w-40 h-10 lg:h-11 rounded border-2 border-solid border-[#36f] hover:border-[#4572fc] bg-transparent m-0 px-3 text-sm text-white font-semibold leading-6">No</button>
                  <button onClick={this.switchToFullscreen} type="button" className="ml-3 w-40 h-10 lg:h-11  rounded bg-[#36f] hover:bg-[#4572fc] border-0 m-0 px-3 text-sm text-white font-semibold leading-6">Yes</button>
                </center>
              </div>
            </div>
          </div>
        </div>
        <Share open_close={this.open_close} share={this.state.share} pid={this.props.roompid} roomId={this.props.roomId} user_id={Firebase.auth().currentUser.uid}  ></Share>
        {this.props.data ? <DocumentModal senddata={this.props.senddata} room={this.props.roomId} data={this.props.data} open_close={this.open_close} document={this.state.document}></DocumentModal> : ""}
        {this.state.project ? <Switchproject switch={this.props.switch} changeProject={this.props.changeProject} data={this.props.data} open_close={this.open_close} project={this.state.project} pid={this.state.pid} user_id={Firebase.auth().currentUser.uid}></Switchproject> : ""}
        {this.state.close ? <EndSession roomid={this.props.roomId} CloseSession={this.CloseSession} Close={() => { this.setState({ close: false }) }} /> : ""}
      </>
    )
  }

}

const mapStateTothisprops = state => {
  return {
    Peers: state.Call.peers,
    SocketId: state.Call.SocketId,
    LocalStream: state.Call.LocalStream,
    UserName: state.Call.UserName,
    Video: state.Call.Video,
    Audio: state.Call.Audio,
    ScreenShare: state.Call.ScreenShare,
    Recorder: state.Call.Recorder,
    CameraAccess: state.Call.HasCamera,
    MicrophoneAccess: state.Call.HasMicrophone,
    Tab: state.Call.Tab,
    ShowControls: state.Call.ShowControls,
    userscount: state.Call.userscount,
    UnreadCount: state.Call.UnreadCount,
    SessionDetails: state.Sessions.sessions,
  }
}

const mapDispatchTothisprops = {
  ...HostActions,
  ...ExceptionActions
}

export default connect(mapStateTothisprops, mapDispatchTothisprops)(RealTimeControls)

