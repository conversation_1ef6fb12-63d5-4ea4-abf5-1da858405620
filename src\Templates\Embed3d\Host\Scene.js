import React from "react"
import { connect } from "react-redux"
class Scene extends React.Component {
    componentDidMount() {

    }
    render() {
        return (
            <>
                <a-scene embedded
                    id="room"
                    loading-screen="enabled:false"
                    renderer=" colorManagement: true;
                   sortObjects: true;
                   physicallyCorrectLights: true;"
                    vr-mode-ui="enabled: true;enterVRButton: #myEnterVRButton"
                    device-orientation-permission-ui="enabled: false"
                    assetloaded
                    rotation-reader
                    keyboard-rot
                    scenelistener
                    style={{ position: "absolute", cursor: "unset", top: "0px", right: "0px", height: "100%" }}
                >
                    <a-entity id="mouseCursor" cursor="rayOrigin: mouse;fuse:false;" raycaster="objects:.modelhome,.boxes,.links,.arrows">
                    </a-entity>
                    <a-assets id="assets">
                        <img
                            id="hotspotimage"
                            crossOrigin="anonymous"
                            src="images/propvrpointer.svg"
                        ></img>
                        <img id="infoimage" crossOrigin="anonymous" src="https://storagecdn.propvr.tech/assets%2Finfohotspot.svg"></img>
                        <img
                            id="modelsky"
                            crossOrigin="anonymous"
                            src="https://storagecdn.propvr.tech/assets%2Fbggray.jpg"
                        ></img>
                        <img id="navarrowimg" src="https://cdn.glitch.com/d919fddc-bed5-4101-a53e-3764014fe3bb%2Farrow2.svg?v=1610102689100"></img>
                        <img
                            id="planecursorimage"
                            crossOrigin="anonymous"
                            src="images/propvrpointer.svg"
                        ></img>

                        <img
                            id="modelfloorplan"
                            crossOrigin="anonymous"
                            src="https://storagecdn.propvr.tech/assets%2Fbggray.jpg"
                        ></img>
                    </a-assets>
                    <a-assets id="model"> </a-assets>
                    <a-image id="planecursor"
                        rotation="-90 0 0"
                        src="#planecursorimage"
                        look-at="player"
                        material="transparent:true;opacity:0.8;shader:flat;depthTest:false"
                        width="0.25"
                        height="0.25"
                        visible="false"
                    ></a-image>
                    <a-entity id="outer-info" position="0 0 0">
                    </a-entity>
                    <a-entity camera="active:false" id="player" modlook-controls compass>

                        <a-entity id="vrcursor"
                            position="0 0 -2"
                            geometry="primitive: ring; radiusInner: 0.01; radiusOuter: 0.02"
                            material="color: white; shader: flat;opacity:0.9;transparent:true" raycaster="far: 1000; interval: 2000; objects:.boxes,.links,.vrarrows,.button_svg" visible="false">
                        </a-entity>

                        <a-entity id="navarrow" position="0 -4 -8.5" counter-rot visible="false">
                            <a-image id="uparr" class="arrows" position="-2 0 0" rotation="-90 90 0" src="#navarrowimg" material="side:front;opacity:0.5;depthTest:false" visible="false" arrow-listener></a-image>
                            <a-image id="downarr" class="arrows" position="2 0 0" rotation="-90 -90 0" src="#navarrowimg" material="side:front;opacity:0.5;depthTest:false" visible="false" arrow-listener></a-image>
                            <a-image id="leftarr" class="arrows" position="0 0 2" rotation="-90 180 0" src="#navarrowimg" material="side:front;opacity:0.5;depthTest:false" visible="false" arrow-listener></a-image>
                            <a-image id="rightarr" class="arrows" position="0 0 -2" rotation="-90 0 0" src="#navarrowimg" material="side:front;opacity:0.5;depthTest:false" visible="false" arrow-listener></a-image>
                        </a-entity>



                    </a-entity>
                </a-scene>

            </>
        )
    }
}


// const mapStateToProps = state => {
//     return {
//       SETUP:state.Call.SETUP,
//       CanvasTrack:state.Call.CanvasTrack,
//       ModalException:state.Exception.modalexception,
//       DummyAudio:state.Call.DummyAudio,
//       ClientData:state.Call.ClientData
//     }
//   }

//   const mapDispatchToProps = {
//     ...HostActions
//   }

export default connect(null, null)(Scene)