.switch {
  position: relative;
  display: inline-block;
  width: 50px; /* Adjust the width to your preference */
  height: 24px; /* Adjust the height to your preference */
  margin-left:16px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: .4s;
  transition: .4s;
  border-radius: 12px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  -webkit-transition: .4s;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #2196F3;
}

input:focus + .slider {
  box-shadow: 0 0 1px #2196F3;
}

input:checked + .slider:before {
  -webkit-transform: translateX(24px);
  -ms-transform: translateX(24px);
  transform: translateX(24px);
}


.collapsible{
  margin-top: 10px;
  margin-right: 5px;
  display: block;
  width: 100%;
  color: #212529;
  background-color: #f8f9fa;
  border-radius: 0.25rem;
  border-color: #f8f9fa;
  padding: .375rem .75rem;
}

/* .active, .collapsible:hover {
  background-color: #e2e6ea;
} */

.add-meeting {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 12px;
  background-color: #1a73e8;
  color: #fff;
  border-radius: 4px;
  transition: background-color 0.2s;
  margin: auto 10px;
  padding: 0px 10px;
  height: 45px;
}

.add-meeting i {
  font-size: 20px;
  margin-right: 10px;
}

.add-meeting:hover {
  background-color: #0a55c2;
}


.schedule-buttons {
  position: absolute;
  right: 7%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 4px;
  z-index: 1000;
  animation: slide-down 0.2s ease-in;
}

.schedule-buttons button {
  background-color: #1a73e8;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 10px 16px;
  cursor: pointer;
  margin: 5px;
  transition: background-color 0.2s;
}

.schedule-buttons button:hover {
  background-color: #0a55c2;
}
@keyframes slide-down {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

















.map{
  margin: auto;
    position: inherit;
    display: inline-block;
    width: 100%;
    height: 100%;
}
.map>div{
  position: relative !important;
    width: 100%;
    height: 100%;
}
.map_button_active{
  background-color: var(--main-color) !important;
  color: white !important;
  border:none !important
}
@media only screen and (max-width:599px){
  .map-modal-dialog{
    max-width: 100%;
  }
}
.map-modal-dialog{
  max-width: 60%;
}

.group{
  padding-top: 10px;

}

.map-menu_option{
  position: absolute;
  margin: 7px 0px 0px 15px;
  border-radius: 4px;
  background-color: var(--main-color);
  border:none;
  height: 42px;
  cursor: pointer;
  width: 46px;
}

.map-closebtn {
  position: absolute;
    right: 15px;
    font-size: 1px;
    background: #fff;
    padding: 3px;
    border-radius: 20px;
    cursor: pointer;
}
.sidenavg {
  height: 100%;
  width: 0;
  position: absolute;
  z-index: 1;
  top: 0;
  background-color: white;
  transition: all 0.5s ease-in-out;
}

.sub-header{
  text-transform: uppercase;
  font-weight: bold;
  color: var(--main-color);
  padding-bottom: 0px;
  margin-bottom: 0px;
}

.map-modal{
  font-size: 1rem;
text-align: left;
color: #000000;
font-family: "propvr";
font-weight: 300;
line-height: 1.5em;
box-sizing: border-box;
-webkit-tap-highlight-color: transparent;
-webkit-font-smoothing: antialiased;
transition: opacity 0.15s linear;
position: fixed;
top: 0;
right: 0;
bottom: 0;
left: 0;
z-index: 1050;
outline: 0;
background-image: linear-gradient(to bottom, #00000087, #00000087);
opacity: 1;
overflow-x: hidden;
overflow-y: auto;
display: block;
}

.out-primary{
  border: 1px solid #007bff;
  background-color: transparent;
  color: #007bff;
  border-color: #007bff;
}

.out-primary:hover{
  background-color: var(--main-color);
  color: white;
}

.out-secondary{
  border: 1px solid #8f9bb3;
  background-color: transparent;
  color: #8f9bb3;
  border-color: #8f9bb3;
}
.out-secondary:hover{
  background-color: var(--main-color);
  color: white;
}

.map-body{
  font-size: 1rem;
text-align: left;
color: #000000;
font-family: "propvr";
font-weight: 300;
line-height: 1.5em;
box-sizing: border-box;
-webkit-tap-highlight-color: transparent;
-webkit-font-smoothing: antialiased;
display: flex;
flex-direction: column;
width: 100%;
pointer-events: auto;
background-color: #fff;
background-clip: padding-box;
outline: 0;
position: absolute;
bottom: 0;
top: 0;
left: 0;
right: 0;
margin: auto;
border-radius: 0px !important;
height: fit-content;
box-shadow: 0 27px 24px 0 rgba(0, 0, 0, 0.2), 0 40px 77px 0 rgba(0, 0, 0, 0.22);
border: none;
}
.map-col{
  font-size: 1rem;
text-align: left;
color: #000000;
font-family: "propvr";
font-weight: 300;
line-height: 1.5em;
pointer-events: auto;
box-sizing: border-box;
-webkit-tap-highlight-color: transparent;
-webkit-font-smoothing: antialiased;
flex-basis: 0;
flex-grow: 1;
max-width: 100%;

padding: 8px;
}

.plus{
  text-align: end;
}
.map-row{
  font-size: 1rem;
text-align: left;
color: #000000;
font-family: "propvr";
font-weight: 300;
line-height: 1.5em;
pointer-events: auto;
box-sizing: border-box;
-webkit-tap-highlight-color: transparent;
-webkit-font-smoothing: antialiased;
display: flex;
flex-wrap: wrap;
border-radius: 4px;
}


.map-header{
  width: 175px;
  height: 24px;
  font-size: 12px;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.6;
  letter-spacing: normal;
  color: #222b45;
}

.map_option_div_close{
  display:none;
  font-size: 20px;
  color: #fff;
  border-radius: 4px;
  background-color: var(--main-color);
  border: none;
  margin-left: 5px;
  margin-top: 15px;
  cursor: pointer;
  width: 30px;
  height: 30px;
}

.contentg {
  margin-right: 5px;
  width: 100%;
  /* padding: 0rem .75rem; */
  overflow: hidden;
  background-color: #edf1f7;
  border-bottom-left-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

.content-button2{
  cursor: context-menu;
  text-align: center;
  color: var(--main-color);
  border-top: 1px solid #c5cee0;
  padding: .375rem .75rem;
}


.stick-right{
  text-align: end;
}

.stick-left{
  margin-left: 0.70rem;
}


.location-label{
  font-weight: bold;
  color: black;
}

.label{
  cursor: context-menu;
}

.map-menu{
  padding-left: 15px;
}

.group-body{
  margin: 15px 0px 0px 0px ;
}

.map-close-btn{
  font-size: 30px;
}