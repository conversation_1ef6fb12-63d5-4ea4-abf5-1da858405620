import React from "react";

import Share from "./ShareModal";
// import CloseModal from './CloseModal';
// import FloorplanClient from "./FloorplanClient";
// import MapModal from "./Mapmodal";
// import PDFclient from "./PDFclient";
import {connect} from "react-redux"
import * as HostActions from "../../Actions/HostAction"
import * as ExceptionActions from "../../Actions/Exception"

class SceneControls extends React.Component { 
  constructor(props){
    super(props);
    this.state={
      share:true,
      menu_bar:true,
      data:this.props.data,
      pid:this.props.pid,
      close:false,
      settings:false,
      lock:true,
      chooseusermodal:false,
      selecteduser:false
    }

    this.lockUser=this.lockUser.bind(this);
  }


  componentDidMount(){   

    window.scrollTo(0, 0);
    document.getElementById('menu_bar').click();
    //this.handleOutsideClick = this.handleOutsideClick.bind(this);
    //document.addEventListener('click', this.handleOutsideClick, false);
    this.menu.addEventListener("click", this.menu_bar_open);
    //this.menu_bar_open();

  
      

   
   
  }
  componentDidUpdate(prevProps){
    
  }
  // handleOutsideClick(e) {


  //   // ignore clicks on the component itself

  //   if(this.state.menu_bar && e.target.id!='menu_bar_up_icon'){
  //     this.setState({
  //       menu_bar:false
  //     })
  //     document.getElementById('menu_bar').classList.remove('menu_option_click');
  //     document.getElementById('tools_div').classList.remove('show');
  //     document.removeEventListener('click', this.handleOutsideClick, false);

  //   }else{
  //     document.addEventListener('click', this.handleOutsideClick, false);

  //     this.setState({
  //       menu_bar:true
  //     })
  //     document.getElementById('tools_div').classList.add('show');
  //     document.getElementById('menu_bar').classList.add('menu_option_click');
  //   }

   
    

  // }
  handleChange = (event) => {
    this.setState({
      selecteduser:event.target.value
    })
  };
  lockUser=(e)=>{
    e.preventDefault();
    const data = {
      actiontype: "unlock",
      lockmode: false,
      targetuser:this.props.Peers[this.state.selecteduser].extra.id || "All"
    };

    this.props.SendCustomMessage(data,this.props.roomId)
    this.setState({
      lock: false
    })

    this.props.CreateToast({message:"Now "+this.props.Peers[this.state.selecteduser].extra.username+" has control"})
    this.open_close('chooseusermodal',false)
  
  }
revoke =()=>{
  const data = {
    actiontype: "lock",
    lockmode: true,
    targetuser:"All"
  };

  this.props.SendCustomMessage(data,this.props.roomId)
  this.setState({
    lock: true
  })
  this.props.CreateToast({message:"Access Revoked"})

  this.open_close('chooseusermodal',false)

}
menu_bar_open = (event) => {
 
  if(this.state.menu_bar){
    this.setState({
      menu_bar:false
    })
    document.getElementById('menu_bar').classList.remove('menu_option_click');
    document.getElementById('tools_div').classList.remove('show');
   // document.removeEventListener('click', this.handleOutsideClick, false);

  }
  else{
    this.setState({
      menu_bar:true
    })
   // document.addEventListener('click', this.handleOutsideClick, false);
    document.getElementById('tools_div').classList.add('show');
    document.getElementById('menu_bar').classList.add('menu_option_click');
  }
}
open_close = (name,flag) =>{
//  document.getElementById('tools_div').classList.remove('show');
this.setState({
  selecteduser:false
})
  this.setState({
    [name]:flag
  })
    if(flag){
    }
}
  render() {

    

return (

  <>
   
        <div style={{height: '72px', flexWrap: 'nowrap',position:"fixed",bottom:"0px",left:"8px",zIndex:"9"}} className="row">
          <div className="content_padding dropup">
            <button style={{marginLeft:"9px"}} ref={elem => this.menu = elem}  id="menu_bar" type="button" className="menu_option dropdown-toggle menu_option_click">
              <span id="menu_bar_up" style={{display:this.state.menu_bar===false?'block':'none'}}>
                <svg id="menu_bar_up_icon" style={{transform: 'translateY(-1px)'}}  width={24} height={24} viewBox="0 0 24 24">
                  <defs>
                    <path id="prefix__a" d="M6.373 7.22c.371-.298.901-.294 1.267.012l6 5c.424.354.481.984.128 1.408-.198.238-.482.36-.769.36-.225 0-.452-.076-.639-.232L6.99 9.293l-5.363 4.314c-.43.347-1.059.279-1.406-.152-.347-.43-.278-1.06.152-1.406zm0-7c.371-.298.901-.295 1.267.012l6 5c.425.353.482.983.128 1.408-.198.237-.482.36-.768.36-.226 0-.453-.077-.64-.232L6.989 2.292 1.627 6.607c-.43.346-1.059.278-1.406-.152-.346-.43-.278-1.06.152-1.407z" />
                  </defs>
                  <g fill="none" fillRule="evenodd" transform="translate(5 5)">
                    <use fill="#FFF" xlinkHref="#prefix__a" />
                  </g>
                </svg>
              </span>
              <span style={{display:this.state.menu_bar===true?'block':'none', transform: 'translateY(-1px)'}} id="menu_bar_down">
                <svg id="menu_bar_down_icon"  width={24} height={24} viewBox="0 0 24 24">
                  <defs>
                    <path id="prefix__down" d="M.232 7.36c.353-.424.983-.482 1.408-.128l5.371 4.476 5.362-4.315c.43-.345 1.061-.277 1.406.152.346.43.278 1.06-.152 1.407l-6 4.828c-.183.147-.405.22-.627.22-.228 0-.455-.077-.64-.232l-6-5c-.425-.353-.482-.983-.128-1.408zm0-7c.354-.425.983-.48 1.408-.128l5.37 4.475L12.374.393c.43-.346 1.06-.278 1.406.152.347.43.278 1.06-.152 1.406l-6 4.828C7.444 6.926 7.222 7 7 7c-.227 0-.455-.077-.64-.232l-6-5C-.064 1.414-.121.784.232.36z" />
                  </defs>
                  <g fill="none" fillRule="evenodd" transform="translate(5 5)">
                    <use className="svgicons-reseller" xlinkHref="#prefix__down" />
                  </g>
                </svg>
              </span>
            </button>
            <div id="tools_div" className="tools_div_guest menudrop dropdown-menu show" x-placement="top-start" style={{position: 'absolute', top: '46px', left: '4px'}}>

              <div onClick={()=> this.open_close('share',true)} name="share"  className="menudrop_item dropdown-item" href="#" data-tip="Share">
                <svg  width={24} height={24} viewBox="0 0 24 24">
                  <defs>
                    <path id="prefix__b" d="M16 16c-.552 0-1-.448-1-1s.448-1 1-1 1 .448 1 1-.448 1-1 1M3 10c-.552 0-1-.448-1-1s.448-1 1-1 1 .448 1 1-.448 1-1 1m13-8c.552 0 1 .448 1 1s-.448 1-1 1-1-.448-1-1 .448-1 1-1m0 10c-.817 0-1.557.33-2.099.861L5.966 9.335C5.979 9.224 6 9.114 6 9c0-.114-.021-.224-.034-.335l7.935-3.526C14.443 5.67 15.183 6 16 6c1.654 0 3-1.346 3-3s-1.346-3-3-3-3 1.346-3 3c0 .***************.335L5.099 6.861C4.557 6.33 3.817 6 3 6 1.346 6 0 7.346 0 9s1.346 3 3 3c.817 0 1.557-.33 2.099-.861l7.935 3.526c-.013.111-.034.221-.034.335 0 1.654 1.346 3 3 3s3-1.346 3-3-1.346-3-3-3" />
                  </defs>
                  <g fill="none" fillRule="evenodd" transform="translate(2 3)">
                    <use className="icon_svg" className="svgicons-reseller" xlinkHref="#prefix__b" />
                  </g>
                </svg>
              </div>
              {/* {!this.state.lock?
             <>
             
              <div onClick={()=> this.open_close('chooseusermodal',true)} data-toggle="tooltip" data-placement="right"  className="menudrop_item dropdown-item" href="#" data-tip="Lock">
              <svg height={24}  width={24}  viewBox="0 0 24 24">
                    <g data-name="Layer 2">
                      <g className="svgicons-reseller" data-name="lock">
                        <rect width="24" height="24" opacity="0"/>
                        <path d="M17 8h-1V6.11a4 4 0 1 0-8 0V8H7a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3v-8a3 3 0 0 0-3-3zm-7-1.89A2.06 2.06 0 0 1 12 4a2.06 2.06 0 0 1 2 2.11V8h-4zM18 19a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1v-8a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1z"/>
                        <path d="M12 12a3 3 0 1 0 3 3 3 3 0 0 0-3-3zm0 4a1 1 0 1 1 1-1 1 1 0 0 1-1 1z"/>
                      </g>
                    </g>
              </svg>
              </div>
              </>
            :
            <>
              
            <div onClick={()=> this.open_close('chooseusermodal',true)} data-toggle="tooltip" data-placement="right" className="menudrop_item dropdown-item" href="#" data-tip="Unlock">
                  <svg height={24}  width={24}   viewBox="0 0 24 24">
                      <g data-name="Layer 2">
                      <g fill="#FF3D71" data-name="unlock">
                          <rect width="24" height="24" opacity="0"/>
                          <path d="M17 8h-7V6a2 2 0 0 1 4 0 1 1 0 0 0 2 0 4 4 0 0 0-8 0v2H7a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3v-8a3 3 0 0 0-3-3zm1 11a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1v-8a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1z"/>
                          <path d="M12 12a3 3 0 1 0 3 3 3 3 0 0 0-3-3zm0 4a1 1 0 1 1 1-1 1 1 0 0 1-1 1z"/>
                      </g></g>
                    </svg>
              </div>
            
</>

            } */}
              {/*  */}
             
              {/* <a onClick={()=> this.open_close('settings',true)} data-toggle="tooltip" data-placement="right" title="" className="menudrop_item dropdown-item" href="#" data-tip="Settings">
                <svg  width={24} height={24} viewBox="0 0 24 24">
                  <defs>
                    <path id="prefix__g" d="M2.403 9.622c1.116.383 2.005 1.317 2.377 2.501l.04.12c.426 1.256.253 2.608-.461 3.622-.13.184-.101.404.036.508l2.072 1.574c.***************.188.05.05-.008.123-.035.182-.119l.23-.328c.69-.977 1.8-1.58 2.972-1.614 1.316-.027 2.498.576 3.234 1.64l.118.17c.**************.*************.116.006.188-.05l2.06-1.555c.145-.108.177-.339.07-.494l-.26-.375c-.67-.968-.87-2.224-.532-3.359.366-1.236 1.297-2.214 2.492-2.614l.2-.068c.162-.053.249-.253.192-.437l-.787-2.52c-.037-.119-.113-.172-.155-.194-.06-.03-.125-.036-.187-.015l-.34.113c-1.163.387-2.446.177-3.431-.564l-.108-.08c-.936-.705-1.493-1.84-1.49-3.036l.003-.28c0-.133-.063-.216-.101-.254-.036-.037-.097-.08-.183-.08L8.657 2c-.156 0-.283.15-.284.333l-.001.242C8.367 3.79 7.798 4.946 6.85 5.67l-.13.098c-1.042.793-2.402 1.017-3.634.597-.047-.016-.091-.013-.133.01-.032.015-.09.056-.118.147l-.817 2.596c-.06.19.038.386.22.45l.165.055zM6.613 20c-.485 0-.957-.158-1.355-.46l-2.072-1.574c-.99-.75-1.21-2.193-.49-3.216.375-.53.452-1.21.232-1.857l-.055-.168c-.183-.582-.601-1.034-1.118-1.21h-.001l-.163-.058C.373 11.04-.277 9.75.11 8.517l.816-2.595c.185-.587.584-1.06 1.124-1.334.528-.266 1.125-.307 1.683-.116.599.204 1.264.093 1.777-.297l.129-.098c.456-.348.73-.913.733-1.51v-.24C6.379 1.041 7.403 0 8.657 0h.004l2.547.003c.602.001 1.17.24 1.598.67.443.445.685 1.04.683 1.675l-.002.28c-.002.565.257 1.1.694 1.428l.107.081c.459.345 1.057.444 1.594.264l.339-.113c.577-.192 1.19-.145 1.732.132.555.284.965.773 1.153 1.378l.787 2.521c.38 1.218-.278 2.532-1.465 2.93l-.201.066c-.576.194-1.03.674-1.21 1.286-.166.561-.07 1.178.259 1.652l.26.375c.714 1.032.486 2.48-.508 3.23l-2.061 1.555c-.495.374-1.102.525-1.711.428-.614-.099-1.15-.439-1.51-.957l-.117-.172c-.35-.504-.91-.81-1.497-.777-.588.016-1.096.295-1.428.767l-.231.328c-.362.513-.9.848-1.51.944-.117.018-.233.026-.348.026zM10 8.5c-.827 0-1.5.673-1.5 1.5s.673 1.5 1.5 1.5 1.5-.673 1.5-1.5-.673-1.5-1.5-1.5m0 5c-1.93 0-3.5-1.57-3.5-3.5S8.07 6.5 10 6.5s3.5 1.57 3.5 3.5-1.57 3.5-3.5 3.5" />
                  </defs>
                  <g fill="none" fillRule="evenodd" transform="translate(2 2)">
                    <use className="icon_svg" fill="#36F" xlinkHref="#prefix__g" />
                  </g>
                </svg>
              </a>
               */}

            </div>
          </div>

          {/* <OwlCarousel responsiveClass={true} autoWidth={true}   margin={10} navText={nav} dots={false} responsive={res} nav={true} id="MultiCarousel" className="MultiCarousel owl-carousel">
            {this.rooms}
        </OwlCarousel> */}
 
        
 
        </div>

     
        
    <Share open_close={this.open_close} share={this.state.share} pid={this.state.pid} roomId={this.props.roomId} user_id={this.props.user_id} ></Share>
    {this.state.chooseusermodal?<>
      <div className="modal" style={{display:'block'}} id="project_modal" tabIndex="-1" role="dialog">
    <div className="modal-dialog" role="document">
      <div className="modal-content">
        <div className="modal-header">
          <h5 style={{color: "#222b45"}} className="modal-title">Manage Access</h5>
          <button  onClick={() => this.open_close('chooseusermodal',false)} type="button" className="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">
                <svg width="24" height="24" viewBox="0 0 24 24">
                <defs>
                    <path id="prefix__close" d="M7.414 6l4.293-4.293c.391-.391.391-1.023 0-1.414-.39-.391-1.023-.391-1.414 0L6 4.586 1.707.293C1.317-.098.684-.098.293.293c-.39.391-.39 1.023 0 1.414L4.586 6 .293 10.293c-.39.391-.39 1.023 0 1.414.195.195.451.293.707.293.256 0 .512-.098.707-.293L6 7.414l4.293 4.293c.195.195.451.293.707.293.256 0 .512-.098.707-.293.391-.391.391-1.023 0-1.414L7.414 6z"/>
                </defs>
                <g fill="none" fillRule="evenodd" transform="translate(6 6)">
                    <use fill="#222B45" href="#prefix__close"/>
                </g>
            </svg></span>
          </button>
        </div>
        <form onSubmit={this.lockUser}>
        <div className="modal-body">
          <p className="share_content">Select the user to give access</p>
          <div className="switch_project_list">
          { Object.keys(this.props.Peers).map((value,index)=>{
               return( <>
                 <div key={index} className="form-check form-check-radio">
              <label className="switch_project_label form-check-label">
                  <input onChange={this.handleChange} className="form-check-input" type="radio" name="members"  value={value} />
                        {this.props.Peers[value].extra.username}
                  <span className="circle">
                      <span className="check"></span>
                  </span>
              </label>
          </div>
           <hr />  
               </>)
             
          })}
          </div>
        </div>
        <div style={{display: "block"}} className="modal-footer">
            <center className="modal_button_div">
                <button onClick={() => this.open_close('chooseusermodal',false)} type="button" className="btn cancel">Cancel</button>
                <button style={{marginLeft: "20px"}} type="submit" className="btn proceed">Give Access</button>
                <button style={{marginLeft: "20px"}} onClick={this.revoke} type="button" className="btn proceed">Revoke Access</button>
            </center>
           
        </div>
        </form>
      </div>
    </div>
    </div>
    </>:<></>}

  </>


)}
}
const mapStateToProps = state => {
  return {
    Peers: state.Call.peers,
    Video:state.Call.Video,
    Audio:state.Call.Audio
  }
}

const mapDispatchToProps = {
  ...HostActions,
  ...ExceptionActions
}

export default connect(mapStateToProps, mapDispatchToProps)(SceneControls)
