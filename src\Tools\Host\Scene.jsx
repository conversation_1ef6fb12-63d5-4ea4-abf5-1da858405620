import React from 'react';
import {connect} from "react-redux"
import AssestsLoader from "./AssetsLoader";
import Hotspots from "./Hotspots";
import {Resolvepath} from "../../components/Resolvepath"
import InfoEntity from './InfoEntity';
import Inventory from './Inventory';
// import LoadingScreen from "react-loading-screen";
const $ = window.jQuery;
// import Fire from "../../config/Firebase.jsx";

class Scene extends React.Component {

    constructor(props)
    {
      super(props);
      this.state = {
        loaded:false,
        VRMode:false,
        imageload:true,
        highimages:[],
        loadedimage:[],
        loadedlowimage:[]
    };
    this.time = null;
    this.total = 0;
    this.assets = [];
    this.clientAssets = [];
    this.imageloaded=this.imageloaded.bind(this);
    this.sendrotation=this.sendrotation.bind(this);
  this.sethighimages=this.sethighimages.bind(this);
  this.loadedhighimage=this.loadedhighimage.bind(this);
  this.loadedlowImage=this.loadedlowImage.bind(this);
  $("#Propvr_Embed").bind('resize', function(){
    window.dispatchEvent(new Event('resize'));
    let aScene = document.querySelector('a-scene');
aScene.camera.aspect = aScene.clientWidth/aScene.clientHeight;
aScene.camera.updateProjectionMatrix();
  });
}
loadedhighimage(image){
  this.setState({
    loadedimage:[...this.state.loadedimage,image]
  })
}
loadedlowImage(image){
  this.setState({
    loadedlowimage:[...this.state.loadedlowimage,image]
  })
}
sethighimages(id){
  this.setState({
    highimages:[...this.state.highimages,id]
  })
}
sendrotation(){
 if(this.props.lock){


    var cameraEl =document.getElementById('cam1');
   const data = {
    actiontype:"lock",
   lockmode:true,
  rotation:cameraEl.getAttribute("rotation")
  };

  this.props.senddata(data)

}
}
SetInventoryInfo(InventoryUnit){

}

imageloaded(){
  this.setState({
    imageload:false
  })

}


componentDidMount(){

  // $('#room').css({'position':'relative','height':'100%','width':'100%'});
  // $('#room').attr('embedded',false);
  this.sethighimages(this.props.image)

}
    shouldComponentUpdate(nextProps,nextState){
      if(nextProps.image!=this.props.image){
        if(!this.state.highimages.includes(nextProps.image)){
          this.sethighimages(nextProps.image)

        }
      return true;
      }
      else{
        return true;
      }
    }
  render()
    {
    return (
      <>
         <a-scene embedded="" id="room" loading-screen="enabled:false"  vr-mode-ui="enabled:true;enterVRButton: #myEnterVRButton"  device-orientation-permission-ui="enabled: false"  style={{position:"relative",display:this.props.platform?"block":"none"}}  >

            <AssestsLoader loadedlisten={this.loadedlowImage} hotspotIcon={this.props.hotspotIcon} sceneloader={this.props.loader} data = {this.props.data}/>

            {this.props.brand?
         this.props.brand.visible?   <a-entity id="BrandCircle" geometry="primitive:circle; radius: 1.5; " material="shader:flat;src:#brandimage" position="0 -2.75 0" rotation="-90 0 0"></a-entity>
        :<></> :<></> }
          {this.props.brand?
           this.props.brand.visible? <img id="brandimage" crossOrigin="anonymous" src={Resolvepath(this.props.brand.url)}></img>:<></>
         :<></> }
<a-assets id="highimages">
{this.state.highimages.map(image=>{

  return(
     <img crossOrigin="anonymous" id={image}  onLoad={()=>{this.loadedhighimage(image)}} src={Resolvepath(this.props.data[image].url)} alt={this.props.data[image].name} key={image}/>

  )
})}
</a-assets>
<a-assets>
<img id="infoimage" crossOrigin="anonymous" src="https://storagecdn.propvr.tech/assets%2Finfohotspot.svg" defscale="0.24 0.24"></img>
<img id="Highlighter" crossOrigin="anonymous" src={"https://cdn.propvr.tech/images/Highlighter.png"} defscale="0.24 0.24"></img>
<img id="inventory_icon" crossorigin="anonymous" defscale="0.05 0.05" src="https://storagecdn.propvr.tech/iHwPWJvWDQYW6ilIAfNfgupytcb2%2Ficons%2F1628861761370-inv3.png?alt=media&amp;token=bbcf5c21-ec82-4e93-a0dd-b369f1bf2c60"></img>
<img id="inventory_sold_icon" crossorigin="anonymous" defscale="0.05 0.05" src="https://storagecdn.propvr.tech/iHwPWJvWDQYW6ilIAfNfgupytcb2%2Ficons%2F1643091249263-1628861761370-inv_RED.png?alt=media"/>
<img id="inventory_hold_icon" crossorigin="anonymous" defscale="0.05 0.05" src="https://storagecdn.propvr.tech/iHwPWJvWDQYW6ilIAfNfgupytcb2%2Ficons%2F1643091515127-1628861761370-Yellow%20(1).png?alt=media"/>
</a-assets>
{this.props.data[this.props.image].info!=undefined?
<InfoEntity SetInfo={this.props.SetInfo} change={this.props.change} GetImageId={this.props.GetImageId} Info={this.props.data[this.props.image].info}/>:""}
<a-entity>

<Inventory SetInfo={this.props.SetInfo} CurrentImage={this.props.image} Inventory={this.props.Inventory}/>

</a-entity>
          <a-entity id="annotate-holder" position="0 0 0"></a-entity>
        <Hotspots
          data={this.props.data}
          image={this.props.image}
          change={this.props.change}
          hotspotIcon={this.props.hotspotIcon}>
        </Hotspots>
            {/* Loads Mouse */}
         <RenderGuest/>
            <a-camera
              id="cam1"
              rotation="0 0 0"
              cursor="rayOrigin: mouse; fuse: false;"
              rotation-reader
              modlook-controls=""
              listen-camera=""
              stereocam="eye:left" cursor-visible="false"
            >
             <a-entity position="0 0 -5" radius="1.25" color="#EF2D5E" id="MYVIEW"></a-entity>

            </a-camera>
            {this.props.ProjectSettings!=undefined && this.props.ProjectSettings.stereotype!==undefined && this.props.ProjectSettings.stereotype===1?  <a-entity id="asd" rotation="0 0 0" overunder={this.props.data[this.props.image].url}></a-entity>:
            <Sky sendrotation={this.sendrotation}  image={this.state.loadedimage.includes(this.props.image)?this.props.image:this.props.image+"__thumb"}/>}

            {/* {this.props.lock?
            <a-camera id="cam1" rotation="0 0 0"  cursor="rayOrigin: mouse; fuse: false;" rotation-reader ></a-camera>
            :<a-camera id="cam1" rotation="0 0 0"  cursor="rayOrigin: mouse; fuse: false;"  ></a-camera>
    } */}
    {!(this.state.loadedimage.includes(this.props.image) || this.state.loadedlowimage.includes(this.props.image))?

            <div className="imageswitch" style={{backgroundColor:'black'}}>
              <div className="switch_project_loader2">
                <span>
                  <svg className="switch_project_svg" width="20" height="20" viewBox="0 0 20 20">
                    <path className="svgicons-reseller" fillRule="evenodd" d="M6.659.473L7.977 0l.947 2.637-1.319.473C4.748 4.134 2.802 6.854 2.802 9.943c0 4.007 3.246 7.256 7.248 7.256 3.103 0 5.833-1.971 6.842-4.856l.463-1.322 2.645.925-.463 1.322c-1.4 4-5.183 6.732-9.487 6.732C4.5 20 0 15.497 0 9.943c0-4.28 2.697-8.049 6.659-9.47z"></path>
                  </svg>
                </span>

                <span style={{ paddingLeft: "16px", marginTop: "-25px" ,color:'white'}}>
                  Loading image...
                </span>
              </div>
            </div>

    :<></>}
        </a-scene>
       <div
       id="myEnterVRButton"
       className="m-075 cursor-pointer  align-items-center justify-content-center position-fixed "
       style={{display:"none"}}

     >
       <svg height={24} width={24} fill="#fff" viewBox="0 0 32 32" role="img">
         <path
           d="M8 5C6.172 5 4.996 6.074 4.5 7.063c-.2.394-.305.769-.375 1.093C2.906 8.54 2 9.664 2 11v13c0 1.645 1.355 3 3 3h7c1.32 0 2.52-.797 3.063-2h1.874c.54 1.203 1.743 2 3.063 2h7c1.645 0 3-1.355 3-3V11c0-1.336-.906-2.46-2.125-2.844c-.07-.324-.176-.699-.375-1.094C27.004 6.079 25.82 5 24 5zm0 2h16c1.148 0 1.457.422 1.719.938L25.75 8H6.25l.031-.063C6.54 7.426 6.84 7 8 7zm-3 3h22c.566 0 1 .434 1 1v13c0 .566-.434 1-1 1h-7a1.361 1.361 0 0 1-1.25-.844l-1.031-2.75v-.031l-.032-.031a1.871 1.871 0 0 0-1.718-1.157a1.91 1.91 0 0 0-1.75 1.157l-.031.031v.063l-.938 2.718A1.365 1.365 0 0 1 12 25H5c-.566 0-1-.434-1-1V11c0-.566.434-1 1-1zm5 3c-2.2 0-4 1.8-4 4s1.8 4 4 4s4-1.8 4-4s-1.8-4-4-4zm12 0c-2.2 0-4 1.8-4 4s1.8 4 4 4s4-1.8 4-4s-1.8-4-4-4zm-12 2c1.117 0 2 .883 2 2s-.883 2-2 2s-2-.883-2-2s.883-2 2-2zm12 0c1.117 0 2 .883 2 2s-.883 2-2 2s-2-.883-2-2s.883-2 2-2zm-6.031 7.438l.219.562h-.407z"
           fill="#fff"
         />
       </svg>
     </div>
      </>
    );

    }
}

export default Scene;


class Sky extends React.Component{
  constructor(props){
    super(props);
  }


  render(){
    return(<>
      <a-sky id="sky"  src= {"#"+this.props.image} />
      <a-sky id="skyannotate" material="opacity:0; blending:subtractive;" visible="false" geometry="radius:400;" class="hotspot" annotate-listen onMouseUp={this.props.sendrotation}  cursor="rayOrigin: mouse; fuse: false;" />
</>
    )
  }
}
const Avatarsrc=[
"https://cdn.glitch.me/a04a26d3-92af-4a88-9d49-8fcc2c5344a5%2Favatar1.png",
"https://cdn.glitch.me/a04a26d3-92af-4a88-9d49-8fcc2c5344a5%2Favatar2.png",
"https://cdn.glitch.me/a04a26d3-92af-4a88-9d49-8fcc2c5344a5%2Favatar3.png",
"https://cdn.glitch.me/a04a26d3-92af-4a88-9d49-8fcc2c5344a5%2Favatar4.png",
"https://cdn.glitch.me/a04a26d3-92af-4a88-9d49-8fcc2c5344a5%2Favatar5.png",
"https://cdn.glitch.me/a04a26d3-92af-4a88-9d49-8fcc2c5344a5%2Favatar6.png",
]
class Guests extends React.Component{

  constructor(props){
    super(props);
  }
  render(){
return(<>
{this.props.Peers && Object.keys(this.props.Peers).length<10 &&
                  Object.values(this.props.Peers).map((peer) => { if (peer != null) {
                     return (
                       <>{this.props.userVideoAudio[peer.peerID] && Object.keys(this.props.userVideoAudio[peer.peerID]).length<10?
                      <a-entity position="0 0 -5" id={peer.peerID+"_VIDEOSPHERE"} >
                     <a-entity    look-at="#cam1" position="0 0 0"    rotation="0 0 0" visible="true">
{this.props.userVideoAudio[peer.peerID].video?
                     <a-videosphere   src={"#"+peer.peerID+"VIDEO"} width="1.3"  geometry="primitive: circle;radius:0.5;" rotation="0 -180 0" height="0.75" position="0 0.59 -0.301">

                      <a-circle color="#2979FF" position="0 0 0.001" rotation="0 -180 0" radius="0.55"></a-circle>
                     </a-videosphere>:      <a-sky src={peer.extra.Avatar || Avatarsrc[Math.floor(Math.random() * 5)]} width="1.3"  geometry="primitive: circle;radius:0.5;" rotation="0 -180 0" height="0.75" position="0 0.59 -0.301"></a-sky>
}<a-entity  htmlembed="styleSheetId: 3dcards" position="0 -0.2 -0.301" scale="3 3 3">
  <div class="EmbedNameDiv">
    <div class="EmbedName">{peer.userName}</div>
  </div>
</a-entity>

                     </a-entity>
                     </a-entity>:""}</>)
                     }
                      })}
                  </>)
  }
}
const mapStateToProps=(state)=>{
  return{
    Peers: state.Call.peers,
    userVideoAudio:state.Call.userVideoAudio,

  }
}
const RenderGuest=connect(mapStateToProps,null)(Guests);