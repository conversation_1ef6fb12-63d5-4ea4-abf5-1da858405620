//server.js
const express = require('express');
const path = require('path');
require('dotenv').config();
//require('dotenv').config({ path: `./.env.${process.env.NODE_ENV}` })
require('dotenv').config({ path: path.join(__dirname, `./.env`)});
const port = process.env.REACT_APP_API_PORT || 5001;


const app = express();
// const FirebaseApp= require("firebase/app")
// require("firebase/database")
// const config = {
//   apiKey: process.env.REACT_APP_API_FIREBASE_API_KEY,
//   authDomain: process.env.REACT_APP_API_FIREBASE_AUTH_DOMAIN,
//   databaseURL: process.env.REACT_APP_API_FIREBASE_DATABASE_URL,
//   projectId: process.env.REACT_APP_API_FIREBASE_PROJECT_ID,
//   storageBucket: process.env.REACT_APP_API_FIREBASE_STORAGE_BUCKET,
//   messagingSenderId: process.env.REACT_APP_API_FIREBASE_MESSAGINGSENDER_ID,
//   appId: process.env.REACT_APP_API_FIREBASE_APP_ID


 
// };
// const Firebase=FirebaseApp.initializeApp(config);
var Reseller

// the __dirname is the current directory from where the script is running
app.use(express.static(__dirname));
app.use(express.static(path.join(__dirname, 'salestool')));
app.engine('html', require('ejs').renderFile);
app.set('view engine', 'html');

app.get('/salestool/pdf', (req, res) => {
  res.sendFile(path.join(__dirname, 'salestool', 'pdf/pdf.html'));
});
app.get('/salestool/service-worker.js', (req, res) => {
  res.sendFile(path.join(__dirname, 'salestool', 'service-worker.js'));
});
app.get('/salestool/firebase-messaging-sw.js', (req, res) => {
  res.sendFile(path.join(__dirname, 'salestool', 'firebase-messaging-sw.js'));
});
app.get('/salestool/*', (req, res) => {
  var data;
  var domain =req.get('host').replace(/\./g,"dot");
  domain=domain.replace(":","");
  res.render(path.join(__dirname, 'salestool', 'index'));

  // domain="viewerdotvirtualrunwaydotin";
  // console.log(domain)
  // if(Reseller && Reseller[domain]){
  //   data={
  //     image:Resolvepath(Reseller[domain].Logo),
  //     color:Reseller[domain].ColorCode,
  //     hovercolor:Reseller[domain].ColorCode+"e8",
  //     name:Reseller[domain].BrandName,
  //     whiteimage:Resolvepath(Reseller[domain].WhiteLogo),
  //     favicon:Resolvepath(Reseller[domain].Logo),
  //     title:Reseller[domain].BrandName,
  //     ResellerBgImage:Reseller[domain].ResellerBgImage || "https://storagecdn.propvr.tech/assets/loginBG.jpg",
  //     ResellerHeader:Reseller[domain].ResellerHeader || "Create, Collaborate and Share",
  //     ResellerText:Reseller[domain].ResellerText || "Prop VR allows you to create, collaborate and share virtual tour experience of your properties with your customers. Take your customers for a virtual-guided tour and increase sales.",
  //   };
    
  // }else{
  //   data={
  //     image:"https://cdn.propvr.tech/images/logo/logo_propvr_vertical.png",
  //     color:"#3366ff",
  //     hovercolor:"#598BFF",
  //     name:"Prop VR",
  //     whiteimage:"https://cdn.propvr.tech/images/logo/logo_propvr_verticalwhite.png",
  //     title:"Prop VR",
  //     favicon:"https://cdn.propvr.tech/images/logo/logo_propvr_iconwhite.png",
  //     ResellerBgImage:"https://storagecdn.propvr.tech/assets/loginBG.jpg",
  //     ResellerHeader:"Create, Collaborate and Share",
  //     ResellerText:"Prop VR allows you to create, collaborate and share virtual tour experience of your properties with your customers. Take your customers for a virtual-guided tour and increase sales.",
      
  //   }
    
  //   console.log(data)
  // }
  //   res.render(path.join(__dirname, 'salestool', 'index'));

});

app.listen(port, () => {
});

// 
function Resolvepath(path){
  
  if(path!=undefined && path.includes("firebasestorage.googleapis.com/v0/b/realvr-eb62c.appspot.com/o")){
      path = path.substring(0, path.lastIndexOf('?alt'));
    path=path.replace('firebasestorage.googleapis.com/v0/b/realvr-eb62c.appspot.com/o','storagecdn.propvr.tech');
      return path;
  }else{
    return path;
  }
      
}
