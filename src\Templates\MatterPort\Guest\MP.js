import React from "react"
import { connect } from "react-redux"

class MP extends React.Component {
  constructor(props) {
    super(props);
    this.onShowcaseConnect = this.onShowcaseConnect.bind(this);
  }

  async componentDidMount() {
    try {
      const iframe = document.getElementById('showcase-iframe');
      this.mpSdk = await window.MP_SDK.connect(
        iframe, // Obtained earlier
        '73q6cfdu77cnqiw8q2tpfws7d', // Your SDK key
        '' // Unused but needs to be a valid string
      );
      this.onShowcaseConnect();
    } catch (e) {
      console.error(e);
    }
  }
  onShowcaseConnect() {
    this.props.SubscribeToCustom((msg) => {
      var data = JSON.parse(msg);


      if (data.actiontype == "Change_Mode") {
        this.mpSdk.Mode.moveTo(data.data, {
        })
          .then(function (nextMode) {
            // Move successful.
          })
          .catch(function (error) {
            // Error with moveTo command
          });
      }
      if (data.actiontype == "Change_Rotation") {
        this.mpSdk.Camera.getPose().then(pose=>{
          this.mpSdk.Mode.moveTo(pose.mode, {
            position: data.data.position,
          })
          .then(function(nextMode){
            // Move successful.
          })
          .catch(function(error){
            // Error with moveTo command
          });
        })
        
        this.mpSdk.Camera.setRotation({ x: data.data.rotation.x, y: data.data.rotation.y })
          .then(function () {
            // Camera rotation complete.
          })
          .catch(function (error) {
            // Camera rotation error.
          });

      }
      if(data.actiontype=="Change_Floor"){
        this.mpSdk.Floor.moveTo(data.data)
  .then(function(floorIndex) {
    // Move to floor complete.
  })
  .catch(function(error) {
    // Error moving to floor.
  });
      }

      if (data.actiontype == "Change_Sweep") {
        const transition = this.mpSdk.Sweep.Transition.FLY;
        const transitionTime = 2000; // in milliseconds

        this.mpSdk.Sweep.moveTo(data.data.sid, {
          rotation: data.data.rotation,
          transition: transition,
          transitionTime: transitionTime,
        })
          .then(function (sweepId) {
            // Move successful.
          })
          .catch(function (error) {
            // Error with moveTo command
          });
      }

    })
  }
  render() {
    return (
      <>
        <iframe
          style={{ width: "100%", height: '100%',position:"absolute" }}
          src={"https://my.matterport.com/show?m=" + this.props.pid + "&play=1&applicationKey=73q6cfdu77cnqiw8q2tpfws7d"}
          frameborder="0"
          allow="fullscreen; vr"
          id="showcase-iframe"></iframe>
          <div style={{position:"absolute",width:"100%",height:"100%"}}></div>
          
          </>
    )
  }
}

export default MP;