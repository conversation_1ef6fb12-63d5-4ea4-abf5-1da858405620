import React from "react";
// import { Redirect, Route, Link } from "react-router-dom";
// import Fire from "../../config/Firebase.jsx";

// import Select from 'react-select';
import {Resolvepath} from "../../components/Resolvepath"
class Flooplanclient extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      screenHeight: window.innerHeight,
      isLandscape: window.innerWidth > window.innerHeight,
    }

  }
  updateScreenHeight = () => {
    this.setState({
      screenHeight: window.innerHeight
    });
  };

  handleOrientationChange = () => {
    const isLandscape = window.innerWidth > window.innerHeight;
    this.setState({ isLandscape });
  };
  componentDidMount() {
    // Add an event listener to update the screen height on window resize
    window.addEventListener('resize', this.updateScreenHeight);
    window.addEventListener('resize', this.handleOrientationChange);
  }


  render() {
    
var position=[];
  if(this.props.data.pin){
position=this.props.data.pin.split("-");
  }
      return(  
        
      
      <div  className="bg-[black]/80 bg-opacity-80 fixed z-[10501] overflow-hidden w-full h-screen inset-0 h-full w-full top-0" id="pdf_modal" tabIndex="-1" role="dialog">
            <div className="w-full h-full max-w-full m-0 p-0 flex justify-center items-center" role="document">
            <div className={` ${!this.state.isLandscape?"h-fit w-[95%] rounded":"h-full w-full "} sm:h-fit sm:max-w-lg lg:max-w-xl  sm:w-[95%] flex flex-col w-full pointer-events-auto bg-transparent backdrop-blur-2xl bg-clip-padding m-auto rounded-none sm:rounded-md  border-[none] inset-0`}>
                <div className="w-full h-full sm:p-1 box-border flex flex-col" >
                  <div className="h-12 sm:h-16 p-2 sm:px-0 w-full flex justify-between items-center">
                    <div className="w-fit sm:px-3 py-1 sm:bg-[#525252] text-sm text-white rounded-sm font-medium leading-normal">
                    {this.props.data!==false?   
                  this.props.data.name
                           :""}
                    </div>
                   
                  </div>
                  <div className="w-full h-full pt-1 sm:px-4 sm:mb-2 grow rounded">
                    <div className=" h-full rounded-t sm:rounded-b bg-white flex justify-center items-center ">
                      <img alt="Floor" src={Resolvepath(this.props.data.url)}  className={`max-w-full  ${this.state.isLandscape?"h-full w-auto":"h-auto w-full "} sm:h-auto  sm:w-full rounded-[inherit]`} />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* </div> */}
          </div>

        
        
      )
  }
}
export default Flooplanclient;
