import React from "react";
import demoImg from '../../assets/img/bg2.jpg'
import './DocumentModal.css'
// import { Redirect, Route, Link } from "react-router-dom";

import Pdfviewer from "./Pdfviewer";

class DocumentModal extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      pdf_modal: false,
      pdf_data: '',
      pdf_list: [],
      pdf_temp_data: '',
      selectedPdf:'',
      screenHeight: window.innerHeight
    }
    this.handleregister = this.handleregister.bind(this);

  }

  updateScreenHeight = () => {
    this.setState({
      screenHeight: window.innerHeight
    });
  };

  componentDidMount() {
    // Add an event listener to update the screen height on window resize
    window.addEventListener('resize', this.updateScreenHeight);
  }

  componentWillUnmount() {
    // Remove the event listener when the component is unmounted to avoid memory leaks
    window.removeEventListener('resize', this.updateScreenHeight);
  }

  componentDidMount() {

    window.scrollTo(0, 0);

    if (this.props.data.hasOwnProperty('brochure')) {
      var key = Object.keys(this.props.data.brochure);
      var list = Object.values(this.props.data.brochure);
      var temp = [];
      for (var i = 0; i < key.length; i++) {
        temp.push({
          id: key[i],
          name: list[i].name,
          url: list[i].url
        })

        if(i===0){
          this.state.pdf_data = list[0].url
        }
      }

      this.setState({
        pdf_list: temp
      })
    }

    // if(this.state.pdf_list.length){
    //   this.state.pdf_data===this.state.pdf_list[0].url
    // }

  }

  open_close_pdf = (name, flag) => {
    if (flag === true) {
      this.props.open_close('document', false);
      this.setState({
        pdf_modal: true,
        pdf_data: this.state.pdf_temp_data
      })
    }
    else {
      this.props.senddata({ actiontype: "pdf", roomid: this.props.room, data: "false" });
    }
    this.setState({
      [name]: flag
    })
  }


  handleChange = (elm) => {
    // document.getElementById('view_doc').disabled = false;
    this.setState({
      pdf_data: elm,
      pdf_temp_data: elm
    })
  };

  handleregister() {
    console.log('luck');
    // event.preventDefault();
    this.open_close_pdf('pdf_modal', true);
    this.props.senddata({ actiontype: "pdf", roomid: this.props.room, data: this.state.pdf_data });
  }

  handleRadioChange = (val) => {
    // const selectedValue = parseInt(event.target.value);
    this.setState({ selectedPdf: val });
  };
  render() {
    if (!this.state.pdf_list.length) {
      return (<div style={{ display: this.props.document === true ? 'block' : 'none' }} className="modal">
        <div className="modal-dialog" role="document">
          <div className="modal-content">
            <div className="modal-header" >
              <h5 style={{ color: "#222b45", margin: '0px' }} className="modal-title">Brochure does not exist!</h5>
              <button onClick={() => this.props.open_close('document', false)} type="button" className="close" data-dismiss="modal" aria-label="Close">


                <span aria-hidden="true">
                  <svg width="24" height="24" viewBox="0 0 24 24">
                    <defs>
                      <path id="prefix__close" d="M7.414 6l4.293-4.293c.391-.391.391-1.023 0-1.414-.39-.391-1.023-.391-1.414 0L6 4.586 1.707.293C1.317-.098.684-.098.293.293c-.39.391-.39 1.023 0 1.414L4.586 6 .293 10.293c-.39.391-.39 1.023 0 1.414.195.195.451.293.707.293.256 0 .512-.098.707-.293L6 7.414l4.293 4.293c.195.195.451.293.707.293.256 0 .512-.098.707-.293.391-.391.391-1.023 0-1.414L7.414 6z" />
                    </defs>
                    <g fill="none" fillRule="evenodd" transform="translate(6 6)">
                      <use fill="#222B45" href="#prefix__close" />
                    </g>
                  </svg></span>
              </button>
            </div>
            <div className="modal-body">
              <p className="share_content">Brouchure are not available for this project</p>
            </div>
            <div style={{ display: "block" }} className="modal-footer">
              <center style={{ display: "flex", justifyContent: "center" }}>
                <button onClick={() => this.props.open_close('document', false)} type="button" className="btn cancel">Close</button>
              </center>
            </div>
          </div>
        </div>
      </div>)
    } else {
      return (
        <>
          <div style={{ display: this.props.document === true ? 'block' : 'none' }} className="bg-[black]/80 bg-opacity-80 fixed z-[10501] overflow-hidden w-full h-full inset-0 w-full h-full top-0" id="close_modal" tabIndex="-1" role="dialog">
            <div className={` inset-0 mx-auto py-4 max-w-md   px-3 transition-all ${this.state.screenHeight<485 ? "":"sm:max-w-xl lg:max-w-2xl"} h-full w-full flex justify-center items-center`} role="document">
              <div className=" h-fit flex flex-col w-full pointer-events-auto bg-transparent backdrop-blur-2xl bg-clip-padding m-auto rounded-md rounded-none border-[none] inset-0">
                <div className="flex justify-between items-center mt-3  mx-3 ">
                  <div className="flex flex-col ">
                    <h1 className="text-white text-sm font-bold mt-0 mb-1">Brochure</h1>
                    <p className=" text-white text-xs font-normal leading-normal mt-0 mb-0">Select the brochure document you would like to share.</p>
                  </div>
                  {/* Search Bar and Icon */}
                  {/* <div className="hidden sm:inline-flex w-fit h-8 px-3 py-2 rounded border border-white justify-start items-center gap-28 ">
                    <input className="text-neutral-400 text-xs placeholder:text-xs font-normal leading-normal bg-transparent" type="text" placeholder="Search for brochure" />
                  </div>
                  <div className="block sm:hidden bg-black bg-opacity-10 h-8 w-8 p-2 rounded-full flex justify-center items-center">
                  <svg className="w-6 h-6 fill-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="search"><rect  opacity="0"/><path d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z"/></g></g></svg>
                  </div> */}
                </div>
                <div className={`pr-1 mr-2 ml-3 h-44  grid grid-cols-3 gap-2 overflow-auto pdfListGrid mt-3 ${this.state.screenHeight<485 ? "":"sm:h-[17rem] lg:h-[19rem]"}`}>
                  {this.state.pdf_list.map((elem,index)=>(
                    <div className="flex flex-col h-fit rounded cursor-pointer" key={index} onClick={()=>this.handleChange(elem.url)}> 
                      <div className={`w-full h-20  flex justify-center items-center text-white rounded-t-[inherit] sm:max-w-xl lg:max-w-2xl ${this.state.screenHeight<485 ? "":"sm:h-24 lg:h-28"}`}>
                        <img src={demoImg} className="h-full w-full object-cover rounded-t-[inherit]"/></div>
                      <div className={`w-full h-7 text-white flex justify-start items-center rounded-b-[inherit] whitespace-nowrap overflow-hidden ${this.state.screenHeight<485 ? "":"sm:h-8 lg:h-9"} ${this.state.pdf_data===elem.url?'bg-[#36f]':'bg-[#525252]'}`}>
                        <div className="mx-2 w-4 h-4 rounded-full cursor-pointer border flex justify-center items-center" >
                          {this.state.pdf_data===elem.url?
                          <div className="w-2 h-2 rounded-full bg-white"></div>
                          :""}
                        </div>
                        <div className="text-xs cursor-pointer flex flex-1 items-center text-ellipsis overflow-hidden mr-2" onClick={()=>this.handleChange(elem.url)}>{elem.name}</div>
                      </div>
                    </div>
                  ))}
                 
                  
                  
                  


                </div>
                <div className={`border-t border-neutral-500 mt-2  ${this.state.screenHeight<485 ? "":"sm:mt-1"} mx-3`}></div>
                <div className={`mx-3 mb-2  block pt-0 pb-1 border-t-[none] ${this.state.screenHeight<485 ? "":"sm:mb-3"}`}>
                <center className={`flex justify-center mt-2  ${this.state.screenHeight<485 ? "":"sm:mt-3"}`}>
                    <button onClick={this.handleregister} id="view_doc" style={{marginLeft: "20px"}} type="button" className="w-fit h-9  bg-[#36f] hover:bg-[#4572fc] border-0 rounded  m-0 px-3 text-xs  text-white font-semibold leading-6">View Brochure</button>
                    <button onClick={() => this.props.open_close('document',false)}  type="button" className="ml-3 w-fit h-9  rounded border-2 border-solid border-[#36f] hover:border-[#4572fc] bg-transparent  m-0 px-3 text-xs text-white font-semibold leading-6" >Cancel</button>
                    
                </center>
               
            </div>

              </div>
            </div>
          </div>
          <Pdfviewer senddata = {this.props.senddata} open_close_pdf={this.open_close_pdf} pdf={this.state.pdf_modal} data={this.state.pdf_data}></Pdfviewer>
        </>

      )
    }
  }
}
export default DocumentModal;
