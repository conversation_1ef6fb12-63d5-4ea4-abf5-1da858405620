const webkitSpeechRecognition =  window.webkitSpeechRecognition;
var recognizer = new webkitSpeechRecognition();
recognizer.lang = "en";
recognizer.continuous = true;
recognizer.interimResults = true;

recognizer.onspeechend = function() {
    // recognition.stop();
}

console.log("Init Transcription")


recognizer.onresult = function(event) {
    if (event.results.length > 0) {
        var result = event.results[event.results.length-1];
        if(result.isFinal) {
        }
    }
};
recognizer.onerror=function(event){
    console.log('Error occurred in recognition: ' + event.error);
}
recognizer.start();