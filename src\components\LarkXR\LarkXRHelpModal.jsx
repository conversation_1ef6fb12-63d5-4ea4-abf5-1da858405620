// src/components/LarkXR/LarkXRHelpModal.jsx
import React, { useState } from 'react';

const LarkXRHelpModal = ({ onClose }) => {
  const [expandedItem, setExpandedItem] = useState(0); // First item expanded by default

  // Help content structure
  const helpContent = [
    {
      title: 'The Caton Solution',
      content: 'The network quality is unstable. Replace the network environment'
    },
    {
      title: 'Application Cannot Start',
      content: 'The number of users entering the application is large. If you still cannot enter the application after a period of time, click the restart button to enter the application again. If still unable to access, please try redrawing the web url to reconnect to the app'
    },
    {
      title: 'How to Collapse/Show Control Ball',
      content: 'Click [Unlock] to unlock the control ball menu. After closing the control ball menu panel for 2 seconds, the control ball will collapse to the side of the screen. Click the control ball to make it pop out and display. If you need to continuously display the control ball, click [Lock] to lock the control ball menu, and the control ball will not collapse to the side of the screen'
    }
  ];

  const toggleExpanded = (index) => {
    setExpandedItem(expandedItem === index ? -1 : index);
  };



  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999999]">
      <div className="bg-white p-2 rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between px-4 py-2">
          <h2 className="text-lg font-semibold text-gray-900">Help</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Accordion Content */}
        <div className="px-4 py-1 overflow-y-auto max-h-[calc(80vh-80px)]">
          <div className="space-y-2">
            {helpContent.map((item, index) => (
              <div key={index} className="border border-gray-200 rounded-lg overflow-hidden">
                <button
                  onClick={() => toggleExpanded(index)}
                  className="w-full flex items-center justify-between p-3 text-left bg-gray-50 hover:bg-gray-100 transition-colors"
                >
                  <span className="text-sm font-medium text-gray-900">{item.title}</span>
                  <svg
                    className={`w-4 h-4 text-gray-500 transition-transform ${
                      expandedItem === index ? 'rotate-180' : ''
                    }`}
                    fill="none"me 
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
                {expandedItem === index && (
                  <div className="p-3 bg-white border-t border-gray-200">
                    <p className="text-sm text-gray-600 leading-relaxed">{item.content}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LarkXRHelpModal;
