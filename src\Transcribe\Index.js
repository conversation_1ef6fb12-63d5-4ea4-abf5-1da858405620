import React from "react"
import Firebase from "../config/Firebase"
class Transcribe extends React.Component{
constructor(props){
    super(props);
    this.state={
        Transcription:{}
    }
    
      
    
}
    render(){
        return(
            <>
            <table class="table">
    <thead>
        <tr>
            <th>Time</th>
            <th>Person</th>
            <th>Confidence</th>
            <th>Transcript</th>
           
        </tr>
    </thead>
    <tbody>
    {Object.values(this.state.Transcription).reverse().map(node=>{
                return(
                    
                
                        <tr>
            <td>{node.time}</td>
            <td>{node.type?node.type:""}</td>
            <td>{node.confidence}</td>
            <td>{node.transcript}</td>
            
        </tr>
                      
                )
            })}
      
    </tbody>
</table>
            <ul>
            
            </ul>
            </>
        )
    }
}
export default Transcribe