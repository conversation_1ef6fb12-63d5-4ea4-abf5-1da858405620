import React from "react";
// import { Redirect, Route, Link } from "react-router-dom";
import Fire from "../../config/Firebase.jsx";


class Switchproject extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      project_list: [],
      project_value: this.props.pid,
      screenHeight: window.innerHeight,
      isLandscape: window.innerWidth > window.innerHeight,
    }
    this.handleregister = this.handleregister.bind(this);

  }

  updateScreenHeight = () => {
    this.setState({
      screenHeight: window.innerHeight
    });
  };

  handleOrientationChange = () => {
    const isLandscape = window.innerWidth > window.innerHeight;
    this.setState({ isLandscape });
  };
  componentDidMount() {
    // Add an event listener to update the screen height on window resize
    window.addEventListener('resize', this.updateScreenHeight);
    window.addEventListener('resize', this.handleOrientationChange);
  }

  componentWillUnmount() {
    // Remove the event listener when the component is unmounted to avoid memory leaks
    window.removeEventListener('resize', this.updateScreenHeight);
    window.removeEventListener('resize', this.handleOrientationChange);
  }
  componentDidMount() {

    window.scrollTo(0, 0);
    var temp = [];

    const userRecord = Fire.auth().currentUser;
    const uid = userRecord ? userRecord.uid : null;
    console.log(uid);

    Fire.auth().currentUser.getIdToken(true).then((idToken) => {
      fetch("https://realvr-eb62c.firebaseio.com/users/" + uid + "/Projects.json?auth=" + idToken + "&shallow=true")
        .then(res => res.json())
        .then((result) => {
          for (let x in result) {
            temp.push({ id: x });
          }
        });
    })

    this.setState({
      project_list: temp
    })

  }

  handleregister(event) {
    this.props.switch()
    event.preventDefault();
    this.setState({ isLoading: true });
    if (this.props.pid !== this.state.project_value) {
      this.props.changeProject(this.state.project_value);
    }
    setTimeout(() => {
      this.setState({ isLoading: false });
    }, 2000);
  }
  handleChange = (event) => {
    this.setState({
      project_value: event.target.id
    })
  };

  render() {

    return (
      <>
        <div className="bg-[black]/70 bg-opacity-80 fixed z-[10501] overflow-hidden w-full h-full inset-0 top-0" style={{ display: this.props.project === true ? 'block' : 'none' }} id="project_modal" tabIndex="-1" role="dialog">
          <div className={` inset-0 mx-auto py-4 max-w-md  mx-3 transition-all ${this.state.screenHeight<485 ? "":"sm:max-w-lg lg:max-w-xl"} w-full h-full flex justify-center items-center`} role="document">
            <div style={{ outline: 'none' }} className="h-fit flex flex-col w-full pointer-events-auto bg-transparent backdrop-blur-2xl bg-clip-padding m-auto rounded-md rounded-none border-[none] inset-0">
              <div className="flex items-start justify-between p-3 rounded-t-[0.3rem]">
                <h5 style={{ color: "#ffffff" }} className="modal-title">Switch Project</h5>
                <button onClick={() => this.props.open_close('project', false)} type="button" className="float-right text-2xl font-medium leading-none text-white rounded-full bg-[#FFFFFF1A] backdrop-blur-[6px] mt-[auto] p-2" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">
                    <svg className="w-3 h-3" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M9.19098 8.00001L15.7534 1.43756C16.0823 1.10869 16.0823 0.5755 15.7534 0.246656C15.4245 -0.0822188 14.8913 -0.0822188 14.5625 0.246656L8.00001 6.8091L1.43756 0.246656C1.10869 -0.0822188 0.5755 -0.0822188 0.246656 0.246656C-0.0821875 0.575532 -0.0822188 1.10872 0.246656 1.43756L6.8091 8.00001L0.246656 14.5624C-0.0822188 14.8913 -0.0822188 15.4245 0.246656 15.7534C0.575532 16.0822 1.10872 16.0822 1.43756 15.7534L8.00001 9.19091L14.5624 15.7534C14.8913 16.0822 15.4245 16.0822 15.7534 15.7534C16.0822 15.4245 16.0822 14.8913 15.7534 14.5624L9.19098 8.00001Z" fill="white" />
                    </svg></span>
                </button>
              </div>
              <form onSubmit={this.handleregister} className="mb-1">
              <div className={`border-t border-neutral-500 mx-3`}></div>
                <div className={`relative px-3 py-1 ${this.state.screenHeight<485 ? "":"sm:px-3 sm:py-3"} `}>
                  <p className="text-white text-sm font-normal leading-normal mt-0 mb-0" >Select the project you want to switch the session.</p>
                  <div className="h-40 sm:h-52  block overflow-y-auto text-[white] mt-4">
                    {this.state.project_list.map((value, index) => {
                      return (<>
                        <div key={index} className="text-[white] relative block pl-5 form-check-radio">
                          <label style={{ color: "white", letterSpacing: '1px', fontFamily: 'Montserrat, sans-serif' }} className="switch_project_label form-check-label ">
                            <input onChange={this.handleChange} className="form-check-input" type="radio" name="projects" id={value.id} value="option1" checked={this.state.project_value === value.id ? true : false} />
                            {value.id}
                            <span className="circle">
                              <span className="check"></span>
                            </span>
                          </label>
                        </div>
                        <hr className={`${this.state.screenHeight < 485 ? "my-1" : "my-3 sm:my-4"} `} />
                      </>)
                    })}
                  </div>
                </div>
                <div className={`border-t border-neutral-500  ${this.state.screenHeight<485 ? "":"sm:mt-1"} mx-3`}></div>
                <div style={{ display: "block" }} className={`flex justify-center py-3 `}>
                  <center className={`flex justify-center`}>
                     <button  type="submit" className="w-fit h-10  bg-[#36f] hover:bg-[#4572fc] border-0 rounded  m-0 px-3 text-sm  text-white font-semibold leading-6 ">Switch project</button>
                     <button onClick={() => this.props.open_close('project', false)} type="button" className="ml-3 w-fit h-10 rounded border-2 border-solid border-[#36f] hover:border-[#4572fc] bg-transparent  m-0 px-3 text-sm text-white font-semibold leading-6">Cancel</button>
                  </center>

                </div>
              </form>
            </div>
          </div>
        </div>
      </>

    )
  }
}
export default Switchproject;