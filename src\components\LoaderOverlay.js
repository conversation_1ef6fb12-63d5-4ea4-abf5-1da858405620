import React from 'react';

const LoaderOverlay = ({ title, message }) => {
  return (
    <div className="absolute left-1/2 transform -translate-x-1/2 top-1/3 z-30 flex items-center justify-center">
      <div className="bg-black/40 backdrop-blur-md p-8 rounded-xl shadow-lg text-center">
        <div className="lds-ring mb-4">
          <div></div>
          <div></div>
          <div></div>
          <div></div>
        </div>
        <h2 className="text-xl font-bold text-white mb-2">{title}</h2>
        <p className="text-sm text-gray-100">{message}</p>
      </div>
      <style jsx>{`
        .lds-ring {
          display: inline-block;
          position: relative;
          width: 80px;
          height: 80px;
        }
        .lds-ring div {
          box-sizing: border-box;
          display: block;
          position: absolute;
          width: 64px;
          height: 64px;
          margin: 8px;
          border: 4px solid #FFFFFF;
          border-radius: 50%;
          animation: lds-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
          border-color: #000000 transparent transparent transparent;
        }
        .lds-ring div:nth-child(1) {
          animation-delay: -0.45s;
        }
        .lds-ring div:nth-child(2) {
          animation-delay: -0.3s;
        }
        .lds-ring div:nth-child(3) {
          animation-delay: -0.15s;
        }
        @keyframes lds-ring {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
      `}</style>
    </div>
  );
};

export default LoaderOverlay;