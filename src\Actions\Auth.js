import Fire from "../config/Firebase"
import { getCookie, setCookie } from "../Tools/helpers/domhelper";
import { PostRequestWithHeaders } from "../Tools/helpers/api";

export function CheckAuth() {
    return dispatch => {
        Fire.auth().onAuthStateChanged((user) => {
            if (user) {
                setCookie("accessToken", user.multiFactor.user.accessToken, 2)
                PostRequestWithHeaders({ url: process.env.REACT_APP_API_BACKEND_API_URL + 'user/GetUserDetails', body: { uid: user.uid } }).then(response => {
                    console.log(response.organization_id[0])
                    if (!getCookie("organization")) {
                        setCookie("organization", response.organization_id[0])
                    }

                    // Find and store organization thumbnail if available
                    let orgThumbnail = null;
                    if (response.organization_id && response.organization_id.length > 0) {
                        const orgWithThumbnail = response.organization_id.find(org => org.thumbnail);
                        if (orgWithThumbnail) {
                            orgThumbnail = orgWithThumbnail.thumbnail;
                            // Store organization thumbnail in cookie for persistence
                            setCookie("orgThumbnail", orgThumbnail, 2);
                        }
                    }

                    dispatch({
                        type: "AUTH_VALIDATE",
                        payload: {
                            Auth: user,
                            orgThumbnail,
                            ...response
                        }
                    })
                })
            } else {
                window.location = "/login"
                dispatch({
                    type: "AUTH_VALIDATE", payload: {
                        Auth: false,
                    }
                })
            }
        });
        Fire.auth().onIdTokenChanged(function (user) {
            setCookie("accessToken", user.multiFactor.user.accessToken, 2)
        })
    }
}

export function SetOnlineStatus(status) {
    return dispatch => {
        dispatch({
            type: "ONLINE_STATUS", payload: status
        })
    }
}
export function Signout() {
    return dispatch => {

        Fire.auth().signOut().then(e => {
            dispatch({
                type: "AUTH_VALIDATE", payload: false
            })

            dispatch({
                type: "PLAN_VALIDATE", payload: false
            })
            window.location = "/login"
        }).catch(function (error) {
            alert(error.message);
        });

    }
}


export function Set_ProfilePic(payload) {
    return dispatch => {
        dispatch({ type: "SET_PROFILE_DETAILS", payload })
    }
}

// function Get_Payment(uid){
// return new Promise ((resolve, reject)=>{
// Fire.database().ref("users/"+uid+"/salestool_payment").on("value",Payment=>{
//     Fire.database().ref("users/"+uid+"/userAdmin").once("value",Admin=>{
// resolve({Payment:Payment.val(),Admin:Admin.val()})
//     })
// })
// })
// }

// function Get_Profile(uid){
//     return new Promise ((resolve, reject)=>{
//         Fire.database().ref("users/"+uid+"/username").once("value",username=>{
//             Fire.database().ref("users/"+uid+"/profile_pic").once("value",profile_pic=>{
//                     resolve({username:username.val(),profile_pic:profile_pic.val()||undefined})
//     })
//         })
//         })
// }

