import React from "react"
export class Modalexception extends React.Component{



render(){
  return(<div className="bg-[black]/30 bg-opacity-40 backdrop-blur fixed z-[9000] hidden overflow-hidden inset-0 p-4 h-full w-full" id="admit_modal" tabIndex={-1} role="dialog" style={{display: 'block'}}>
  <div className="px-3 sm:mx-auto flex justify-center items-center sm:py-7 h-full w-full" role="document">
    <div className=" max-w-full p-4 sm:max-w-lg h-fit flex flex-col w-full pointer-events-auto bg-transparent backdrop-blur-3xl m-auto rounded inset-0 sm:shadow">
      <div className="flex items-start justify-between  rounded-t-[0.3rem] mb-3">
        <h5  className="text-lg font-[bold] not-italic tracking-[normal] text-white mb-0 font-sans mt-0">{this.props.title}</h5>
      </div>
      <div className="relative flex-auto">
      <p class="text-base mt-0 mb-3 mx-0 text-white">{this.props.message}</p>
      </div>
      <div  className="block border-t-[none]">
      <center className="flex justify-end mt-2 sm:mt-3">
        {this.props.onclick!==undefined?<button onClick={this.props.onclick} style={{marginLeft: "20px"}} type="button" className="w-40 h-10 lg:h-11  rounded bg-[#36f] hover:bg-[#4572fc] border-0 m-0 px-3 text-sm  text-white font-semibold leading-6">Close</button>:<></>}
        </center>
      </div>
    </div>

  </div>
  </div>)
}
}

export class Stickyexception extends React.Component{

    render(){
    return(<>
        <div className="banner" style={{display:'flex',backgroundColor: "#ff4070",height: "60px" ,padding: "18px",width:'100%',zIndex:'1',position:"relative"}}>
              <div style={{flex: "auto"}}>
                <span style={{fontSize: "16px",color: "#fff"}}>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width={21} height={21}>
                    <g data-name="Layer 2">
                    <g data-name="info" fill='#fff'>
                      <rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"></rect>
                      <path d="M12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2zm0 18a8 8 0 1 1 8-8 8 8 0 0 1-8 8z"></path>
                      <circle cx="12" cy="8" r="1"></circle>
                      <path d="M12 10a1 1 0 0 0-1 1v5a1 1 0 0 0 2 0v-5a1 1 0 0 0-1-1z"></path>
                    </g>
                  </g>
                </svg>
                  <span style={{marginLeft:"8px"}}>{this.props.message}</span></span></div>
              <div onClick={this.props.onclick} style={{paddingRight: "10px",cursor:"pointer"}}>
                <span aria-hidden="true">
                    <svg width="24" height="24" viewBox="0 0 24 24">
                    <defs>
                        <path id="prefix__close" d="M7.414 6l4.293-4.293c.391-.391.391-1.023 0-1.414-.39-.391-1.023-.391-1.414 0L6 4.586 1.707.293C1.317-.098.684-.098.293.293c-.39.391-.39 1.023 0 1.414L4.586 6 .293 10.293c-.39.391-.39 1.023 0 1.414.195.195.451.293.707.293.256 0 .512-.098.707-.293L6 7.414l4.293 4.293c.195.195.451.293.707.293.256 0 .512-.098.707-.293.391-.391.391-1.023 0-1.414L7.414 6z"></path>
                    </defs>
                    <g fill="none" fillrule="evenodd" transform="translate(6 6)">
                        <use fill="#fff" href="#prefix__close"></use>
                    </g>
                </svg></span></div>
                </div>

                </>)
    }
}